import { cookies } from 'next/headers';
import { v4 as uuidv4 } from 'uuid';

const CSRF_TOKEN_COOKIE = 'csrf_token';
const CSRF_TOKEN_HEADER = 'X-CSRF-Token';
const CSRF_COOKIE_OPTIONS = {
  httpOnly: false, // Allow client-side access for CSRF token
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  path: '/',
  maxAge: 60 * 60, // 1 hour in seconds
};

/**
 * Generates a new CSRF token and sets it as a cookie
 */
export async function generateCsrfToken(): Promise<string> {
  const csrfToken = uuidv4();
  
  // Set the token as a cookie
  const cookieStore = await cookies();
  cookieStore.set({
    name: CSRF_TOKEN_COOKIE,
    value: csrfToken,
    ...CSRF_COOKIE_OPTIONS
  });
  
  return csrfToken;
}

/**
 * Validates a CSRF token against the token stored in cookies
 */
export async function validateCsrfToken(token: string): Promise<boolean> {
  const cookieStore = await cookies();
  const storedToken = cookieStore.get(CSRF_TOKEN_COOKIE)?.value;
  
  if (!storedToken || !token) {
    return false;
  }
  
  return storedToken === token;
}

/**
 * Rotates the CSRF token by generating a new one and replacing the old one
 */
export async function rotateCsrfToken(): Promise<string> {
  return await generateCsrfToken();
}

/**
 * Clears the CSRF token cookie
 */
export async function clearCsrfToken(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.set({
    name: CSRF_TOKEN_COOKIE,
    value: '',
    ...CSRF_COOKIE_OPTIONS,
    maxAge: 0
  });
}

/**
 * CSRF configuration constants
 */
export const CSRF = {
  cookieName: CSRF_TOKEN_COOKIE,
  headerName: CSRF_TOKEN_HEADER,
  formFieldName: 'csrfToken',
};