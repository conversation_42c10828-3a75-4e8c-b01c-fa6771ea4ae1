// app/api/imagekit-auth/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getImageKitAuthParams } from '@/lib/imagekit';

export async function GET(_request: NextRequest) {
  try {
    // Generate authentication parameters for client-side ImageKit usage
    const authParams = getImageKitAuthParams();
    
    return NextResponse.json({
      success: true,
      ...authParams
    });
  } catch (error) {
    console.error('ImageKit auth error:', error);
    return NextResponse.json(
      { success: false, message: 'Gagal mendapatkan authentication parameters' },
      { status: 500 }
    );
  }
}
