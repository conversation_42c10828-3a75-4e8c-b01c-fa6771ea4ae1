import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { handleApiError } from '@/utils/apiErrorHandler';

// POST /api/forms/[formId]/submit - Submit a form response
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ formId: string }> }
) {
  try {
    // Await params to get formId
    const { formId } = await params;
    
    // Get current user (if authenticated)
    const currentUser = await getCurrentUser();

    // Check if form exists and is published
    const form = await prisma.form.findUnique({
      where: {
        id: formId,
        isPublished: true,
        isDeleted: false,
      },
      include: {
        questions: { orderBy: { order: 'asc' } },
      },
    });

    if (!form) {
      return NextResponse.json(
        { message: 'Form not found or not published' },
        { status: 404 }
      );
    }

    const { respondentInfo, answers } = await req.json();

    // Validate required fields
    if (!answers) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Pastikan respondentInfo adalah objek, bukan null
    const safeRespondentInfo = respondentInfo || {};

    // Buat submission baru
    const submission = await prisma.formSubmission.create({
      data: {
        formId: formId,
        respondentInfo: safeRespondentInfo,
        respondentId: currentUser?.id || null,
      },
    });

    // Buat array untuk menyimpan data jawaban
    const answersData = [];

    // Proses setiap jawaban
    for (const [questionId, value] of Object.entries(answers)) {
      // Tentukan nilai fileUrl jika ada
      let fileUrl: string | null = null;
      let answerValue: string | null = null;
      let answerJson: string | undefined = undefined;
      
      if (typeof value === 'string') {
        answerValue = value;
      } else if (value === null) {
        // Jika nilai null, biarkan kedua field tetap null/undefined
      } else if (typeof value === 'object') {
        // Jika nilai adalah objek
        if ('fileUrl' in value && typeof value.fileUrl === 'string') {
          fileUrl = value.fileUrl;
        }
        // Konversi objek ke JSON string
        answerJson = JSON.stringify(value);
      }
      
      // Tambahkan jawaban ke array
      answersData.push({
        submissionId: submission.id,
        questionId,
        answerValue,
        answerJson,
        fileUrl,
      });
    }

    // Buat jawaban secara batch
    if (answersData.length > 0) {
      await prisma.answer.createMany({
        data: answersData,
      });
    }

    return NextResponse.json(
      { message: 'Form submitted successfully', submissionId: submission.id },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error submitting form:', error);
    return handleApiError(error);
  }
}
