@echo off
echo.
echo 🚀 Running Performance Audit for Zoom_Rutin...
echo.

echo 📦 Checking bundle size...
if exist ".next\analyze" (
    echo ✅ Bundle analysis available
    start /b explorer ".next\analyze"
) else (
    echo ⚠️  Bundle analysis not found. Run: npm run build:analyze
)

echo.
echo 🔍 Checking TypeScript compilation...
call npx tsc --noEmit
if %errorlevel% equ 0 (
    echo ✅ TypeScript compilation successful
) else (
    echo ❌ TypeScript compilation issues found
)

echo.
echo 🧹 Running ESLint...
call npm run lint
if %errorlevel% equ 0 (
    echo ✅ ESLint check passed
) else (
    echo ⚠️  ESLint issues found
)

echo.
echo 🔒 Security audit...
call npm audit --audit-level moderate
if %errorlevel% equ 0 (
    echo ✅ Security audit passed
) else (
    echo ⚠️  Security vulnerabilities found
)

echo.
echo 📋 Checking for unused dependencies...
where depcheck >nul 2>nul
if %errorlevel% equ 0 (
    call npx depcheck
) else (
    echo Install depcheck to analyze unused dependencies: npm install -g depcheck
)

echo.
echo 🎉 Performance audit completed!
echo.
echo 📊 Summary recommendations:
echo 1. Review bundle analyzer results
echo 2. Address any TypeScript/ESLint issues  
echo 3. Update vulnerable dependencies
echo 4. Remove unused dependencies
echo 5. Check bundle size improvements

pause
