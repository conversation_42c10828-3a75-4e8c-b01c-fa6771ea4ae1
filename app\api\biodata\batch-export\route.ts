// File: app/api/biodata/batch-export/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import PDFDocument from 'pdfkit';

// Define the biodata type based on Prisma's generated types
import type { PrismaClient } from '@prisma/client';
type biodata = Awaited<ReturnType<PrismaClient['biodata']['findUnique']>>;

// Tipe untuk Biodata dengan relasi Pelatihan
type BiodataWithPelatihan = biodata & {
  pelatihan: {
    id: string;
    nama: string;
    tempat: string;
    jenjang: string | null;
    // Add other pelatihan fields as needed
  }
};

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { pelatihanId } = body;
    
    // Buat query untuk biodata
    const biodataQuery = {
      include: {
        pelatihan: true,
      },
      orderBy: {
        nama: 'asc' as const,
      },
      where: {},
    };
    
    // Filter berdasarkan pelatihan jika disediakan
    if (pelatihanId) {
      biodataQuery.where = {
        ...biodataQuery.where,
        pelatihanId: pelatihanId
      };
    }
    
    // Query database
    const biodataList = await prisma.biodata.findMany(biodataQuery);
    
    if (biodataList.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data biodata yang ditemukan' },
        { status: 404 }
      );
    }
    
    // Buat buffer untuk PDF
    const pdfBuffer = await generatePDF(biodataList[0]);
    
    // Set response header
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Disposition', 'attachment; filename=Biodata_Peserta.pdf');
    
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers,
    });
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghasilkan PDF' },
      { status: 500 }
    );
  }
}

import { formatIndonesiaDate } from '../../../../utils/dateUtils';

// Fungsi untuk memformat tanggal ke format Indonesia dengan penyesuaian zona waktu
function formatTanggal(date: Date): string {
  return formatIndonesiaDate(date);
}

async function generatePDF(
  biodataItem: BiodataWithPelatihan
): Promise<Buffer> {
  return new Promise<Buffer>((resolve, reject) => {
    try {
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        bufferPages: true,
        info: {
          Title: 'Biodata Peserta',
          Author: 'BPMP KALIMANTAN TIMUR',
          Subject: 'Biodata Peserta Pelatihan',
          Keywords: 'biodata, peserta, pelatihan',
        }
      });
      
      // Collect PDF data into buffer
      const chunks: Buffer[] = [];
      doc.on('data', (chunk: Buffer) => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
      
      // Tambahkan logo dan header
      // Untuk logo, gunakan placeholder kotak
      doc.rect(50, 50, 70, 70).stroke();
      
      // Header text - Kementerian Pendidikan
      doc.font('Helvetica-Bold').fontSize(12);
      doc.text('KEMENTERIAN PENDIDIKAN DASAR DAN MENENGAH', 150, 50, {
        align: 'center'
      });
      
      doc.fontSize(10);
      doc.text('BALAI PENJAMINAN MUTU PENDIDIKAN', 150, 65, {
        align: 'center'
      });
      
      doc.text('PROVINSI KALIMANTAN TIMUR', 150, 78, {
        align: 'center'
      });
      
      doc.font('Helvetica').fontSize(9);
      doc.text('Jalan Cipto Mangunkusumo Km 2, Samarinda Seberang, Samarinda 75132', 150, 92, {
        align: 'center'
      });
      
      doc.text('Telepon (0541) 260304, Faksimile (0541) 262059', 150, 105, {
        align: 'center'
      });
      
      doc.text('Laman www.bpmpkaltim.kemdikbud.go.id, pos-el <EMAIL>', 150, 118, {
        align: 'center'
      });
      
      // Garis pemisah
      doc.moveTo(50, 140).lineTo(545, 140).stroke();
      
      // Judul
      doc.font('Helvetica-Bold').fontSize(14);
      doc.text('BIODATA PESERTA', 300, 160, {
        align: 'center'
      });
      
      // Data Biodata
      doc.font('Helvetica').fontSize(10);
      
      const startY = 200;
      const labelX = 130;
      const valueX = 250;
      const lineHeight = 25;
      
      // Format field-field biodata
      doc.text('Nama Lengkap', labelX, startY);
      doc.text(':', valueX - 10, startY);
      doc.text(biodataItem.nama, valueX, startY);
      
      doc.text('Tempat/Tanggal Lahir', labelX, startY + lineHeight * 1);
      doc.text(':', valueX - 10, startY + lineHeight * 1);
      doc.text(`${biodataItem.tempat_lahir}, ${formatTanggal(new Date(biodataItem.tanggal_lahir))}`, valueX, startY + lineHeight * 1);
      
      doc.text('Pendidikan', labelX, startY + lineHeight * 2);
      doc.text(':', valueX - 10, startY + lineHeight * 2);
      doc.text(biodataItem.pendidikan, valueX, startY + lineHeight * 2);
      
      doc.text('JenisKelamin', labelX, startY + lineHeight * 3);
      doc.text(':', valueX - 10, startY + lineHeight * 3);
      doc.text(biodataItem.jenis_kelamin, valueX, startY + lineHeight * 3);
      
      doc.text('NIP', labelX, startY + lineHeight * 4);
      doc.text(':', valueX - 10, startY + lineHeight * 4);
      doc.text(biodataItem.nip || '-', valueX, startY + lineHeight * 4);
      
      doc.text('Pangkat/Golongan', labelX, startY + lineHeight * 5);
      doc.text(':', valueX - 10, startY + lineHeight * 5);
      doc.text(biodataItem.pangkat_golongan || '-', valueX, startY + lineHeight * 5);
      
      doc.text('Jabatan', labelX, startY + lineHeight * 6);
      doc.text(':', valueX - 10, startY + lineHeight * 6);
      doc.text(biodataItem.jabatan, valueX, startY + lineHeight * 6);
      
      doc.text('Unit Kerja / Asal Sekolah', labelX, startY + lineHeight * 7);
      doc.text(':', valueX - 10, startY + lineHeight * 7);
      doc.text(biodataItem.unit_kerja, valueX, startY + lineHeight * 7);
      
      doc.text('Alamat Unit Kerja / Sekolah', labelX, startY + lineHeight * 8);
      doc.text(':', valueX - 10, startY + lineHeight * 8);
      doc.text(biodataItem.alamat_unit_kerja, valueX, startY + lineHeight * 8);
      
      doc.text('NPWP', labelX, startY + lineHeight * 9);
      doc.text(':', valueX - 10, startY + lineHeight * 9);
      doc.text(biodataItem.npwp || '-', valueX, startY + lineHeight * 9);
      
      doc.text('Email', labelX, startY + lineHeight * 10);
      doc.text(':', valueX - 10, startY + lineHeight * 10);
      doc.text(biodataItem.email, valueX, startY + lineHeight * 10);
      
      doc.text('No. HP/WA', labelX, startY + lineHeight * 11);
      doc.text(':', valueX - 10, startY + lineHeight * 11);
      doc.text(biodataItem.no_hp, valueX, startY + lineHeight * 11);
        // Tambahkan tanda tangan jika ada
      const signatureY = startY + lineHeight * 13;
        // Tanggal dan lokasi (kota, tanggal) - align right to match main export
      const today = new Date();
      const formattedDate = formatTanggal(today);
      doc.text(`${biodataItem.kota}, ${formattedDate}`, 300, signatureY, { 
        align: 'right',
        width: 200
      });
      
      // Area tanda tangan - posisi ke bawah sedikit dari tanggal
      const ttdStartY = signatureY + 30;
        if (biodataItem.tanda_tangan) {
        try {
          // Pastikan base64 dimulai dengan string yang benar
          let base64Data = biodataItem.tanda_tangan;
          if (!base64Data.startsWith('data:image')) {
            base64Data = `data:image/png;base64,${base64Data}`;
          }
            // Posisikan tanda tangan - aligned with right-aligned text layout
          const signatureAreaX = 300;
          const signatureAreaWidth = 200;
          const signatureX = signatureAreaX + signatureAreaWidth - 150; // Right-align the signature within the area
          const ttdY = ttdStartY + 15;
          
          doc.image(base64Data, signatureX, ttdY, {
            fit: [150, 40],
            align: 'center',
            valign: 'center'
          });
          
        } catch (error) {
          console.error('Error adding signature:', error);
        }
      }
        // Tambahkan nama peserta di atas garis - aligned with Samarinda text area (right-aligned)
      doc.text(biodataItem.nama, 300, ttdStartY + 55, {
        width: 200,
        align: 'right'
      });
      
      // Tambahkan garis untuk tanda tangan - positioned to align with right-aligned text
      const lineStartX = 300 + 80; // Start line to align with typical right-aligned text position
      const lineEndX = 300 + 200; // End line at the right edge of the area
      doc.moveTo(lineStartX, ttdStartY + 70).lineTo(lineEndX, ttdStartY + 70).stroke();
      
      // Tambahkan text NIP di bawah tanda tangan - aligned with Samarinda text area (right-aligned)
      doc.text(`NIP. ${biodataItem.nip || '-'}`, 300, ttdStartY + 80, {
        width: 200,
        align: 'right'
      });
      
      // Tambahkan footer dengan logo kecil
      const footerY = 740;
      
      // Logo kiri
      doc.circle(80, footerY, 20).stroke();
      
      // Logo kanan
      doc.circle(520, footerY, 20).stroke();
      
      // Text footer
      doc.fontSize(8);
      doc.text('BPMP PROVINSI KALIMANTAN TIMUR', 300, footerY - 10, { align: 'center' });
      doc.text('MENUJU ZONA INTEGRITAS WILAYAH BEBAS DARI KORUPSI', 300, footerY + 5, { align: 'center' });
      
      // Finalize PDF
      doc.end();
      
    } catch (error) {
      reject(error);
    }
  });
}
