import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { Prisma } from '@prisma/client';
// import { logger } from './logger';

/**
 * Check if the current user has write permission
 * @param user The authenticated user object
 * @returns NextResponse with error or null if the user has permission
 */
export function checkWritePermission(user: { role: string } | null) {
  if (!user) {
    return NextResponse.json(
      { error: 'Tidak terautentikasi' },
      { status: 401 }
    );
  }
  
  if (!hasWritePermission(user.role)) {
    return NextResponse.json(
      { error: 'Tidak memiliki izin untuk membuat atau mengubah data' },
      { status: 403 }
    );
  }
  
  return null;
}

/**
 * Handle validation errors from Zod or other validation libraries
 * @param error The error object from validation
 * @returns NextResponse with formatted error message
 */
export function handleApiValidationError(error: unknown) {
  if (error instanceof ZodError) {
    // Format Zod validation errors
    const formattedErrors = error.errors.map(err => ({
      path: err.path.join('.'),
      message: err.message
    }));
    
    return NextResponse.json(
      { 
        error: 'Validasi gagal', 
        validationErrors: formattedErrors 
      },
      { status: 400 }
    );
  }
  
  // Handle other types of validation errors
  const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan validasi';
  
  return NextResponse.json(
    { error: errorMessage },
    { status: 400 }
  );
}

/**
 * Handle API errors in a consistent way
 * @param error The error object
 * @param path API path where the error occurred
 * @param method HTTP method that was used
 * @returns NextResponse with formatted error message
 */
export function handleApiError(error: unknown, _path?: string, _method?: string) {
  // Logging dinonaktifkan untuk menghindari error
  // const errorContext = {
  //   path: _path || 'UNKNOWN',
  //   method: _method || 'UNKNOWN'
  // };
  
  // Log error dinonaktifkan
  // logger.error(
  //   `API Error [${method || 'UNKNOWN'} ${path || 'UNKNOWN'}]:`, 
  //   error instanceof Error ? error : new Error(String(error)),
  //   errorContext
  // );
  
  // Handle specific types of errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Handle Prisma-specific errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Terjadi konflik data. Kemungkinan data dengan nilai yang sama sudah ada.' },
        { status: 409 }
      );
    } else if (error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Data yang diminta tidak ditemukan.' },
        { status: 404 }
      );
    }
  }
  
  // Handle generic errors
  const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan pada server';
  
  return NextResponse.json(
    { error: errorMessage },
    { status: 500 }
  );
}

export function hasWritePermission(role: string): boolean {
  // Define roles that have write permission
  const writePermissionRoles = [
    'ADMIN',
    'GM1',
    'GM2',
    'GM3',
    'GM4',
    'GM5'
  ];
  
  // Ensure case-insensitive comparison and trim any whitespace
  const normalizedRole = role.trim().toUpperCase();
  return writePermissionRoles.map(r => r.toUpperCase()).includes(normalizedRole);
}
