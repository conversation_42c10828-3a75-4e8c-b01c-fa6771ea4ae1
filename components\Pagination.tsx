'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  limit: number;
  searchParams: Record<string, string | undefined>;
  baseUrl: string;
}

/**
 * Komponen paginasi yang dapat digunakan kembali di seluruh aplikasi
 * Mendukung filter yang dipertahankan saat melakukan paginasi
 */
export default function Pagination({
  currentPage,
  totalPages,
  limit,
  searchParams,
  baseUrl
}: PaginationProps) {
  const router = useRouter();

  // Gunakan fungsi ini untuk navigasi saat tombol diklik
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams();
    
    // Tambahkan parameter page dan limit
    params.set('page', newPage.toString());
    params.set('limit', limit.toString());
    
    // Pertahankan semua parameter filter lainnya
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && key !== 'page' && key !== 'limit') {
        params.set(key, value);
      }
    });
    
    // Navigasi ke URL baru
    router.push(`${baseUrl}?${params.toString()}`);
  };

  // Jika tidak ada halaman atau hanya ada satu halaman, jangan tampilkan pagination
  if (!totalPages || totalPages <= 1) return null;

  return (
    <div className="flex justify-center mt-6">
      <nav className="flex items-center space-x-2" aria-label="Pagination">
        {/* Tombol Previous */}
        <button 
          onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage <= 1}
          className={`px-3 py-1 rounded-md ${
            currentPage <= 1 
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          aria-label="Halaman sebelumnya"
        >
          Prev
        </button>
        
        {/* Nomor halaman */}
        {renderPaginationLinks(currentPage, totalPages, limit, searchParams, baseUrl, handlePageChange)}
        
        {/* Tombol Next */}
        <button 
          onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage >= totalPages}
          className={`px-3 py-1 rounded-md ${
            currentPage >= totalPages 
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          aria-label="Halaman berikutnya"
        >
          Next
        </button>
      </nav>
    </div>
  );
}

// Ubah fungsi renderPaginationLinks untuk memeriksa halaman kosong
function renderPaginationLinks(
  currentPage: number, 
  totalPages: number, 
  limit: number, 
  searchParams: Record<string, string | undefined>,
  baseUrl: string,
  onPageChange: (page: number) => void
) {
  // Jika totalPages adalah 0 atau undefined, jangan tampilkan pagination
  if (!totalPages || totalPages <= 0) return null;
  
  const maxPagesToShow = 5;
  let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
  const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
  
  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }
  
  const pageNumbers = [];
  
  if (startPage > 1) {
    pageNumbers.push(1);
    if (startPage > 2) {
      pageNumbers.push('ellipsis-start');
    }
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }
  
  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      pageNumbers.push('ellipsis-end');
    }
    pageNumbers.push(totalPages);
  }
  
  return pageNumbers.map((pageNum, index) => {
    if (pageNum === 'ellipsis-start' || pageNum === 'ellipsis-end') {
      return (
        <span key={`ellipsis-${index}`} className="px-3 py-1" aria-hidden="true">
          ...
        </span>
      );
    }
    
    const isActive = pageNum === currentPage;
    
    return (
      <button
        key={pageNum}
        onClick={() => onPageChange(pageNum as number)}
        className={`px-3 py-1 rounded-md ${
          isActive 
            ? 'bg-blue-600 text-white' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
        aria-current={isActive ? 'page' : undefined}
        aria-label={`Halaman ${pageNum}`}
      >
        {pageNum.toString()}
      </button>
    );
  });
}
