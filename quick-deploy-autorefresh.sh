#!/bin/bash

# Quick Deploy Auto-Refresh Fix
# Script cepat untuk deploy solusi auto-refresh tanpa interaksi manual

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

echo -e "${GREEN}"
echo "================================================="
echo "   QUICK DEPLOY AUTO-REFRESH FIX"
echo "   Deploy Cepat Tanpa Interaksi Manual"
echo "================================================="
echo -e "${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    log_error "Script harus dijalankan sebagai root (gunakan sudo)"
    exit 1
fi

# STEP 1: Quick Detection
log_info "Melakukan deteksi cepat aplikasi..."

# Auto-detect CloudPanel application
APP_DIR=""
DOMAIN_USER=""
DOMAIN_GROUP=""

# Quick scan untuk CloudPanel sites
for home_dir in /home/<USER>/htdocs; do
    if [ -d "$home_dir" ] && [ -f "$home_dir/package.json" ]; then
        if grep -q "next" "$home_dir/package.json" 2>/dev/null; then
            APP_DIR="$home_dir"
            DOMAIN_USER=$(echo "$home_dir" | cut -d'/' -f3)
            DOMAIN_GROUP="$DOMAIN_USER"
            log_success "Found CloudPanel app: $APP_DIR"
            log_info "Domain user: $DOMAIN_USER"
            break
        fi
    fi
done

# Fallback to standard locations
if [ -z "$APP_DIR" ]; then
    for check_dir in "/var/www/html" "/var/www/kegiatan" "/opt/kegiatan" "/home/<USER>/htdocs"; do
        if [ -d "$check_dir" ] && ([ -f "$check_dir/package.json" ] || [ -f "$check_dir/server.js" ]); then
            APP_DIR="$check_dir"
            DOMAIN_USER="www-data"
            DOMAIN_GROUP="www-data"
            log_success "Found standard app: $APP_DIR"
            break
        fi
    done
fi

if [ -z "$APP_DIR" ]; then
    log_error "Tidak dapat menemukan aplikasi Next.js"
    exit 1
fi

# STEP 2: Quick Backup
BACKUP_DIR="/tmp/quick-autorefresh-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup critical configs
cp /etc/nginx/nginx.conf "$BACKUP_DIR/" 2>/dev/null || true
cp -r /etc/nginx/sites-available "$BACKUP_DIR/" 2>/dev/null || true
cp -r /etc/nginx/conf.d "$BACKUP_DIR/" 2>/dev/null || true

log_success "Quick backup completed: $BACKUP_DIR"

# STEP 3: Quick Nginx Configuration
log_action "Applying quick nginx fix..."

# Detect main domain
DOMAIN="kegiatan.bpmpkaltim.id"

# Find or create nginx config
NGINX_CONFIG=""
for config_path in \
    "/etc/nginx/sites-available/$DOMAIN" \
    "/etc/nginx/sites-available/kegiatan-bpmpkaltim-id" \
    "/etc/nginx/conf.d/$DOMAIN.conf" \
    "/etc/nginx/conf.d/kegiatan.conf"; do
    
    if [ -f "$config_path" ]; then
        NGINX_CONFIG="$config_path"
        break
    fi
done

# Create config if not found
if [ -z "$NGINX_CONFIG" ]; then
    if [ -d "/etc/nginx/sites-available" ]; then
        NGINX_CONFIG="/etc/nginx/sites-available/$DOMAIN"
    else
        mkdir -p "/etc/nginx/conf.d"
        NGINX_CONFIG="/etc/nginx/conf.d/$DOMAIN.conf"
    fi
fi

# Create quick nginx config
cat > "$NGINX_CONFIG" << EOF
# Quick Auto-Refresh Fix Configuration
# Generated by: Quick Deploy Script

server {
    listen 80;
    server_name $DOMAIN *.bpmpkaltim.id;

    root $APP_DIR/public;
    index index.html;

    # Quick uploads fix - NO CACHE
    location /uploads/ {
        alias $APP_DIR/public/uploads/;
        
        # Disable all caching
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        
        # Quick file serving
        try_files \$uri \$uri/ =404;
        
        # Image files handling
        location ~* \.(jpg|jpeg|png|gif|webp|svg)$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            try_files \$uri =404;
        }
    }

    # API proxy
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass 1;
        proxy_no_cache 1;
    }

    # Main app proxy
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

log_success "Quick nginx config created"

# STEP 4: Quick Directory Setup
log_action "Setting up upload directories..."

# Create upload structure
mkdir -p "$APP_DIR/public/uploads/absensi/photos"
chown -R "$DOMAIN_USER:$DOMAIN_GROUP" "$APP_DIR/public/uploads"
chmod -R 755 "$APP_DIR/public/uploads"

log_success "Upload directories ready"

# STEP 5: Quick Monitoring Setup
log_action "Setting up quick monitoring..."

# Simple monitoring script
cat > "/usr/local/bin/quick-photo-monitor.sh" << MONITOR_SCRIPT
#!/bin/bash

UPLOAD_DIR="$APP_DIR/public/uploads"
DOMAIN_USER="$DOMAIN_USER"
DOMAIN_GROUP="$DOMAIN_GROUP"
LOG_FILE="/var/log/quick-photo-monitor.log"

log_event() {
    echo "\$(date '+%Y-%m-%d %H:%M:%S') [\$1] \$2" >> "\$LOG_FILE"
}

# Monitor and fix permissions
inotifywait -m -r -q -e create,moved_to,close_write "\$UPLOAD_DIR" --format '%w%f %e' | \\
while read file_path event; do
    if [[ "\$file_path" =~ \\.(jpg|jpeg|png|gif|webp|svg)\$ ]]; then
        log_event "INFO" "New image: \$file_path"
        sleep 0.1
        chown "\$DOMAIN_USER:\$DOMAIN_GROUP" "\$file_path" 2>/dev/null || true
        chmod 644 "\$file_path" 2>/dev/null || true
        log_event "SUCCESS" "Fixed permissions: \$file_path"
    fi
done
MONITOR_SCRIPT

chmod +x "/usr/local/bin/quick-photo-monitor.sh"

# Quick systemd service
cat > "/etc/systemd/system/quick-photo-monitor.service" << EOF
[Unit]
Description=Quick Photo Monitor
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/quick-photo-monitor.sh
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable quick-photo-monitor.service

log_success "Quick monitoring deployed"

# STEP 6: Quick System Optimization
log_action "Applying quick optimizations..."

# Quick inotify optimization
if ! grep -q "fs.inotify.max_user_watches" /etc/sysctl.conf; then
    echo "fs.inotify.max_user_watches=524288" >> /etc/sysctl.conf
    sysctl -p >/dev/null 2>&1
fi

log_success "Quick optimization applied"

# STEP 7: Enable and Test
log_action "Starting services..."

# Test nginx config
if ! nginx -t; then
    log_error "Nginx config error! Restoring backup..."
    cp "$BACKUP_DIR/nginx.conf" /etc/nginx/nginx.conf
    exit 1
fi

# Enable site if needed
if [ -d "/etc/nginx/sites-available" ] && [ -d "/etc/nginx/sites-enabled" ]; then
    ln -sf "$NGINX_CONFIG" "/etc/nginx/sites-enabled/$(basename "$NGINX_CONFIG")" 2>/dev/null || true
fi

# Restart services
systemctl reload nginx
systemctl start quick-photo-monitor.service

# Quick test
sleep 2
NGINX_STATUS=$(systemctl is-active nginx)
MONITOR_STATUS=$(systemctl is-active quick-photo-monitor.service)

echo ""
echo -e "${GREEN}"
echo "================================================="
echo "   QUICK DEPLOY COMPLETED"
echo "================================================="
echo -e "${NC}"

log_success "🎉 QUICK DEPLOYMENT COMPLETE!"
echo ""

echo -e "${CYAN}📊 STATUS:${NC}"
echo "• Nginx: $NGINX_STATUS"
echo "• Monitor: $MONITOR_STATUS"
echo "• App: $APP_DIR"
echo "• Owner: $DOMAIN_USER:$DOMAIN_GROUP"
echo ""

echo -e "${CYAN}🧪 QUICK TEST:${NC}"
echo "1. Upload foto di aplikasi absensi"
echo "2. Langsung cek detail absensi"
echo "3. Foto harus muncul tanpa reload nginx"
echo ""

echo -e "${CYAN}📋 MONITORING:${NC}"
echo "• Status: systemctl status quick-photo-monitor"
echo "• Logs: tail -f /var/log/quick-photo-monitor.log"
echo ""

if [ "$NGINX_STATUS" = "active" ] && [ "$MONITOR_STATUS" = "active" ]; then
    log_success "✅ QUICK FIX BERHASIL - FOTO UPLOAD REAL-TIME!"
else
    log_warning "⚠️ Ada service yang belum aktif, cek status di atas"
fi

echo -e "${GREEN}=================================================${NC}"
