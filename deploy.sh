#!/bin/bash

# NextJS 15 Standalone Structure Copy Script
# Ensures correct folder structure for deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
STANDALONE_DIR=".next/standalone"
STATIC_SOURCE=".next/static"
PUBLIC_SOURCE="public"
ENV_PROD=".env.production"
ENV_LOCAL=".env.local"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_build_exists() {
    log_info "Checking if build exists..."
    
    if [ ! -d "$STANDALONE_DIR" ]; then
        log_error "Standalone build not found at $STANDALONE_DIR"
        log_error "Please run 'npm run build' first with output: 'standalone' in next.config.js"
        exit 1
    fi
    
    if [ ! -f "$STANDALONE_DIR/server.js" ]; then
        log_error "server.js not found in standalone build"
        log_error "Make sure your build completed successfully"
        exit 1
    fi
    
    log_success "Standalone build found"
}

show_current_structure() {
    log_info "Current structure analysis:"
    echo ""
    
    # Check standalone directory
    if [ -d "$STANDALONE_DIR" ]; then
        echo -e "${GREEN}✅ .next/standalone/${NC} - $(du -sh $STANDALONE_DIR | cut -f1)"
        
        # Check server.js
        if [ -f "$STANDALONE_DIR/server.js" ]; then
            echo -e "   ${GREEN}✅ server.js${NC}"
        else
            echo -e "   ${RED}❌ server.js${NC}"
        fi
        
        # Check package.json
        if [ -f "$STANDALONE_DIR/package.json" ]; then
            echo -e "   ${GREEN}✅ package.json${NC}"
        else
            echo -e "   ${RED}❌ package.json${NC}"
        fi
        
        # Check .next directory
        if [ -d "$STANDALONE_DIR/.next" ]; then
            echo -e "   ${GREEN}✅ .next/${NC}"
        else
            echo -e "   ${RED}❌ .next/${NC}"
        fi
        
        # Check node_modules
        if [ -d "$STANDALONE_DIR/node_modules" ]; then
            echo -e "   ${GREEN}✅ node_modules/${NC}"
        else
            echo -e "   ${YELLOW}⚠️  node_modules/${NC} (will be created if needed)"
        fi
    else
        echo -e "${RED}❌ .next/standalone/${NC}"
    fi
    
    # Check static directory
    if [ -d "$STATIC_SOURCE" ]; then
        echo -e "${BLUE}📁 .next/static/${NC} - $(du -sh $STATIC_SOURCE | cut -f1)"
        if [ -d "$STANDALONE_DIR/.next/static" ]; then
            echo -e "   ${GREEN}✅ Already copied to standalone${NC}"
        else
            echo -e "   ${YELLOW}⚠️  Needs to be copied${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  .next/static/${NC} - Not found"
    fi
    
    # Check public directory
    if [ -d "$PUBLIC_SOURCE" ]; then
        echo -e "${BLUE}📁 public/${NC} - $(du -sh $PUBLIC_SOURCE | cut -f1)"
        if [ -d "$STANDALONE_DIR/public" ]; then
            echo -e "   ${GREEN}✅ Already copied to standalone${NC}"
        else
            echo -e "   ${YELLOW}⚠️  Needs to be copied${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  public/${NC} - Not found"
    fi
    
    # Check environment files
    if [ -f "$ENV_PROD" ]; then
        echo -e "${BLUE}📄 .env.production${NC}"
        if [ -f "$STANDALONE_DIR/.env" ]; then
            echo -e "   ${GREEN}✅ Already copied to standalone${NC}"
        else
            echo -e "   ${YELLOW}⚠️  Needs to be copied${NC}"
        fi
    elif [ -f "$ENV_LOCAL" ]; then
        echo -e "${BLUE}📄 .env.local${NC}"
        if [ -f "$STANDALONE_DIR/.env" ]; then
            echo -e "   ${GREEN}✅ Already copied to standalone${NC}"
        else
            echo -e "   ${YELLOW}⚠️  Needs to be copied${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  No environment files found${NC}"
    fi
    
    echo ""
}

copy_static_files() {
    log_info "Copying static files..."
    
    if [ -d "$STATIC_SOURCE" ]; then
        # Create target directory if it doesn't exist
        mkdir -p "$STANDALONE_DIR/.next"
        
        # Copy static files
        cp -r "$STATIC_SOURCE" "$STANDALONE_DIR/.next/static"
        
        # Get size
        STATIC_SIZE=$(du -sh "$STANDALONE_DIR/.next/static" | cut -f1)
        log_success "Static files copied successfully ($STATIC_SIZE)"
        
        # Show what was copied
        echo "   Files copied:"
        find "$STANDALONE_DIR/.next/static" -type f | head -10 | while read file; do
            echo "   - $(basename "$file")"
        done
        
        TOTAL_STATIC=$(find "$STANDALONE_DIR/.next/static" -type f | wc -l)
        if [ "$TOTAL_STATIC" -gt 10 ]; then
            echo "   ... and $((TOTAL_STATIC - 10)) more files"
        fi
    else
        log_warning "No static files found to copy"
    fi
}

copy_public_files() {
    log_info "Copying public files..."
    
    if [ -d "$PUBLIC_SOURCE" ]; then
        # Remove existing public directory in standalone to avoid conflicts
        if [ -d "$STANDALONE_DIR/public" ]; then
            rm -rf "$STANDALONE_DIR/public"
        fi
        
        # Copy public files
        cp -r "$PUBLIC_SOURCE" "$STANDALONE_DIR/public"
        
        # Get size
        PUBLIC_SIZE=$(du -sh "$STANDALONE_DIR/public" | cut -f1)
        log_success "Public files copied successfully ($PUBLIC_SIZE)"
        
        # Show what was copied
        echo "   Files copied:"
        find "$STANDALONE_DIR/public" -type f | head -10 | while read file; do
            echo "   - $(basename "$file")"
        done
        
        TOTAL_PUBLIC=$(find "$STANDALONE_DIR/public" -type f | wc -l)
        if [ "$TOTAL_PUBLIC" -gt 10 ]; then
            echo "   ... and $((TOTAL_PUBLIC - 10)) more files"
        fi
        
        # Create uploads directory structure if it doesn't exist
        mkdir -p "$STANDALONE_DIR/public/uploads/absensi/photos"
        mkdir -p "$STANDALONE_DIR/public/uploads/biodata/photos"
        mkdir -p "$STANDALONE_DIR/public/uploads/materi"
        
        log_info "Upload directories structure created"
    else
        log_warning "No public files found to copy"
    fi
}

copy_environment_files() {
    log_info "Copying environment files..."
    
    if [ -f "$ENV_PROD" ]; then
        cp "$ENV_PROD" "$STANDALONE_DIR/.env"
        log_success "Production environment file copied"
    elif [ -f "$ENV_LOCAL" ]; then
        cp "$ENV_LOCAL" "$STANDALONE_DIR/.env"
        log_success "Local environment file copied"
    else
        log_warning "No environment files found"
        
        # Create basic .env template
        cat > "$STANDALONE_DIR/.env" << 'EOF'
# Production Environment Variables
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0

# Add your environment variables here
# DATABASE_URL=
# NEXTAUTH_URL=
# NEXTAUTH_SECRET=
EOF
        log_info "Basic .env template created"
    fi
}

verify_structure() {
    log_info "Verifying final structure..."
    
    ERRORS=0
    
    # Check critical files
    if [ ! -f "$STANDALONE_DIR/server.js" ]; then
        log_error "server.js missing"
        ERRORS=$((ERRORS + 1))
    fi
    
    if [ ! -f "$STANDALONE_DIR/package.json" ]; then
        log_error "package.json missing"
        ERRORS=$((ERRORS + 1))
    fi
    
    if [ ! -d "$STANDALONE_DIR/.next" ]; then
        log_error ".next directory missing"
        ERRORS=$((ERRORS + 1))
    fi
    
    # Check copied files
    if [ -d "$STATIC_SOURCE" ] && [ ! -d "$STANDALONE_DIR/.next/static" ]; then
        log_error "Static files not copied properly"
        ERRORS=$((ERRORS + 1))
    fi
    
    if [ -d "$PUBLIC_SOURCE" ] && [ ! -d "$STANDALONE_DIR/public" ]; then
        log_error "Public files not copied properly"
        ERRORS=$((ERRORS + 1))
    fi
    
    if [ $ERRORS -eq 0 ]; then
        log_success "Structure verification passed!"
    else
        log_error "Structure verification failed with $ERRORS errors"
        exit 1
    fi
}

show_final_structure() {
    log_info "Final standalone structure:"
    echo ""
    
    cd "$STANDALONE_DIR"
    
    # Show directory tree
    if command -v tree &> /dev/null; then
        tree -L 3 -a
    else
        find . -type d | head -20 | sed 's|[^/]*/|  |g'
    fi
    
    cd - > /dev/null
    
    echo ""
    log_info "Total size: $(du -sh $STANDALONE_DIR | cut -f1)"
}

create_deployment_info() {
    log_info "Creating deployment info file..."
    
    cat > "$STANDALONE_DIR/DEPLOYMENT_INFO.md" << EOF
# Deployment Information

## Build Information
- Build Date: $(date)
- Node Version: $(node --version)
- NPM Version: $(npm --version)
- NextJS Version: $(npm list next --depth=0 2>/dev/null | grep next || echo "Unknown")

## Structure
\`\`\`
$(cd "$STANDALONE_DIR" && find . -type f | head -20)
\`\`\`

## Files Count
- Total Files: $(find "$STANDALONE_DIR" -type f | wc -l)
- Static Files: $([ -d "$STANDALONE_DIR/.next/static" ] && find "$STANDALONE_DIR/.next/static" -type f | wc -l || echo "0")
- Public Files: $([ -d "$STANDALONE_DIR/public" ] && find "$STANDALONE_DIR/public" -type f | wc -l || echo "0")

## Size Information
- Total Size: $(du -sh "$STANDALONE_DIR" | cut -f1)
- Static Size: $([ -d "$STANDALONE_DIR/.next/static" ] && du -sh "$STANDALONE_DIR/.next/static" | cut -f1 || echo "0B")
- Public Size: $([ -d "$STANDALONE_DIR/public" ] && du -sh "$STANDALONE_DIR/public" | cut -f1 || echo "0B")

## Start Command
\`\`\`bash
node server.js
\`\`\`

## Environment Variables
$([ -f "$STANDALONE_DIR/.env" ] && echo "Environment file exists" || echo "No environment file")

## Upload Directory Structure
- Absensi Photos: public/uploads/absensi/photos/
- Biodata Photos: public/uploads/biodata/photos/
- Training Materials: public/uploads/materi/

## Important Notes for VPS Deployment
1. Make sure nginx serves static files from the correct public directory
2. Ensure upload directories have proper permissions (755)
3. Configure nginx to handle file uploads up to 50MB
4. Set proper SSL certificates for HTTPS
EOF

    log_success "Deployment info created"
}

create_nginx_config() {
    log_info "Creating nginx configuration template..."
    
    cat > "$STANDALONE_DIR/nginx.conf.template" << 'EOF'
# Nginx configuration for Next.js standalone deployment
# Copy this to /etc/nginx/sites-available/your-domain.conf

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL configuration
    ssl_certificate /path/to/your/ssl/certificate.crt;
    ssl_certificate_key /path/to/your/ssl/private.key;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Upload file size limit
    client_max_body_size 50M;
    
    # Root directory for static files
    root /path/to/your/deployment/.next/standalone/public;
    
    # Serve uploaded files directly
    location /uploads/ {
        try_files $uri $uri/ =404;
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Serve Next.js static files
    location /_next/static/ {
        try_files $uri $uri/ =404;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
    
    # Serve favicon and other static assets
    location ~* \.(ico|css|js|gif|jpe?g|png|svg|woff|woff2|ttf|eot)$ {
        try_files $uri $uri/ =404;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
    
    # Proxy all other requests to Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}
EOF

    log_success "Nginx configuration template created at nginx.conf.template"
}

create_vps_deployment_guide() {
    log_info "Creating VPS deployment guide..."
    
    cat > "$STANDALONE_DIR/VPS_DEPLOYMENT_GUIDE.md" << 'EOF'
# VPS Deployment Guide for Photo Upload Fix

## Problem Identified
- Photos uploaded to: `/public/public/uploads/absensi/photos/` (duplicate public)
- URLs accessing: `/uploads/absensi/photos/` (missing public)
- Result: 404 File Not Found

## Solution Steps

### 1. Upload your built application
```bash
# On your local machine, create deployment archive
tar -czf deployment.tar.gz -C .next/standalone .

# Upload to your VPS
scp deployment.tar.gz user@your-server:/path/to/deployment/
```

### 2. Extract and setup on VPS
```bash
# On your VPS
cd /path/to/deployment
tar -xzf deployment.tar.gz
```

### 3. Install Node.js (if not already installed)
```bash
# Ubuntu 22.04
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 4. Install PM2 for process management
```bash
sudo npm install -g pm2
```

### 5. Setup nginx configuration
```bash
# Copy the nginx template
sudo cp nginx.conf.template /etc/nginx/sites-available/your-domain.conf

# Edit the configuration
sudo nano /etc/nginx/sites-available/your-domain.conf

# Update these lines:
# - Replace "your-domain.com" with your actual domain
# - Replace "/path/to/your/deployment" with actual path
# - Replace SSL certificate paths

# Enable the site
sudo ln -s /etc/nginx/sites-available/your-domain.conf /etc/nginx/sites-enabled/

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

### 6. Set proper permissions for upload directories
```bash
# Make sure nginx can read the files
sudo chown -R www-data:www-data public/uploads/
sudo chmod -R 755 public/uploads/

# Create upload directories if they don't exist
mkdir -p public/uploads/absensi/photos
mkdir -p public/uploads/biodata/photos
mkdir -p public/uploads/materi
```

### 7. Start your Next.js application
```bash
# Start with PM2
pm2 start server.js --name "nextjs-app"

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
```

### 8. Verify deployment
1. Test photo upload in absensi form
2. Check that files are created in correct location:
   ```bash
   ls -la public/uploads/absensi/photos/[pelatihan-id]/
   ```
3. Verify photos display correctly in detail view

### 9. Monitor logs
```bash
# Next.js application logs
pm2 logs nextjs-app

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## Troubleshooting

### If photos still don't work:
1. Check nginx error logs
2. Verify file permissions
3. Test direct file access: `https://your-domain.com/uploads/test.jpg`
4. Ensure upload directories exist and are writable

### File permission commands:
```bash
# Fix ownership
sudo chown -R www-data:www-data public/uploads/

# Fix permissions
find public/uploads/ -type d -exec chmod 755 {} \;
find public/uploads/ -type f -exec chmod 644 {} \;
```

## Security Notes
- Keep your server updated
- Use strong SSL certificates
- Monitor upload directory sizes
- Consider implementing file cleanup for old uploads
- Use fail2ban to protect against brute force attacks
EOF

    log_success "VPS deployment guide created"
}

main() {
    echo -e "${BLUE}"
    echo "============================================"
    echo "   NextJS 15 Standalone Structure Copier"
    echo "============================================"
    echo -e "${NC}"
    
    check_build_exists
    show_current_structure
    
    echo ""
    read -p "Continue with copying? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled"
        exit 0
    fi
      echo ""
    copy_static_files
    copy_public_files
    copy_environment_files
    verify_structure
    create_deployment_info
    create_nginx_config
    create_vps_deployment_guide
    
    echo ""
    show_final_structure
    
    echo ""
    log_success "Structure copying completed successfully!"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. cd $STANDALONE_DIR"
    echo "2. Test locally: node server.js"
    echo "3. Create deployment archive: tar -czf ../deployment.tar.gz ."
    echo "4. Upload to your server and follow VPS_DEPLOYMENT_GUIDE.md"
    echo ""
    echo -e "${BLUE}Important files created:${NC}"
    echo "- nginx.conf.template (nginx configuration)"
    echo "- VPS_DEPLOYMENT_GUIDE.md (deployment instructions)"
    echo "- DEPLOYMENT_INFO.md (build information)"
    echo ""
}

# Run main function
main "$@"