@echo off
echo ========================================
echo   UPLOAD EMERGENCY FIX ke VPS
echo ========================================
echo.

set VPS_HOST=kegiatan.bpmpkaltim.id
set VPS_USER=root

echo Upload emergency scripts ke VPS...
echo Host: %VPS_HOST%
echo User: %VPS_USER%
echo.

echo [1/3] Upload emergency-fix-photo.sh...
scp emergency-fix-photo.sh %VPS_USER%@%VPS_HOST%:/root/
if %errorlevel% neq 0 (
    echo ERROR: Gagal upload emergency-fix-photo.sh
    echo Coba manual dengan WinSCP atau FileZilla
    pause
    exit /b 1
)

echo [2/3] Upload debug-photo-status.sh...
scp debug-photo-status.sh %VPS_USER%@%VPS_HOST%:/root/
if %errorlevel% neq 0 (
    echo ERROR: Gagal upload debug-photo-status.sh
    echo Coba manual dengan WinSCP atau FileZilla
    pause
    exit /b 1
)

echo [3/3] Upload advanced-diagnosa.sh...
scp advanced-diagnosa.sh %VPS_USER%@%VPS_HOST%:/root/
if %errorlevel% neq 0 (
    echo ERROR: Gagal upload advanced-diagnosa.sh
    echo Coba manual dengan WinSCP atau FileZilla
    pause
    exit /b 1
)

echo.
echo ========================================
echo   UPLOAD BERHASIL!
echo ========================================
echo.
echo LANGKAH SELANJUTNYA:
echo 1. Login ke VPS: ssh %VPS_USER%@%VPS_HOST%
echo 2. Jalankan: chmod +x emergency-fix-photo.sh
echo 3. Jalankan: sudo ./emergency-fix-photo.sh
echo 4. Verifikasi: ./debug-photo-status.sh
echo.
echo Tekan tombol apa saja untuk melanjutkan ke SSH...
pause

echo.
echo Membuka SSH connection...
ssh %VPS_USER%@%VPS_HOST%
