import { PrismaClient, user_role, pelatihan_jenjang } from '@prisma/client';
import { hash } from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Fungsi untuk membuat waktu Indonesia yang tepat untuk database
function createIndonesiaTime(baseDate?: Date): Date {
  const now = baseDate || new Date();
  
  // Dapatkan waktu dalam format Asia/Makassar 
  const witaString = now.toLocaleString("en-US", {
    timeZone: "Asia/Makassar",
    hour12: false,
    year: 'numeric',
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  
  // Parse komponen tanggal dan waktu
  const [datePart, timePart] = witaString.split(', ');
  const [month, day, year] = datePart.split('/');
  const [hour, minute, second] = timePart.split(':');
  
  // Buat Date object dalam UTC yang merepresentasikan waktu WITA
  // WITA = UTC+8, jadi untuk mendapatkan UTC kita kurangi 8 jam
  const utcTime = new Date(Date.UTC(
    parseInt(year),
    parseInt(month) - 1, // Month is 0-indexed
    parseInt(day),
    parseInt(hour) - 8, // Convert WITA to UTC (subtract 8 hours)
    parseInt(minute),
    parseInt(second)
  ));
  
  return utcTime;
}

const prisma = new PrismaClient();

async function main() {
  // Seed Users
  const adminPassword = await hash('admin123', 10);
  const userPassword = await hash('user123', 10);

  const adminUser = await prisma.user.create({
    data: {
      id: uuidv4(),
      name: 'Admin User',
      email: '<EMAIL>',
      password: adminPassword,
      role: user_role.ADMIN, // Use enum instead of string
      updatedAt: new Date()
    }
  });

  // Kepala dan Kasubag users
  await prisma.user.create({
    data: {
      id: uuidv4(),
      name: 'Kepala',
      email: '<EMAIL>',
      password: await hash('kepala123', 10),
      role: user_role.KEPALA, // Use enum instead of string with type assertion
      updatedAt: new Date()
    }
  });

  await prisma.user.create({
    data: {
      id: uuidv4(),
      name: 'Kasubag',
      email: '<EMAIL>',
      password: await hash('kasubag123', 10),
      role: user_role.KASUBAG, // Use enum instead of string with type assertion
      updatedAt: new Date()
    }
  });

  // Prepare user data with correct enum types
  const users = [
    // Gugus Mutu 1
    {
      id: uuidv4(),
      name: 'Gugus Mutu 1 ',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM1, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM1 Member 1',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM1, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM1 Member 2',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM1, // Use enum instead of string
      updatedAt: new Date()
    },
    
    // Gugus Mutu 2
    {
      id: uuidv4(),
      name: 'GM2 Lead',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM2, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM2 Member 1',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM2, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM2 Member 2',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM2, // Use enum instead of string
      updatedAt: new Date()
    },
    
    // Gugus Mutu 3
    {
      id: uuidv4(),
      name: 'GM3 Lead',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM3, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM3 Member 1',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM3, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM3 Member 2',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM3, // Use enum instead of string
      updatedAt: new Date()
    },
    
    // Gugus Mutu 4
    {
      id: uuidv4(),
      name: 'GM4 Lead',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM4, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM4 Member 1',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM4, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM4 Member 2',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM4, // Use enum instead of string
      updatedAt: new Date()
    },
    
    // Gugus Mutu 5
    {
      id: uuidv4(),
      name: 'GM5 Lead',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM5, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM5 Member 1',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM5, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'GM5 Member 2',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.GM5, // Use enum instead of string
      updatedAt: new Date()
    },
    
    // Regular Users
    {
      id: uuidv4(),
      name: 'Regular User 1',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.USER, // Use enum instead of string
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      name: 'Regular User 2',
      email: '<EMAIL>',
      password: userPassword,
      role: user_role.USER, // Use enum instead of string
      updatedAt: new Date()
    }
  ];

  for (const user of users) {
    await prisma.user.create({
      data: user
    });
  }

  console.info(`Created ${users.length + 3} users`);

  // Seed Pelatihan dengan enum yang benar
  const pelatihanData = [
    // GM1 Pelatihan
    {
      id: uuidv4(),
      nama: 'Pelatihan Kurikulum Merdeka PAUD',
      tempat: 'Gedung Diklat Lt. 3',
      tgl_mulai: new Date('2025-04-10'),
      tgl_berakhir: new Date('2025-04-12'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.PAUD, // Use enum instead of string
      target_peserta: 30,
      userId: users[0].id, // GM1 Lead
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      nama: 'Pelatihan Inovasi Pembelajaran PAUD',
      tempat: 'Balai Pelatihan Guru',
      tgl_mulai: new Date('2025-04-18'),
      tgl_berakhir: new Date('2025-04-19'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.PAUD, // Use enum instead of string
      target_peserta: 25,
      userId: users[1].id, // GM1 Member 1
      updatedAt: new Date()
    },
    
    // GM2 Pelatihan
    {
      id: uuidv4(),
      nama: 'Workshop Pengembangan Bahan Ajar SD',
      tempat: 'Hotel Grand Mercure',
      tgl_mulai: new Date('2025-04-15'),
      tgl_berakhir: new Date('2025-04-17'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.SD, // Use enum instead of string
      target_peserta: 40,
      userId: users[3].id, // GM2 Lead
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      nama: 'Pelatihan Digitalisasi Pembelajaran SD',
      tempat: 'Gedung BPSDM',
      tgl_mulai: new Date('2025-04-25'),
      tgl_berakhir: new Date('2025-04-27'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.SD, // Use enum instead of string
      target_peserta: 35,
      userId: users[4].id, // GM2 Member 1
      updatedAt: new Date()
    },
    
    // GM3 Pelatihan
    {
      id: uuidv4(),
      nama: 'Pelatihan Metode Pengajaran SMP',
      tempat: 'Ruang Meeting Utama',
      tgl_mulai: new Date('2025-05-10'),
      tgl_berakhir: new Date('2025-05-13'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.SMP, // Use enum instead of string
      target_peserta: 35,
      userId: users[6].id, // GM3 Lead
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      nama: 'Workshop Assessment Pembelajaran SMP',
      tempat: 'Gedung Diklat Lt. 2',
      tgl_mulai: new Date('2025-05-15'),
      tgl_berakhir: new Date('2025-05-16'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.SMP, // Use enum instead of string
      target_peserta: 30,
      userId: users[7].id, // GM3 Member 1
      updatedAt: new Date()
    },
    
    // GM4 Pelatihan
    {
      id: uuidv4(),
      nama: 'Seminar Pengembangan Karir Guru SMA',
      tempat: 'Auditorium Pusat',
      tgl_mulai: new Date('2025-05-20'),
      tgl_berakhir: new Date('2025-05-21'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.SMA, // Use enum instead of string
      target_peserta: 50,
      userId: users[9].id, // GM4 Lead
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      nama: 'Pelatihan Riset Tindakan Kelas SMA',
      tempat: 'Hotel Santika',
      tgl_mulai: new Date('2025-05-25'),
      tgl_berakhir: new Date('2025-05-28'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.SMA, // Use enum instead of string
      target_peserta: 40,
      userId: users[10].id, // GM4 Member 1
      updatedAt: new Date()
    },
    
    // GM5 Pelatihan
    {
      id: uuidv4(),
      nama: 'Workshop Manajemen Pendidikan LAINNYA',
      tempat: 'Daring via Zoom',
      tgl_mulai: new Date('2025-06-05'),
      tgl_berakhir: new Date('2025-06-07'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.LAINNYA, // Use enum instead of string
      target_peserta: 100,
      userId: users[12].id, // GM5 Lead
      updatedAt: new Date()
    },
    {
      id: uuidv4(),
      nama: 'Pelatihan Kepemimpinan Pendidikan',
      tempat: 'Hotel JW Marriott',
      tgl_mulai: new Date('2025-06-12'),
      tgl_berakhir: new Date('2025-06-14'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.LAINNYA, // Use enum instead of string
      target_peserta: 75,
      userId: users[13].id, // GM5 Member 1
      updatedAt: new Date()
    },
    
    // Admin Pelatihan
    {
      id: uuidv4(),
      nama: 'Pelatihan Administrasi Pendidikan',
      tempat: 'Gedung Rektorat',
      tgl_mulai: new Date('2025-07-01'),
      tgl_berakhir: new Date('2025-07-03'),
      link_registrasi: `reg-${uuidv4().slice(0, 8)}`,
      link_absensi: `abs-${uuidv4().slice(0, 8)}`,
      jenjang: pelatihan_jenjang.LAINNYA, // Use enum instead of string
      target_peserta: 60,
      userId: adminUser.id,
      updatedAt: new Date()
    }
  ];

  const createdPelatihan = [];
  for (const pelatihan of pelatihanData) {
    const created = await prisma.pelatihan.create({
      data: pelatihan
    });
    createdPelatihan.push(created);
    
    // Tambahkan jenjang target untuk setiap pelatihan
    await prisma.pelatihan_jenjang_target.create({
      data: {
        id: uuidv4(),
        pelatihanId: created.id,
        jenjang: created.jenjang,
        target_peserta: created.target_peserta,
        createdAt: new Date()
      }
    });
  }

  console.info(`Created ${createdPelatihan.length} pelatihan`);

  // Bagian lainnya tidak berubah...
  // Seed Biodata dan Absensi seperti sebelumnya
  
  // Lanjutkan dengan code yang sama untuk bagian biodata dan absensi...
  const generateSignatureData = () => {
    // Simple base64 encoded placeholder for signature data
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAyCAYAAACqNX6+AAAA...';
  };

  // Contoh data biodata peserta
  const biodataList = [];
  for (let i = 0; i < createdPelatihan.length; i++) {
    const pelatihan = createdPelatihan[i];
    const participantCount = Math.min(5, pelatihan.target_peserta); // Create up to 5 participants per training
    
    for (let j = 0; j < participantCount; j++) {
      const isNonCivil = Math.random() > 0.7; // 30% chance participant is not a civil servant
      
      biodataList.push({
        id: uuidv4(),
        nama: `Peserta ${j + 1} ${pelatihan.nama}`,
        tempat_lahir: ['Jakarta', 'Bandung', 'Surabaya', 'Yogyakarta', 'Medan'][Math.floor(Math.random() * 5)],
        tanggal_lahir: new Date(1970 + Math.floor(Math.random() * 30), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
        pendidikan: ['S1', 'S2', 'S3', 'D3', 'D4'][Math.floor(Math.random() * 5)],
        jenis_kelamin: Math.random() > 0.5 ? 'Laki-laki' : 'Perempuan',
        nip: isNonCivil ? null : `19${Math.floor(Math.random() * 100)}${Math.floor(Math.random() * 10000000).toString().padStart(8, '0')}`,
        pangkat_golongan: isNonCivil ? null : ['III/a', 'III/b', 'III/c', 'IV/a', 'IV/b'][Math.floor(Math.random() * 5)],
        jabatan: [`Guru ${pelatihan.jenjang}`, 'Kepala Sekolah', 'Wakil Kepala Sekolah', 'Pengawas', 'Staf Dinas'][Math.floor(Math.random() * 5)],
        unit_kerja: `${pelatihan.jenjang} Negeri ${Math.floor(Math.random() * 100) + 1} ${['Jakarta', 'Bandung', 'Surabaya', 'Yogyakarta', 'Medan'][Math.floor(Math.random() * 5)]}`,
        alamat_unit_kerja: `Jl. Pendidikan No. ${Math.floor(Math.random() * 100) + 1}, ${['Jakarta', 'Bandung', 'Surabaya', 'Yogyakarta', 'Medan'][Math.floor(Math.random() * 5)]}`,
        npwp: Math.random() > 0.3 ? `${Math.floor(Math.random() * 100000000000000).toString().padStart(15, '0')}` : null,
        email: `peserta${j + 1}.${pelatihan.jenjang.toLowerCase()}${i}@example.com`,
        no_hp: `08${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`,
        tanda_tangan: generateSignatureData(),
        pelatihanId: pelatihan.id,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
  }

  for (const biodata of biodataList) {
    await prisma.biodata.create({
      data: biodata
    });
  }

  console.info(`Created ${biodataList.length} biodata entries`);

  // Seed Absensi
  const absensiList = [];
  for (let i = 0; i < createdPelatihan.length; i++) {
    const pelatihan = createdPelatihan[i];
    // Create attendance for each day of the training
    const durationDays = Math.ceil((pelatihan.tgl_berakhir.getTime() - pelatihan.tgl_mulai.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    
    // Generate 40 participants for each training
    const participantCount = 40;
    const participants = [];
    
    for (let p = 0; p < participantCount; p++) {
      participants.push({
        nama: `Peserta ${p + 1} ${pelatihan.jenjang}`,
        nip_nik: Math.random() > 0.3 ? `19${Math.floor(Math.random() * 100)}${Math.floor(Math.random() * 10000000).toString().padStart(8, '0')}` : null,
        jabatan: [`Guru ${pelatihan.jenjang}`, 'Kepala Sekolah', 'Wakil Kepala Sekolah', 'Pengawas', 'Staf Dinas'][Math.floor(Math.random() * 5)],
        unit_kerja: `${pelatihan.jenjang} Negeri ${Math.floor(Math.random() * 100) + 1} ${['Jakarta', 'Bandung', 'Surabaya', 'Yogyakarta', 'Medan'][Math.floor(Math.random() * 5)]}`,
        no_hp: `08${Math.floor(Math.random() * 10)}${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`
      });
    }
    
    for (let day = 0; day < durationDays; day++) {
      const attendanceDate = new Date(pelatihan.tgl_mulai);
      attendanceDate.setDate(attendanceDate.getDate() + day);
      
      // Some participants might be absent randomly
      const presentParticipants = participants.filter(() => Math.random() > 0.1); // 10% chance of absence
      
      for (const participant of presentParticipants) {
        absensiList.push({
          id: uuidv4(),
          pelatihanId: pelatihan.id,
          nama: participant.nama,          nip_nik: participant.nip_nik || `NIK${Math.floor(Math.random() * 10000000000000000).toString().padStart(16, '0')}`,
          jabatan: participant.jabatan,
          unit_kerja: participant.unit_kerja,
          no_hp: participant.no_hp,
          jenjang: pelatihan.jenjang,
          waktu: createIndonesiaTime(new Date(attendanceDate.setHours(8 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 60)))),
          tanda_tangan: generateSignatureData(),
          createdAt: new Date()
        });
      }
    }
  }

  for (const absensi of absensiList) {
    await prisma.absensi.create({
      data: absensi
    });
  }

  console.info(`Created ${absensiList.length} absensi entries`);
  console.info('Seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
