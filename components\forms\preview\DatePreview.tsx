import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { BasePreviewProps } from './BasePreviewProps';
import { normalizeQuestion } from './helpers';
// Remove unused import

export function DatePreview({ question, answers, errors, handleAnswerChange }: BasePreviewProps) {
  const q = normalizeQuestion(question);
  
  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-lg">
          {q.title}
          {q.isRequired && <span className="ml-1 text-red-500">*</span>}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Input 
          type="date" 
          value={answers[q.id] || ''}
          onChange={(e) => handleAnswerChange(q.id, e.target.value)}
        />
        {errors[q.id] && (
          <p className="mt-2 text-sm text-red-500">{errors[q.id]}</p>
        )}
      </CardContent>
    </Card>
  );
}
