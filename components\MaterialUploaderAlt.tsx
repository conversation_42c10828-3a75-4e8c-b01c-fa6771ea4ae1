'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { FaFilePdf, FaTrash, FaDownload, FaUpload } from 'react-icons/fa';
import { toast } from 'sonner';
import { csrfFetch } from '../utils/csrfClient';
import { logger } from '@/utils/logger';

interface MaterialUploaderProps {
  pelatihanId: string;
  initialFiles?: MaterialFile[];
  onChange?: (files: MaterialFile[]) => void;
}

export interface MaterialFile {
  id: string;
  nama_file: string;
  path_file: string;
  size: number;
  mime_type: string;
  createdAt?: string;
}

export default function MaterialUploaderAlt({ pelatihanId, initialFiles = [], onChange }: MaterialUploaderProps) {
  const [files, setFiles] = useState<MaterialFile[]>(initialFiles);
  const [uploading, setUploading] = useState(false);

  // Update files state when initialFiles prop changes
  useEffect(() => {
    setFiles(initialFiles);
  }, [initialFiles]);

  // Fungsi untuk upload file - dibungkus dengan useCallback
  const uploadFile = useCallback(async (file: File) => {
    logger.info('Attempting to upload material file', {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
    });

    if (file.type !== 'application/pdf') {
      toast.error('Hanya file PDF yang diperbolehkan');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      toast.error('Ukuran file melebihi batas 2MB');
      return;
    }

    setUploading(true);
    const formData = new FormData();
    formData.append('file', file);

    try {      logger.info('Uploading file to pelatihan API', {
        endpoint: `/api/pelatihan/${pelatihanId}/upload-materi`,
      });

      const response = await csrfFetch(`/api/pelatihan/${pelatihanId}/upload-materi`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Gagal mengunggah materi');
      }

      const result = await response.json();
      logger.info('Upload response received', { success: result.success });

      if (!result.success) {
        throw new Error(result.message || 'Gagal mengunggah materi');
      }

      const newFile = result.data;
      const updatedFiles = [...files, newFile];
      setFiles(updatedFiles);

      if (onChange) {
        onChange(updatedFiles);
      }

      toast.success('Materi berhasil diunggah');
    } catch (error) {
      logger.error('Error uploading file', error instanceof Error ? error : new Error('Unknown upload error'));
      toast.error(error instanceof Error ? error.message : 'Gagal mengunggah file');
    } finally {
      setUploading(false);
    }
  }, [files, onChange, pelatihanId]);

  // Handler untuk hapus file - dibungkus dengan useCallback
  const deleteFile = useCallback(async (fileId: string) => {
    try {
      logger.info('Deleting material file', { fileId });

      const response = await csrfFetch(`/api/pelatihan/${pelatihanId}/materi?materiId=${fileId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Gagal menghapus materi');
      }

      const result = await response.json();
      logger.info('Delete response received', { success: result.success });

      if (!result.success) {
        throw new Error(result.message || 'Gagal menghapus materi');
      }

      const updatedFiles = files.filter(f => f.id !== fileId);
      setFiles(updatedFiles);

      if (onChange) {
        onChange(updatedFiles);
      }

      toast.success('Materi berhasil dihapus');
    } catch (error) {
      logger.error('Error deleting file', error instanceof Error ? error : new Error('Unknown delete error'));
      toast.error(error instanceof Error ? error.message : 'Gagal menghapus file');
    }
  }, [files, onChange, pelatihanId]);

  // Format ukuran file ke dalam format yang bisa dibaca manusia
  const formatFileSize = useCallback((bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  }, []);

  // Setup react-dropzone dengan callback yang lebih robust
  const onDrop = useCallback((acceptedFiles: File[]) => {
    logger.info('Files dropped', {
      count: acceptedFiles.length,
      fileNames: acceptedFiles.map(f => f.name),
    });

    if (acceptedFiles && acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      uploadFile(file);
    }
  }, [uploadFile]);

  const onDropRejected = useCallback((fileRejections: any[]) => {
    logger.info('Files rejected', { fileRejections });

    if (fileRejections.length > 0) {
      const { errors } = fileRejections[0];
      if (errors && errors.length > 0) {
        toast.error(errors[0].message || 'File tidak valid');
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    onDropRejected,
    accept: {
      'application/pdf': ['.pdf'],
    },
    maxSize: 2 * 1024 * 1024,
    multiple: false,
    noClick: true, // Disable click to open file dialog, we'll use our button instead
  });

  return (
    <div className="mb-6">
      <label className="block mb-2 font-medium text-gray-700">
        Materi Kegiatan (Opsional)
      </label>

      {/* Dropzone area using react-dropzone */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
        }`}
      >
        <input {...getInputProps()} />

        {uploading ? (
          <div className="flex flex-col items-center justify-center">
            <div className="w-10 h-10 border-b-2 border-blue-500 rounded-full animate-spin"></div>
            <p className="mt-2 text-sm text-gray-500">Mengupload file...</p>
          </div>
        ) : (
          <div>
            <FaFilePdf className="mx-auto mb-2 text-3xl text-gray-400" />
            <p className="text-gray-500">
              {isDragActive
                ? 'Letakkan file di sini...'
                : 'Seret file PDF ke sini, atau klik tombol di bawah untuk pilih file'}
            </p>
            <p className="mt-1 text-xs text-gray-400">
              Hanya file PDF, maksimal 2MB
            </p>
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                open();
              }}
              className="px-4 py-2 mt-4 text-sm text-white bg-blue-500 rounded-md hover:bg-blue-600"
            >
              <FaUpload className="inline mr-1" /> Pilih File
            </button>
          </div>
        )}
      </div>

      {/* Daftar file yang telah diupload */}
      {files.length > 0 && (
        <div className="mt-4">
          <h3 className="mb-2 text-sm font-medium">Materi yang diunggah:</h3>
          <ul className="divide-y divide-gray-200">
            {files.map((file) => (
              <li key={file.id} className="flex items-center justify-between py-3">
                <div className="flex items-center">
                  <FaFilePdf className="mr-2 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">{file.nama_file}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <a
                    href={file.path_file}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-1 text-blue-500 hover:text-blue-700"
                  >
                    <FaDownload />
                  </a>
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      deleteFile(file.id);
                    }}
                    className="p-1 text-red-500 hover:text-red-700"
                  >
                    <FaTrash />
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}


