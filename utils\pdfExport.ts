import { PDFDocument, StandardFonts, rgb, PDFFont, PDFPage } from 'pdf-lib';

// Interfaces
export interface RiskActivity {
  id: string;
  risk_managementId: string;
  kegiatan: string;
  risiko: string;
  level: 'SANGAT_TINGGI' | 'TINGGI' | 'SEDANG' | 'RENDAH';
  mitigasi: string;
  gugus_mutu: string;
  probability: string;
  due_date: Date | string;
}

export interface Pelatihan {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: Date | string;
  tgl_berakhir: Date | string;
}

// Helper functions
export const stripHtmlTags = (html: string): string => {
  if (!html) return '';
  const tempElement = document.createElement('div');
  tempElement.innerHTML = html;
  return tempElement.textContent || '';
};

export const formatLevel = (level: string): string => {
  switch (level) {
    case 'SANGAT_TINGGI': return 'Sangat Tinggi';
    case 'TINGGI': return 'Tinggi';
    case 'SEDANG': return 'Sedang';
    case 'RENDAH': return 'Rendah';
    default: return level;
  }
};

import { formatIndonesiaDate } from './dateUtils';

export const formatDate = (date: Date | string) => {
  if (!date) return '';
  return formatIndonesiaDate(date);
};

export const getWrappedTextLines = (text: string, font: PDFFont, fontSize: number, maxWidth: number): string[] => {
  const words = text.split(' ');
  const lines: string[] = [];
  let currentLine = '';
  
  for (const word of words) {
    const testLine = currentLine + (currentLine ? ' ' : '') + word;
    const testWidth = font.widthOfTextAtSize(testLine, fontSize);
    
    if (testWidth > maxWidth && currentLine !== '') {
      lines.push(currentLine);
      currentLine = word;
    } else {
      currentLine = testLine;
    }
  }
  
  if (currentLine) {
    lines.push(currentLine);
  }
  
  return lines;
};

// Colors for the risk matrix cells
const colors = {
  red: rgb(1, 0.25, 0.25),     // #FF4040
  yellow: rgb(1, 0.92, 0.23),  // #FFEB3B
  green: rgb(0.3, 0.69, 0.31), // #4CAF50
  blue: rgb(0.13, 0.59, 0.95), // #2196F3
};

// Template functions
export const renderHeader = (page: PDFPage, pelatihan: Pelatihan | null, fonts: { regular: PDFFont, bold: PDFFont }) => {
  page.drawText('Matrik Manajemen Resiko', {
    x: 50,
    y: 540,
    size: 24,
    font: fonts.bold,
  });
  
  if (pelatihan) {
    page.drawText(`Pelatihan: ${pelatihan.nama}`, {
      x: 50,
      y: 510,
      size: 12,
      font: fonts.regular,
    });
    
    page.drawText(`Lokasi: ${pelatihan.tempat}`, {
      x: 50,
      y: 490,
      size: 12,
      font: fonts.regular,
    });
    
    page.drawText(`Tanggal: ${formatDate(pelatihan.tgl_mulai)} - ${formatDate(pelatihan.tgl_berakhir)}`, {
      x: 50,
      y: 470,
      size: 12,
      font: fonts.regular,
    });
  }
};

// Update the renderMatrix function to include checkmarks

export const renderMatrix = (page: PDFPage, fonts: { regular: PDFFont, bold: PDFFont }, activities: RiskActivity[]) => {
  // Matrix positioning
  const startX = 100;
  const startY = 400;
  const cellWidth = 100;
  const cellHeight = 50;
  const headerCellHeight = 30;
  const firstColumnWidth = 60;
  
  // Helper functions to determine if a cell should have a checkmark
  const getLevelText = (level: string): string => {
    switch (level) {
      case 'SANGAT_TINGGI': return 'Sangat Tinggi';
      case 'TINGGI': return 'Tinggi';
      case 'SEDANG': return 'Sedang';
      case 'RENDAH': return 'Rendah';
      default: return level;
    }
  };
  
  const getProbabilityText = (probability: string): string => {
    switch (probability) {
      case 'VERY_LOW': return 'Jarang Terjadi';
      case 'LOW': return 'Mungkin Terjadi';
      case 'MEDIUM': return 'Dapat Terjadi';
      case 'HIGH': return 'Sering Terjadi';
      default: return probability;
    }
  };
  
  const shouldShowCheckmark = (level: string, probability: string): boolean => {
    return activities.some(activity => {
      const activityLevelText = getLevelText(activity.level);
      const activityProbabilityText = getProbabilityText(activity.probability);
      return activityLevelText === level && activityProbabilityText === probability;
    });
  };
  
  const countActivities = (level: string, probability: string): number => {
    return activities.filter(activity => {
      const activityLevelText = getLevelText(activity.level);
      const activityProbabilityText = getProbabilityText(activity.probability);
      return activityLevelText === level && activityProbabilityText === probability;
    }).length;
  };
  
  // Empty row at top
  let currentY = startY;
  let currentX = startX;
  
  // Draw first column with rotated "Dampak" text
  page.drawRectangle({
    x: currentX,
    y: currentY - (4 * cellHeight), // span 4 rows
    width: firstColumnWidth,
    height: 4 * cellHeight,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  
  // Rotated "Dampak" text (we'll use a horizontal text as pdf-lib doesn't support rotation directly)
  page.drawText("D", {
    x: currentX + 25,
    y: currentY - cellHeight * 2 + 10,
    size: 12,
    font: fonts.bold,
  });
  page.drawText("A", {
    x: currentX + 25,
    y: currentY - cellHeight * 2 - 5,
    size: 12,
    font: fonts.bold,
  });
  page.drawText("M", {
    x: currentX + 25,
    y: currentY - cellHeight * 2 - 20,
    size: 12, 
    font: fonts.bold,
  });
  page.drawText("P", {
    x: currentX + 25,
    y: currentY - cellHeight * 2 - 35,
    size: 12,
    font: fonts.bold,
  });
  page.drawText("A", {
    x: currentX + 25,
    y: currentY - cellHeight * 2 - 50,
    size: 12,
    font: fonts.bold,
  });
  page.drawText("K", {
    x: currentX + 25,
    y: currentY - cellHeight * 2 - 65,
    size: 12,
    font: fonts.bold,
  });
  
  currentX += firstColumnWidth;
  
  // Draw risk level cells
  
  // Row 1: "Sangat Tinggi" - Red cell
  page.drawRectangle({
    x: currentX,
    y: currentY - cellHeight,
    width: cellWidth,
    height: cellHeight,
    color: colors.red,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  page.drawText("Sangat Tinggi", {
    x: currentX + 10,
    y: currentY - cellHeight/2 - 5,
    size: 10,
    font: fonts.bold,
    color: rgb(1, 1, 1),
  });
  
  // Empty cells in row 1
  for (let i = 0; i < 4; i++) {
    page.drawRectangle({
      x: currentX + cellWidth * (i + 1),
      y: currentY - cellHeight,
      width: cellWidth,
      height: cellHeight,
      borderColor: rgb(0.5, 0.5, 0.5),
      borderWidth: 1,
    });
  }
  
  currentY -= cellHeight;
  
  // Row 2: "Tinggi" - Yellow cell
  page.drawRectangle({
    x: currentX,
    y: currentY - cellHeight,
    width: cellWidth,
    height: cellHeight,
    color: colors.yellow,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  page.drawText("Tinggi", {
    x: currentX + 30,
    y: currentY - cellHeight/2 - 5,
    size: 10,
    font: fonts.bold,
    color: rgb(0, 0, 0),
  });
  
  // Empty cells in row 2
  for (let i = 0; i < 4; i++) {
    page.drawRectangle({
      x: currentX + cellWidth * (i + 1),
      y: currentY - cellHeight,
      width: cellWidth,
      height: cellHeight,
      borderColor: rgb(0.5, 0.5, 0.5),
      borderWidth: 1,
    });
  }
  
  currentY -= cellHeight;
  
  // Row 3: "Sedang" - Green cell
  page.drawRectangle({
    x: currentX,
    y: currentY - cellHeight,
    width: cellWidth,
    height: cellHeight,
    color: colors.green,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  page.drawText("Sedang", {
    x: currentX + 30,
    y: currentY - cellHeight/2 - 5,
    size: 10,
    font: fonts.bold,
    color: rgb(0, 0, 0),
  });
  
  // Empty cells in row 3
  for (let i = 0; i < 4; i++) {
    page.drawRectangle({
      x: currentX + cellWidth * (i + 1),
      y: currentY - cellHeight,
      width: cellWidth,
      height: cellHeight,
      borderColor: rgb(0.5, 0.5, 0.5),
      borderWidth: 1,
    });
  }
  
  currentY -= cellHeight;
  
  // Row 4: "Rendah" - Blue cell
  page.drawRectangle({
    x: currentX,
    y: currentY - cellHeight,
    width: cellWidth,
    height: cellHeight,
    color: colors.blue,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  page.drawText("Rendah", {
    x: currentX + 30,
    y: currentY - cellHeight/2 - 5,
    size: 10,
    font: fonts.bold,
    color: rgb(0, 0, 0),
  });
  
  // Empty cells in row 4
  for (let i = 0; i < 4; i++) {
    page.drawRectangle({
      x: currentX + cellWidth * (i + 1),
      y: currentY - cellHeight,
      width: cellWidth,
      height: cellHeight,
      borderColor: rgb(0.5, 0.5, 0.5),
      borderWidth: 1,
    });
  }
  
  currentY -= cellHeight;
  
  // Probability row
  // First cell empty
  page.drawRectangle({
    x: startX,
    y: currentY - headerCellHeight,
    width: firstColumnWidth,
    height: headerCellHeight,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  
  // Second cell empty
  page.drawRectangle({
    x: currentX,
    y: currentY - headerCellHeight,
    width: cellWidth,
    height: headerCellHeight,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  
  // Probability cells
  const probabilityLabels = [
    { percent: "0-10%", label: "Jarang Terjadi" },
    { percent: "10-25%", label: "Mungkin Terjadi" },
    { percent: "25-50%", label: "Dapat Terjadi" },
    { percent: ">50%", label: "Sering Terjadi" }
  ];
  
  for (let i = 0; i < 4; i++) {
    page.drawRectangle({
      x: currentX + cellWidth * (i + 1),
      y: currentY - headerCellHeight,
      width: cellWidth,
      height: headerCellHeight,
      borderColor: rgb(0.5, 0.5, 0.5),
      borderWidth: 1,
    });
    
    // Probability percentage
    page.drawText(probabilityLabels[i].percent, {
      x: currentX + cellWidth * (i + 1) + 35,
      y: currentY - 10,
      size: 8,
      font: fonts.regular,
    });
    
    // Probability label
    page.drawText(probabilityLabels[i].label, {
      x: currentX + cellWidth * (i + 1) + 15,
      y: currentY - 25,
      size: 9,
      font: fonts.bold,
    });
  }
  
  currentY -= headerCellHeight;
  
  // "Kemungkinan" row
  page.drawRectangle({
    x: startX,
    y: currentY - headerCellHeight,
    width: firstColumnWidth,
    height: headerCellHeight,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  
  page.drawRectangle({
    x: currentX,
    y: currentY - headerCellHeight,
    width: cellWidth,
    height: headerCellHeight,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  
  // "Kemungkinan" cell spanning 4 columns
  page.drawRectangle({
    x: currentX + cellWidth,
    y: currentY - headerCellHeight,
    width: cellWidth * 4,
    height: headerCellHeight,
    borderColor: rgb(0.5, 0.5, 0.5),
    borderWidth: 1,
  });
  
  page.drawText("Kemungkinan", {
    x: currentX + cellWidth * 2.5,
    y: currentY - headerCellHeight/2 - 5,
    size: 10,
    font: fonts.bold,
  });

  // After drawing the empty cells in each row, add checkmarks where needed:
  
  // For "Sangat Tinggi" row
  currentY = startY - cellHeight;
  currentX = startX + firstColumnWidth;
  
  // Add checkmarks to the 4 cells in the "Sangat Tinggi" row
  const probabilitiesInOrder = ['Jarang Terjadi', 'Mungkin Terjadi', 'Dapat Terjadi', 'Sering Terjadi'];
  
  for (let i = 0; i < 4; i++) {
    // Check if this cell should have a checkmark
    if (shouldShowCheckmark('Sangat Tinggi', probabilitiesInOrder[i])) {
      // Calculate center of the cell
      // Note that we need to use (i) since cells start at index 0
      const cellCenterX = currentX + cellWidth * i + cellWidth * 1.5;
      const cellCenterY = currentY - cellHeight/2;
      
      // Draw a checkmark using lines instead of text
      const checkSize = 15; // Increased size for better visibility
      
      // V shape for checkmark - adjusted positioning
      page.drawLine({
        start: { x: cellCenterX - checkSize/2, y: cellCenterY + checkSize/2 },
        end: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        thickness: 2.5,
        color: rgb(0.8, 0, 0)
      });
      
      page.drawLine({
        start: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        end: { x: cellCenterX + checkSize, y: cellCenterY + checkSize },
        thickness: 2.5,
        color: rgb(0.8, 0, 0)
      });
      
      // Draw the count below the checkmark
      const count = countActivities('Sangat Tinggi', probabilitiesInOrder[i]);
      if (count > 0) {
        page.drawText(`(${count})`, {
          x: cellCenterX - 8, // Center the count
          y: cellCenterY - 20,
          size: 10,
          font: fonts.regular
        });
      }
    }
  }
  
  // Move to "Tinggi" row
  currentY -= cellHeight;
  
  // Apply the same positioning fix to the remaining rows 
  // (Use the same cellCenterX calculation for all rows)
  
  // Add checkmarks to the 4 cells in the "Tinggi" row
  for (let i = 0; i < 4; i++) {
    if (shouldShowCheckmark('Tinggi', probabilitiesInOrder[i])) {
      const cellCenterX = currentX + cellWidth * i + cellWidth/2;  // Fix the position calculation
      const cellCenterY = currentY - cellHeight/2;
      
      // Draw a checkmark using lines with consistent styling
      const checkSize = 15;
      page.drawLine({
        start: { x: cellCenterX - checkSize/2, y: cellCenterY + checkSize/2 },
        end: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        thickness: 2.5,
        color: rgb(0.8, 0.6, 0) // Yellow-orange
      });
      
      page.drawLine({
        start: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        end: { x: cellCenterX + checkSize, y: cellCenterY + checkSize },
        thickness: 2.5,
        color: rgb(0.8, 0.6, 0)
      });
      
      // Draw the count below the checkmark
      const count = countActivities('Tinggi', probabilitiesInOrder[i]);
      if (count > 0) {
        page.drawText(`(${count})`, {
          x: cellCenterX - 8,
          y: cellCenterY - 20,
          size: 10,
          font: fonts.regular
        });
      }
    }
  }
  
  // Move to "Sedang" row
  currentY -= cellHeight;
  
  // Add checkmarks to the 4 cells in the "Sedang" row
  for (let i = 0; i < 4; i++) {
    if (shouldShowCheckmark('Sedang', probabilitiesInOrder[i])) {
      const cellCenterX = currentX + cellWidth * i + cellWidth/2;  // Fix the position calculation
      const cellCenterY = currentY - cellHeight/2;
      
      // Draw a checkmark using lines with consistent styling
      const checkSize = 15;
      page.drawLine({
        start: { x: cellCenterX - checkSize/2, y: cellCenterY + checkSize/2 },
        end: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        thickness: 2.5,
        color: rgb(0, 0.6, 0) // Green
      });
      
      page.drawLine({
        start: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        end: { x: cellCenterX + checkSize, y: cellCenterY + checkSize },
        thickness: 2.5,
        color: rgb(0, 0.6, 0)
      });
      
      const count = countActivities('Sedang', probabilitiesInOrder[i]);
      if (count > 0) {
        page.drawText(`(${count})`, {
          x: cellCenterX - 8,
          y: cellCenterY - 20,
          size: 10,
          font: fonts.regular
        });
      }
    }
  }
  
  // Move to "Rendah" row
  currentY -= cellHeight;
  
  // Add checkmarks to the 4 cells in the "Rendah" row
  for (let i = 0; i < 4; i++) {
    if (shouldShowCheckmark('Rendah', probabilitiesInOrder[i])) {
      const cellCenterX = currentX + cellWidth * i + cellWidth/2;  // Fix the position calculation
      const cellCenterY = currentY - cellHeight/2;
      
      // Draw a checkmark using lines with consistent styling
      const checkSize = 15;  // Make all checkmarks the same size
      page.drawLine({
        start: { x: cellCenterX - checkSize/2, y: cellCenterY + checkSize/2 },
        end: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        thickness: 2.5,
        color: rgb(0, 0.4, 0.8) // Blue
      });
      
      page.drawLine({
        start: { x: cellCenterX, y: cellCenterY - checkSize/2 },
        end: { x: cellCenterX + checkSize, y: cellCenterY + checkSize },
        thickness: 2.5,
        color: rgb(0, 0.4, 0.8)
      });
      
      const count = countActivities('Rendah', probabilitiesInOrder[i]);
      if (count > 0) {
        page.drawText(`(${count})`, {
          x: cellCenterX - 8, 
          y: cellCenterY - 20,
          size: 10,
          font: fonts.regular
        });
      }
    }
  }
};

export const renderTable = (page: PDFPage, activities: RiskActivity[], fonts: { regular: PDFFont, bold: PDFFont }) => {
    // Define table layout - adjusted for landscape A4 (841.89 x 595.28)
    const pageWidth = 841.89;
    const margins = 50; // Left and right margins
    const availableWidth = pageWidth - (margins * 2);
    
    // Adjusted column widths to fit within page boundaries
    // Total should equal availableWidth (741.89)
    const colWidths = [40, 150, 180, 80, 200, 91.89]; // Width sum = 741.89
    
    const startY = 540; // Moved higher on the page
    const startX = margins;
    const fontSize = 10;
    const minRowHeight = 40; // Minimum row height, will expand based on content
    
    // Draw table headers
    const headers = ['No', 'Kegiatan', 'Risiko', 'Level', 'Mitigasi', 'Gugus Mutu'];
    let currentX = startX;
    
    for (let i = 0; i < headers.length; i++) {
      // Draw header cell background
      page.drawRectangle({
        x: currentX,
        y: startY - minRowHeight,
        width: colWidths[i],
        height: minRowHeight,
        color: rgb(0.05, 0.15, 0.26),
        borderColor: rgb(0, 0, 0),
        borderWidth: 1,
      });
      
      // Draw header text
      page.drawText(headers[i], {
        x: currentX + 5,
        y: startY - minRowHeight + 15, // Adjusted for better vertical centering
        size: fontSize,
        font: fonts.bold,
        color: rgb(1, 1, 1),
      });
      
      currentX += colWidths[i];
    }
    
    // Draw table rows
    let currentY = startY - minRowHeight;
    
    activities.forEach((activity, index) => {
      // Calculate row height based on content
      const kegiatanText = activity.kegiatan || '';
      const risikoText = stripHtmlTags(activity.risiko || '');
      const mitigasiText = stripHtmlTags(activity.mitigasi || '');
      
      const kegiatanLines = getWrappedTextLines(kegiatanText, fonts.regular, fontSize, colWidths[1] - 10);
      const risikoLines = getWrappedTextLines(risikoText, fonts.regular, fontSize, colWidths[2] - 10);
      const mitigasiLines = getWrappedTextLines(mitigasiText, fonts.regular, fontSize, colWidths[4] - 10);
      
      // Calculate the max number of lines to determine the row height
      const maxLines = Math.max(
        kegiatanLines.length,
        risikoLines.length,
        mitigasiLines.length,
        1 // Minimum 1 line
      );
      
      // Calculate dynamic row height with a minimum
      const lineHeight = 12; // Line height in points
      const paddingHeight = 10; // Top and bottom padding
      const rowHeight = Math.max(minRowHeight, (maxLines * lineHeight) + paddingHeight);
      
      // Draw row background (alternating)
      page.drawRectangle({
        x: startX,
        y: currentY - rowHeight,
        width: availableWidth,
        height: rowHeight,
        color: index % 2 === 0 ? rgb(0.95, 0.95, 0.95) : rgb(1, 1, 1),
        borderColor: rgb(0, 0, 0),
        borderWidth: 1,
      });
      
      // Reset X position for this row's cells
      currentX = startX;
      
      // No. column
      page.drawText((index + 1).toString(), {
        x: currentX + 5,
        y: currentY - (rowHeight / 2) + 5, // Vertically centered
        size: fontSize,
        font: fonts.regular,
      });
      
      // Vertical border for column
      page.drawLine({
        start: { x: currentX + colWidths[0], y: currentY },
        end: { x: currentX + colWidths[0], y: currentY - rowHeight },
        thickness: 1,
        color: rgb(0, 0, 0),
      });
      currentX += colWidths[0];
      
      // Kegiatan column
      const kegiatanYStart = currentY - 15;
      kegiatanLines.forEach((line, i) => {
        page.drawText(line, {
          x: currentX + 5,
          y: kegiatanYStart - (i * lineHeight),
          size: fontSize,
          font: fonts.regular,
        });
      });
      
      // Vertical border
      page.drawLine({
        start: { x: currentX + colWidths[1], y: currentY },
        end: { x: currentX + colWidths[1], y: currentY - rowHeight },
        thickness: 1,
        color: rgb(0, 0, 0),
      });
      currentX += colWidths[1];
      
      // Risiko column
      const risikoYStart = currentY - 15;
      risikoLines.forEach((line, i) => {
        page.drawText(line, {
          x: currentX + 5,
          y: risikoYStart - (i * lineHeight),
          size: fontSize,
          font: fonts.regular,
        });
      });
      
      // Vertical border
      page.drawLine({
        start: { x: currentX + colWidths[2], y: currentY },
        end: { x: currentX + colWidths[2], y: currentY - rowHeight },
        thickness: 1,
        color: rgb(0, 0, 0),
      });
      currentX += colWidths[2];
      
      // Level column
      page.drawText(formatLevel(activity.level), {
        x: currentX + 5,
        y: currentY - (rowHeight / 2) + 5, // Vertically centered
        size: fontSize,
        font: fonts.regular,
      });
      
      // Vertical border
      page.drawLine({
        start: { x: currentX + colWidths[3], y: currentY },
        end: { x: currentX + colWidths[3], y: currentY - rowHeight },
        thickness: 1,
        color: rgb(0, 0, 0),
      });
      currentX += colWidths[3];
      
      // Mitigasi column
      const mitigasiYStart = currentY - 15;
      mitigasiLines.forEach((line, i) => {
        page.drawText(line, {
          x: currentX + 5,
          y: mitigasiYStart - (i * lineHeight),
          size: fontSize,
          font: fonts.regular,
        });
      });
      
      // Vertical border
      page.drawLine({
        start: { x: currentX + colWidths[4], y: currentY },
        end: { x: currentX + colWidths[4], y: currentY - rowHeight },
        thickness: 1,
        color: rgb(0, 0, 0),
      });
      currentX += colWidths[4];
      
      // Gugus Mutu column
      page.drawText(activity.gugus_mutu || '', {
        x: currentX + 5,
        y: currentY - (rowHeight / 2) + 5, // Vertically centered
        size: fontSize,
        font: fonts.regular,
      });
      
      // Move down for next row
      currentY -= rowHeight;
    });
  };

// Main export function
export const generateRiskManagementPDF = async (pelatihan: Pelatihan | null, activities: RiskActivity[]): Promise<Uint8Array> => {
  const pdfDoc = await PDFDocument.create();
  
  // Create landscape A4 pages
  const page1 = pdfDoc.addPage([841.89, 595.28]);
  const page2 = pdfDoc.addPage([841.89, 595.28]);
  
  const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  
  const fonts = {
    regular: helveticaFont,
    bold: helveticaBold
  };
  
  // Render each section using templates
  renderHeader(page1, pelatihan, fonts);
  renderMatrix(page1, fonts, activities);
  renderTable(page2, activities, fonts);
  
  return pdfDoc.save();
};

// Add this function to export just the matrix

export const generateRiskMatrixPDF = async (pelatihan: Pelatihan | null, activities: RiskActivity[]): Promise<Uint8Array> => {
  const pdfDoc = await PDFDocument.create();
  
  // Create landscape A4 page
  const page = pdfDoc.addPage([841.89, 595.28]);
  
  const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  
  const fonts = {
    regular: helveticaFont,
    bold: helveticaBold
  };
  
  // Render title
  page.drawText("MATRIKS MANAJEMEN RISIKO", {
    x: 320,
    y: 550,
    size: 16,
    font: fonts.bold
  });
  
  // Render pelatihan details if available
  if (pelatihan) {
    page.drawText(`Pelatihan: ${pelatihan.nama}`, {
      x: 50,
      y: 520,
      size: 12,
      font: fonts.regular,
    });
    
    page.drawText(`Lokasi: ${pelatihan.tempat}`, {
      x: 50,
      y: 500,
      size: 12,
      font: fonts.regular,
    });
    
    page.drawText(`Tanggal: ${formatDate(pelatihan.tgl_mulai)} - ${formatDate(pelatihan.tgl_berakhir)}`, {
      x: 50,
      y: 480,
      size: 12,
      font: fonts.regular,
    });
  }
  
  // Render just the matrix table
  renderMatrix(page, fonts, activities);
  
  return pdfDoc.save();
};
