@echo off
setlocal enabledelayedexpansion

:: Real-time Monitoring untuk Advanced Auto-Refresh Fix
:: Windows script untuk monitor status dari jarak jauh

title Advanced Auto-Refresh Monitoring - Real-time Status

echo.
echo ================================================
echo    ADVANCED AUTO-REFRESH MONITORING
echo ================================================
echo.

:: Configuration
set VPS_IP=*************
set VPS_USER=root
set MONITOR_SCRIPT=/tmp/monitor-autorefresh-status.sh

:: Check arguments
set MODE=%1
if "%MODE%"=="" set MODE=single

echo [36m[INFO][0m Connecting to VPS: %VPS_IP%
echo [36m[INFO][0m Mode: %MODE%
echo.

:: Step 1: Upload monitoring script if needed
echo [35m[ACTION][0m Uploading monitoring script...

scp "%~dp0monitor-autorefresh-status.sh" %VPS_USER%@%VPS_IP%:%MONITOR_SCRIPT%

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to upload monitoring script
    echo [33m[WARNING][0m Trying to run existing script...
    goto :run_monitor
)

echo [32m[SUCCESS][0m Monitoring script uploaded

:run_monitor
:: Step 2: Make executable and run monitoring
echo [35m[ACTION][0m Running monitoring dashboard...

if "%MODE%"=="continuous" (
    echo [36m[INFO][0m Starting continuous monitoring...
    echo [33m[WARNING][0m Press Ctrl+C to stop monitoring
    echo.
    ssh %VPS_USER%@%VPS_IP% "chmod +x %MONITOR_SCRIPT% && %MONITOR_SCRIPT% --continuous"
) else if "%MODE%"=="test" (
    echo [36m[INFO][0m Running single check with test...
    ssh %VPS_USER%@%VPS_IP% "chmod +x %MONITOR_SCRIPT% && %MONITOR_SCRIPT% --test"
) else (
    echo [36m[INFO][0m Running single status check...
    ssh %VPS_USER%@%VPS_IP% "chmod +x %MONITOR_SCRIPT% && %MONITOR_SCRIPT% --single"
)

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Monitoring failed
    echo.
    echo [33mTroubleshooting:[0m
    echo 1. Check SSH connection
    echo 2. Verify VPS is accessible
    echo 3. Try manual: ssh %VPS_USER%@%VPS_IP%
    goto :show_manual_commands
)

echo.
echo [32m[SUCCESS][0m Monitoring completed

:show_manual_commands
echo.
echo [36m================================================[0m
echo [32m   MONITORING COMMANDS REFERENCE[0m
echo [36m================================================[0m
echo.
echo [33mQuick SSH Connect:[0m
echo   ssh %VPS_USER%@%VPS_IP%
echo.
echo [33mService Status:[0m
echo   systemctl status nginx
echo   systemctl status advanced-photo-monitor
echo.
echo [33mReal-time Logs:[0m
echo   journalctl -u advanced-photo-monitor -f
echo   tail -f /var/log/advanced-photo-monitor.log
echo   tail -f /var/log/photo-monitor-stats.log
echo.
echo [33mNginx Logs:[0m
echo   tail -f /var/log/nginx/photo_access.log
echo   tail -f /var/log/nginx/kegiatan_access.log
echo.
echo [33mQuick Fix Commands:[0m
echo   systemctl restart advanced-photo-monitor
echo   systemctl reload nginx
echo.
echo [33mTest Commands:[0m
echo   curl -I http://localhost/uploads/test.jpg
echo   ls -la /home/<USER>/htdocs/public/uploads/
echo.

:: Option to connect to SSH directly
echo [36m[INFO][0m Connect to SSH for manual monitoring? (y/n)
set /p choice="Choice: "

if /i "%choice%"=="y" (
    echo.
    echo [35m[ACTION][0m Opening SSH connection...
    ssh %VPS_USER%@%VPS_IP%
)

echo.
echo [32m[SUCCESS][0m Monitoring session completed
pause
