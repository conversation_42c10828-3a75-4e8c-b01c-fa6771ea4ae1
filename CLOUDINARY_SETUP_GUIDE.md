# Panduan Setup Cloudinary untuk Biodata Photo Upload

## Masalah yang Terjadi
API key Cloudinary yang digunakan tidak valid, menyebabkan error 401 "Unknown API key".

## Solusi yang Telah Diimplementasikan

### 1. Sistem Backup Upload Lokal
- Dibuat endpoint `/api/upload-local` sebagai backup
- PhotoUpload component akan mencoba Cloudinary dulu, jika gagal akan otomatis menggunakan upload lokal
- File akan disimpan di `/public/uploads/{folder}/` dengan optimasi menggunakan Sharp

### 2. Format Credentials yang Benar
File `.env` telah diupdate dengan format placeholder yang benar:
```
CLOUDINARY_CLOUD_NAME="demo"
CLOUDINARY_API_KEY="***************"
CLOUDINARY_API_SECRET="abcdefghijklmnopqrstuvwxyz123456"
```

## Cara Mendapatkan Credentials Cloudinary yang Valid

### Langkah 1: Daftar/Login ke Cloudinary
1. <PERSON><PERSON> https://cloudinary.com
2. Klik "Sign Up for Free" atau "Log In"
3. Daftar dengan email atau login dengan akun yang sudah ada

### Langkah 2: Dapatkan Credentials
1. Setelah login, buka https://console.cloudinary.com
2. Di dashboard, lihat section "Account Details"
3. Copy credentials berikut:
   - **Cloud Name**: nama cloud Anda (contoh: "my-cloud-name")
   - **API Key**: angka numerik (contoh: "***************")
   - **API Secret**: string huruf/angka (contoh: "abcdefghijklmnopqrstuvwxyz123456")

### Langkah 3: Update File .env
Ganti nilai di file `.env` dengan credentials asli:
```
CLOUDINARY_CLOUD_NAME="nama-cloud-anda"
CLOUDINARY_API_KEY="api-key-numerik-anda"
CLOUDINARY_API_SECRET="api-secret-anda"
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="nama-cloud-anda"
```

### Langkah 4: Restart Server
Setelah update .env, restart development server:
```bash
npm run dev
```

## Status Saat Ini
✅ **Sistem backup upload lokal sudah aktif** - foto akan tetap bisa diupload meski Cloudinary gagal
✅ **PhotoUpload component sudah diupdate** - otomatis fallback ke local upload
✅ **Error handling sudah diperbaiki** - user akan dapat feedback yang jelas

## Testing
1. Coba upload foto di form biodata
2. Jika Cloudinary berhasil: foto akan tersimpan di cloud
3. Jika Cloudinary gagal: foto akan otomatis tersimpan secara lokal di `/public/uploads/biodata/`

## Production Deployment
Untuk production, pastikan:
1. Credentials Cloudinary sudah valid
2. Direktori `/public/uploads/` memiliki permission write
3. Environment variables sudah diset dengan benar di server

## Troubleshooting
- Jika masih error, check console browser untuk pesan error detail
- Pastikan file .env sudah disave dan server sudah direstart
- Check apakah direktori uploads/ sudah terbuat di /public/
