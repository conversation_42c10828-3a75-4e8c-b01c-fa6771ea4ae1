#!/bin/bash

# Script Perbaikan Photo Upload - Khusus untuk masalah folder duplikasi
# Jalankan setelah diagnosa untuk memperbaiki masalah

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Konfigurasi - UPDATE SESUAI VPS ANDA
APP_DIR="/home/<USER>/deployment"  # UPDATE: Path ke aplikasi Next.js Anda
BACKUP_DIR="/home/<USER>/backup-uploads-$(date +%Y%m%d-%H%M%S)"
NGINX_SITE_CONFIG="/etc/nginx/sites-available/default"  # atau kegiatan.bpmpkaltim.id

echo -e "${BLUE}"
echo "============================================"
echo "   PERBAIKAN PHOTO UPLOAD - OTOMATIS"
echo "============================================"
echo -e "${NC}"

# Verifikasi user permission
if [ "$EUID" -ne 0 ]; then
    log_error "Script ini harus dijalankan dengan sudo"
    echo "Gunakan: sudo $0"
    exit 1
fi

# Verifikasi APP_DIR
if [ ! -d "$APP_DIR" ]; then
    log_error "Directory aplikasi tidak ditemukan: $APP_DIR"
    echo "Update variable APP_DIR di script ini"
    exit 1
fi

echo ""
log_info "=== LANGKAH 1: BACKUP DATA ===="

# Buat backup directory
mkdir -p "$BACKUP_DIR"
log_success "Backup directory dibuat: $BACKUP_DIR"

# Backup semua folder uploads yang ada
if [ -d "$APP_DIR/public" ]; then
    cp -r "$APP_DIR/public" "$BACKUP_DIR/"
    log_success "Backup public folder selesai"
else
    log_error "Folder public tidak ditemukan!"
    exit 1
fi

echo ""
log_info "=== LANGKAH 2: DETEKSI MASALAH ===="

DUPLICATE_FOUND=false
UPLOADS_MOVED=false

# Cek apakah ada duplikasi folder public/public
if [ -d "$APP_DIR/public/public" ]; then
    log_warning "Duplikasi folder ditemukan: public/public/"
    DUPLICATE_FOUND=true
    
    # Cek apakah ada uploads di folder duplikasi
    if [ -d "$APP_DIR/public/public/uploads" ]; then
        log_warning "Upload files ditemukan di folder duplikasi"
        
        # Pindahkan uploads dari duplikasi ke lokasi yang benar
        log_info "Memindahkan files dari public/public/uploads/ ke public/uploads/"
        
        # Buat folder uploads target jika belum ada
        mkdir -p "$APP_DIR/public/uploads"
        
        # Pindahkan semua isi folder
        if [ "$(ls -A $APP_DIR/public/public/uploads/)" ]; then
            cp -r "$APP_DIR/public/public/uploads/"* "$APP_DIR/public/uploads/"
            log_success "Files berhasil dipindahkan"
            UPLOADS_MOVED=true
        else
            log_info "Folder duplikasi kosong"
        fi
        
        # Hapus folder duplikasi
        rm -rf "$APP_DIR/public/public"
        log_success "Folder duplikasi dihapus"
    else
        log_info "Tidak ada uploads di folder duplikasi"
        rm -rf "$APP_DIR/public/public"
        log_success "Folder duplikasi kosong dihapus"
    fi
else
    log_info "Tidak ada duplikasi folder ditemukan"
fi

echo ""
log_info "=== LANGKAH 3: SETUP STRUKTUR FOLDER ===="

# Pastikan struktur folder uploads lengkap
REQUIRED_DIRS=(
    "$APP_DIR/public/uploads"
    "$APP_DIR/public/uploads/absensi"
    "$APP_DIR/public/uploads/absensi/photos"
    "$APP_DIR/public/uploads/biodata"
    "$APP_DIR/public/uploads/biodata/photos"
    "$APP_DIR/public/uploads/materi"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        log_success "Dibuat: $dir"
    else
        log_info "Sudah ada: $dir"
    fi
done

echo ""
log_info "=== LANGKAH 4: SET PERMISSIONS ===="

# Set ownership ke www-data
chown -R www-data:www-data "$APP_DIR/public/uploads"
log_success "Ownership diset ke www-data:www-data"

# Set permissions
chmod -R 755 "$APP_DIR/public/uploads"
log_success "Permissions diset ke 755"

echo ""
log_info "=== LANGKAH 5: UPDATE NGINX CONFIG ===="

# Backup nginx config
cp "$NGINX_SITE_CONFIG" "$BACKUP_DIR/nginx-config-backup"
log_success "Nginx config di-backup"

# Cek apakah sudah ada konfigurasi uploads
if grep -q "location /uploads/" "$NGINX_SITE_CONFIG"; then
    log_info "Konfigurasi uploads sudah ada di nginx"
else
    log_warning "Menambahkan konfigurasi uploads ke nginx"
    
    # Tambahkan konfigurasi uploads sebelum location /
    sed -i '/location \/ {/i\
    # Static file serving for uploads\
    location /uploads/ {\
        root '"$APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        try_files $uri $uri/ =404;\
    }\
' "$NGINX_SITE_CONFIG"

    log_success "Konfigurasi uploads ditambahkan ke nginx"
fi

# Test nginx config
if nginx -t; then
    log_success "Nginx config valid"
    
    # Reload nginx
    systemctl reload nginx
    log_success "Nginx direload"
else
    log_error "Nginx config error! Restoring backup..."
    cp "$BACKUP_DIR/nginx-config-backup" "$NGINX_SITE_CONFIG"
    systemctl reload nginx
    exit 1
fi

echo ""
log_info "=== LANGKAH 6: VERIFIKASI HASIL ===="

# Cek struktur akhir
log_info "Struktur folder akhir:"
if [ -d "$APP_DIR/public/uploads" ]; then
    find "$APP_DIR/public/uploads" -type d | head -10 | while read -r dir; do
        file_count=$(find "$dir" -maxdepth 1 -type f | wc -l 2>/dev/null || echo "0")
        echo "   $dir ($file_count files)"
    done
fi

# Cek sample file jika ada
SAMPLE_PHOTOS=$(find "$APP_DIR/public/uploads/absensi/photos" -name "*.jpg" | head -3 2>/dev/null || true)
if [ -n "$SAMPLE_PHOTOS" ]; then
    log_success "Sample foto yang ditemukan:"
    echo "$SAMPLE_PHOTOS" | while read -r photo; do
        # Convert absolute path to relative URL
        REL_PATH=${photo#$APP_DIR/public}
        echo "   https://kegiatan.bpmpkaltim.id$REL_PATH"
    done
else
    log_info "Belum ada foto yang diupload"
fi

echo ""
log_info "=== LANGKAH 7: TEST AKSES ===="

# Test akses dengan curl jika ada sample photo
if [ -n "$SAMPLE_PHOTOS" ]; then
    FIRST_PHOTO=$(echo "$SAMPLE_PHOTOS" | head -1)
    REL_PATH=${FIRST_PHOTO#$APP_DIR/public}
    TEST_URL="https://kegiatan.bpmpkaltim.id$REL_PATH"
    
    log_info "Testing akses ke: $TEST_URL"
    
    if curl -s -I "$TEST_URL" | grep -q "200 OK"; then
        log_success "✅ Foto dapat diakses!"
    else
        log_warning "❌ Foto belum dapat diakses, cek nginx config"
    fi
fi

echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   PERBAIKAN SELESAI!"
echo "============================================"
echo -e "${NC}"
echo ""
echo "📋 RINGKASAN:"
echo "✅ Backup dibuat di: $BACKUP_DIR"
echo "✅ Struktur folder diperbaiki"
echo "✅ Permissions diset"
echo "✅ Nginx dikonfigurasi"
echo ""
echo "🧪 TESTING:"
echo "1. Upload foto baru melalui form absensi"
echo "2. Cek apakah foto muncul di halaman detail"
echo "3. Test direct URL: https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/..."
echo ""
echo "🆘 JIKA MASIH BERMASALAH:"
echo "1. Cek logs: pm2 logs"
echo "2. Cek nginx: sudo nginx -t"
echo "3. Restore backup: cp -r $BACKUP_DIR/public/* $APP_DIR/public/"
