// File: /app/public-pages/absensi/[link]/success/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { CheckCircle, Download, Clock, User } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AbsensiData {
  id: string;
  nama: string;
  unit_kerja: string;
  waktu: string;
  pelatihan: {
    nama: string;
    tempat: string;
  };
}

export default function AbsensiSuccessPage() {
  const params = useParams<{ link: string }>();
  const [absensiData, setAbsensiData] = useState<AbsensiData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Prevent going back to the form page
  useEffect(() => {
    // Replace the current history entry to prevent going back
    window.history.replaceState(null, '', window.location.href);

    // Handle the popstate event (when user clicks back button)
    const handlePopState = () => {
      window.history.pushState(null, '', window.location.href);
    };

    // Add event listener for back button clicks
    window.addEventListener('popstate', handlePopState);

    return () => {
      // Clean up event listener
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Fetch the latest absensi data for this link
  useEffect(() => {
    const fetchAbsensiData = async () => {
      try {
        const response = await fetch(`/api/absensi/latest?link=${params.link}`);
        if (response.ok) {
          const data = await response.json();
          setAbsensiData(data);
        }
      } catch (err) {
        console.error('Error fetching absensi data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (params.link) {
      fetchAbsensiData();
    }
  }, [params.link]);

  const handlePrintCertificate = () => {
    window.print();
  };

  const handleDownloadData = () => {
    if (!absensiData) return;
    
    const dataText = `
BUKTI ABSENSI

Nama: ${absensiData.nama}
Unit Kerja: ${absensiData.unit_kerja}
Kegiatan: ${absensiData.pelatihan.nama}
Tempat: ${absensiData.pelatihan.tempat}
Waktu Absensi: ${new Date(absensiData.waktu).toLocaleString('id-ID')}

© ${new Date().getFullYear()} BPMP Provinsi Kalimantan Timur
    `.trim();

    const blob = new Blob([dataText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `absensi-${absensiData.nama}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  return (
    <div className="flex items-center justify-center min-h-screen px-3 sm:px-4 lg:px-6 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="w-full max-w-lg bg-white rounded-lg shadow-lg">
        {/* Success Header */}
        <div className="p-4 sm:p-6 lg:p-8 text-center border-b">
          <div className="flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 bg-green-100 rounded-full">
            <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
          </div>
          <h1 className="mb-2 text-xl sm:text-2xl font-bold text-gray-800">Absensi Berhasil!</h1>
          <p className="text-sm sm:text-base text-gray-600">
            Terima kasih. Data absensi Anda telah berhasil disimpan.
          </p>
        </div>        {/* Attendance Summary */}
        {!isLoading && absensiData && (
          <div className="p-4 sm:p-6 lg:p-8">
            <h2 className="mb-3 sm:mb-4 text-base sm:text-lg font-semibold text-gray-800 flex items-center gap-2">
              <User className="w-4 h-4 sm:w-5 sm:h-5" />
              Ringkasan Absensi
            </h2>
            
            <div className="space-y-2 sm:space-y-3 mb-4 sm:mb-6">
              <div>
                <p className="text-xs sm:text-sm text-gray-500">Nama Peserta</p>
                <p className="font-medium text-sm sm:text-base">{absensiData.nama}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm text-gray-500">Unit Kerja</p>
                <p className="font-medium text-sm sm:text-base">{absensiData.unit_kerja}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm text-gray-500">Kegiatan</p>
                <p className="font-medium text-sm sm:text-base">{absensiData.pelatihan.nama}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm text-gray-500">Tempat</p>
                <p className="font-medium text-sm sm:text-base">{absensiData.pelatihan.tempat}</p>
              </div>
              <div>
                <p className="text-xs sm:text-sm text-gray-500 flex items-center gap-1">
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                  Waktu Absensi
                </p>
                <p className="font-medium text-sm sm:text-base">
                  {new Date(absensiData.waktu).toLocaleString('id-ID')}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-2 sm:gap-3 lg:flex-row">
              <Button
                onClick={handlePrintCertificate}
                className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 w-full lg:w-auto text-sm sm:text-base"
              >
                <Download className="w-3 h-3 sm:w-4 sm:h-4" />
                Cetak Bukti
              </Button>
              
              <Button
                onClick={handleDownloadData}
                variant="outline"
                className="flex items-center justify-center gap-2 w-full lg:w-auto text-sm sm:text-base"
              >
                <Download className="w-3 h-3 sm:w-4 sm:h-4" />
                Unduh Data
              </Button>
            </div>
          </div>
        )}        {/* Loading State */}
        {isLoading && (
          <div className="p-4 sm:p-6 lg:p-8 text-center">
            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto mb-3 sm:mb-4"></div>
            <p className="text-sm sm:text-base text-gray-600">Memuat data absensi...</p>
          </div>
        )}

        {/* Footer */}
        <div className="p-3 sm:p-4 text-center text-xs text-gray-500 border-t bg-gray-50 rounded-b-lg">
          <p>© {new Date().getFullYear()} BPMP Provinsi Kalimantan Timur</p>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .no-print {
            display: none !important;
          }
          
          body {
            background: white !important;
          }
          
          .bg-gradient-to-br {
            background: white !important;
          }
          
          .shadow-lg {
            box-shadow: none !important;
            border: 1px solid #e5e7eb !important;
          }
        }
      `}</style>
    </div>
  );
}