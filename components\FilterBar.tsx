'use client';

import { useState, useEffect, ReactNode, FormEvent } from 'react';
import { useRouter } from 'next/navigation';

interface FilterOption {
  id: string;
  label: string;
  value: string;
}

interface FilterBarProps {
  filters: {
    name: string;
    label: string;
    options?: FilterOption[];
    type: 'select' | 'date' | 'text';
    placeholder?: string;
  }[];
  baseUrl: string;
  currentParams?: Record<string, string | undefined>;
  onFilterChange?: (filters: Record<string, string>) => void;
  actionButton?: ReactNode;
}

export default function FilterBar({
  filters,
  baseUrl,
  currentParams = {},
  onFilterChange,
  actionButton
}: FilterBarProps) {
  const router = useRouter();
  const [filterValues, setFilterValues] = useState<Record<string, string>>({});

  // Initialize filter values from URL params
  useEffect(() => {
    const initialValues: Record<string, string> = {};
    filters.forEach(filter => {
      const paramValue = currentParams[filter.name];
      if (paramValue) {
        initialValues[filter.name] = paramValue;
      }
    });
    setFilterValues(initialValues);
  }, [currentParams, filters]);

  // Handle filter value changes
  const handleFilterChange = (name: string, value: string) => {
    setFilterValues(prev => ({
      ...prev,
      [name]: value || ''
    }));
  };

  // Apply filters
  const applyFilters = (e: FormEvent) => {
    e.preventDefault();
    
    // Build the URL with query parameters
    const params = new URLSearchParams();
    
    // Add page param if it exists
    if (currentParams.page) {
      params.set('page', '1'); // Reset to page 1 when filters change
    }
    
    // Add limit param if it exists
    if (currentParams.limit) {
      params.set('limit', currentParams.limit);
    }
    
    // Add filter values
    Object.entries(filterValues).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });
    
    // Navigate with the new params
    const newUrl = `${baseUrl}?${params.toString()}`;
    router.push(newUrl);
    
    // Call the onFilterChange callback if provided
    if (onFilterChange) {
      onFilterChange(filterValues);
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setFilterValues({});
    
    // Navigate with just the page and limit params
    const params = new URLSearchParams();
    if (currentParams.page) {
      params.set('page', '1');
    }
    if (currentParams.limit) {
      params.set('limit', currentParams.limit || '20');
    }
    
    const newUrl = `${baseUrl}?${params.toString()}`;
    router.push(newUrl);
    
    if (onFilterChange) {
      onFilterChange({});
    }
  };

  return (
    <div className="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <form id="filterbar-form" onSubmit={applyFilters} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {filters.map((filter) => (
            <div key={filter.name} className="space-y-1">
              <label htmlFor={filter.name} className="block mb-1 text-sm font-semibold text-gray-800">
                {filter.label}
              </label>
              
              {filter.type === 'select' && filter.options && (
                <select
                  id={filter.name}
                  name={filter.name}
                  value={filterValues[filter.name] || ''}
                  onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                  className="block w-full px-4 py-2 text-base text-gray-800 transition-all duration-150 border border-gray-300 rounded-lg shadow-sm h-14 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-gray-50"
                >
                  <option value="">Semua</option>
                  {filter.options.map((option) => (
                    <option key={option.id} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
              
              {filter.type === 'date' && (
                <input
                  type="date"
                  id={filter.name}
                  name={filter.name}
                  value={filterValues[filter.name] || ''}
                  onChange={(e) => {
                    handleFilterChange(filter.name, e.target.value);
                    // Auto-submit form saat tanggal diubah
                    setTimeout(() => {
                      document.getElementById('filterbar-form')?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                    }, 0);
                  }}
                  className="block w-full px-4 py-2 text-base text-gray-800 transition-all duration-150 border border-gray-300 rounded-lg shadow-sm h-14 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-gray-50"
                />
              )}
              
              {filter.type === 'text' && (
                <input
                  type="text"
                  id={filter.name}
                  name={filter.name}
                  value={filterValues[filter.name] || ''}
                  onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                  placeholder={filter.placeholder || `Cari ${filter.label.toLowerCase()}...`}
                  className="block w-full px-4 py-2 text-base text-gray-800 transition-all duration-150 border border-gray-300 rounded-lg shadow-sm h-14 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 bg-gray-50"
                />
              )}
            </div>
          ))}
        </div>
        
        <div className="flex items-center justify-between mt-2 space-x-2">
          <div className="flex space-x-2">
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 text-base font-semibold text-white transition-all duration-150 bg-blue-600 border border-transparent rounded-lg shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
              Filter
            </button>
            
            <button
              type="button"
              onClick={resetFilters}
              className="inline-flex items-center px-4 py-2 text-base font-semibold text-gray-700 transition-all duration-150 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset
            </button>
          </div>
          
          {actionButton && (
            <div>
              {actionButton}
            </div>
          )}
        </div>
      </form>
    </div>
  );
}