// File: /components/InputField.tsx
import React, { InputHTMLAttributes } from 'react';

interface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  name: string;
  error?: string;
  type?: string;
  required?: boolean;
  fullWidth?: boolean;
  labelSize?: 'sm' | 'md' | 'lg';
}

export default function InputField({
  label,
  name,
  error,
  type = 'text',
  required = false,
  fullWidth = true,
  labelSize = 'md',
  className = '',
  ...props
}: InputFieldProps) {
  // Label size classes
  const labelSizes = {
    sm: 'text-xs sm:text-sm',
    md: 'text-sm sm:text-base',
    lg: 'text-base sm:text-lg'
  };

  return (
    <div className={`mb-3 sm:mb-4 ${fullWidth ? 'w-full' : ''}`}>
      <label
        htmlFor={name}
        className={`block ${labelSizes[labelSize]} font-medium text-gray-700 mb-1`}
      >
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        id={name}
        name={name}
        type={type}
        className={`w-full px-2 sm:px-3 py-1.5 sm:py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${className}`}
        required={required}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-red-600 sm:text-sm">{error}</p>}
    </div>
  );
}