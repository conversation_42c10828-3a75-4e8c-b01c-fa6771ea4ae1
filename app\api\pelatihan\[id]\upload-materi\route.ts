'use server';

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { writeFile, chmod } from 'fs/promises';
import path from 'path';
import { mkdir } from 'fs/promises';
import { withCsrfProtection } from '@/lib/csrfMiddleware';

// POST /api/pelatihan/[id]/upload-materi - Mengunggah file materi pelatihan
export const POST = withCsrfProtection(async (request: Request | NextRequest, context: { params: Promise<{ id: string }> }) => {
  const params = await context.params;
  try {
    const { id } = params;
    
    // Autentikasi user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Cek apakah pelatihan ada
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { id },
    });

    if (!pelatihan) {
      return NextResponse.json(
        { success: false, message: 'Kegiatan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Periksa otorisasi
    const isAdmin = user.role === 'ADMIN';
    if (!isAdmin && pelatihan.userId !== user.id) {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses untuk mengupload materi pada pelatihan ini' },
        { status: 403 }
      );
    }

    // Proses FormData untuk file upload
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File tidak ditemukan' },
        { status: 400 }
      );
    }
    
    // Validasi file
    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { success: false, message: 'Hanya file PDF yang diperbolehkan' },
        { status: 400 }
      );
    }
    
    if (file.size > 2 * 1024 * 1024) { // 2MB
      return NextResponse.json(
        { success: false, message: 'Ukuran file melebihi batas 2MB' },
        { status: 400 }
      );
    }
    
    // Simpan file ke server (buat path unik)
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Buat nama file unik
    const fileName = file.name;
    const uniqueFileName = `${Date.now()}-${fileName.replace(/\s+/g, '_')}`;
    const filePath = `/uploads/materi/${uniqueFileName}`;
    const publicDir = path.join(process.cwd(), 'public');
    const uploadsDir = path.join(publicDir, 'uploads', 'materi');
    const fullPath = path.join(publicDir, 'uploads', 'materi', uniqueFileName);
    
    // Pastikan direktori ada
    try {
      await mkdir(uploadsDir, { recursive: true });
    } catch (err) {
      console.error('Error creating directory:', err);
    }
      // Tulis file
    await writeFile(fullPath, buffer);
    
    // Set proper file permissions (644 - rw-r--r--)
    await chmod(fullPath, 0o644);

    // Buat materi baru di database
    const materiPelatihan = await prisma.materi_pelatihan.create({
      data: {
        nama_file: fileName,
        path_file: filePath,
        size: file.size,
        mime_type: file.type,
        pelatihanId: id,
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Materi berhasil diunggah',
      data: materiPelatihan
    }, { status: 201 });  } catch (error) {
    console.error('Error uploading materi:', error);
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan saat mengunggah materi' },
      { status: 500 }
    );
  }
});
