'use client';

import { useState } from 'react';
import Link from 'next/link';
import SimplePagination from './SimplePagination';
import { formatDateTime } from '../utils/helpers';

interface Absensi {
  id: string;
  nama: string;
  waktu: Date | string;
}

interface PaginatedAbsensiListProps {
  absensiList: Absensi[];
  itemsPerPage?: number;
}

export default function PaginatedAbsensiList({ 
  absensiList, 
  itemsPerPage = 10
}: PaginatedAbsensiListProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(absensiList.length / itemsPerPage);
  
  // Calculate the current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = absensiList.slice(indexOfFirstItem, indexOfLastItem);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">
        Daftar Absensi ({absensiList.length})
      </h2>
      
      {absensiList.length === 0 ? (
        <p className="text-sm text-gray-500">Belum ada absensi tercatat</p>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Nama</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Waktu</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-right text-gray-500 uppercase sm:px-3">Aksi</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentItems.map((absensi) => (
                  <tr key={absensi.id}>
                    <td className="px-2 sm:px-3 py-2 text-sm font-medium text-gray-900 truncate max-w-[120px] sm:max-w-full">
                      {absensi.nama}
                    </td>
                    <td className="px-2 py-2 text-sm text-gray-500 sm:px-3">
                      {formatDateTime(absensi.waktu)}
                    </td>
                    <td className="px-2 py-2 text-sm font-medium text-right sm:px-3">
                      <Link
                        href={`/dashboard/absensi/${absensi.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Detail
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="pt-4">
            <SimplePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </>
      )}
    </>
  );
}