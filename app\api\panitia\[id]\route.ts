import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

export async function GET(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const id = params.id;

    const panitia = await prisma.panitia.findUnique({
      where: { id },
      include: {
        pegawai: true,
        lokasi: true,
        pelatihan: {
          select: {
            nama: true,
            tgl_mulai: true,
            tgl_berakhir: true,
          }
        }
      }
    });

    if (!panitia) {
      return NextResponse.json({ error: 'Panitia tidak ditemukan' }, { status: 404 });
    }

    return NextResponse.json(panitia);
  } catch (error) {
    console.error('Error fetching panitia:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getCurrentUser();
    
    if (!user || !['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const id = params.id;
    const body = await request.json();
    const { 
      pegawaiId, 
      jabatan, 
      lokasiId,
      no_surat, 
      keterangan 
    } = body;

    // Validasi input
    if (!pegawaiId || !jabatan || !lokasiId) {
      return NextResponse.json({ error: 'Field yang wajib diisi belum lengkap' }, { status: 400 });
    }

    // Periksa apakah panitia ada
    const existingPanitia = await prisma.panitia.findUnique({
      where: { id },
      include: {
        pelatihan: {
          select: {
            id: true
          }
        }
      }
    });

    if (!existingPanitia) {
      return NextResponse.json({ error: 'Panitia tidak ditemukan' }, { status: 404 });
    }

    // Validasi enum jabatan
    const validJabatan = ['PENGARAH', 'PENANGGUNG_JAWAB', 'KETUA', 'ANGGOTA', 'MODERATOR', 'HOST'];
    if (!validJabatan.includes(jabatan)) {
      return NextResponse.json(
        { error: 'Jabatan tidak valid. Pilih dari: Pengarah, Penanggung Jawab, Ketua, Anggota, Moderator, atau Host' }, 
        { status: 400 }
      );
    }

    // Update panitia
    const panitia = await prisma.panitia.update({
      where: { id },
      data: {
        pegawaiId,
        jabatan,
        lokasiId,
        no_surat,
        keterangan,
      },
      include: {
        pegawai: true,
        lokasi: true,
        pelatihan: {
          select: {
            nama: true,
            tgl_mulai: true,
            tgl_berakhir: true
          }
        }
      }
    });

    return NextResponse.json(panitia);
  } catch (error) {
    console.error('Error updating panitia:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await getCurrentUser();
    
    if (!user || !['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const id = params.id;

    // Periksa apakah panitia ada
    const existingPanitia = await prisma.panitia.findUnique({
      where: { id },
    });

    if (!existingPanitia) {
      return NextResponse.json({ error: 'Panitia tidak ditemukan' }, { status: 404 });
    }

    // Hapus panitia
    await prisma.panitia.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Panitia berhasil dihapus' });
  } catch (error) {
    console.error('Error deleting panitia:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}