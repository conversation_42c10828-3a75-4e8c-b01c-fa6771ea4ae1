'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface DeleteResponse {
  success: boolean;
  message?: string;
}

interface DeleteButtonProps {
  id: string;
  onDelete?: (id: string) => Promise<DeleteResponse>;
  className?: string;
  variant?: 'text' | 'icon' | 'button';
}

export default function DeleteButton({
  id,
  onDelete,
  className = '',
  variant = 'text'
}: DeleteButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
   
    if (confirm('Apakah Anda yakin ingin menghapus pelatihan ini?')) {
      setIsDeleting(true);
      try {
        let result: DeleteResponse;
       
        if (onDelete) {
          result = await onDelete(id);
        } else {
          const { deletePelatihan } = await import('@/app/dashboard/pelatihan/actions');
          result = await delete<PERSON>elatihan(id);
        }
       
        console.log('Hasil penghapusan:', result);
       
        if (!result.success) {
          throw new Error(result.message || 'Gagal menghapus pelatihan');
        }
       
        router.refresh();
      } catch (error) {
        console.error('Error deleting:', error);
        alert(error instanceof Error ? error.message : 'Terjadi kesalahan saat menghapus data');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  // Base styles for different variants
  const baseStyles = {
    text: 'text-red-600 hover:text-red-900 text-xs sm:text-sm font-medium transition-colors duration-200',
    icon: 'text-red-600 hover:text-red-900 p-1 sm:p-1.5 rounded-md hover:bg-red-50 transition-all duration-200',
    button: 'bg-red-600 hover:bg-red-700 text-white px-2 sm:px-3 py-1 sm:py-1.5 rounded text-xs sm:text-sm font-medium shadow-sm hover:shadow transition-all duration-200'
  };

  return (
    <button
      type="button"
      onClick={handleDelete}
      disabled={isDeleting}
      className={`${baseStyles[variant]} ${isDeleting ? 'opacity-50 cursor-not-allowed' : ''} focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 ${className}`}
      aria-busy={isDeleting}
    >
      {isDeleting ? (
        <>
          {variant === 'icon' ? (
            <svg className="w-3 h-3 animate-spin sm:h-4 sm:w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
            </svg>
          ) : (
            <span className="inline-flex items-center">
              <svg className="animate-spin -ml-0.5 mr-1.5 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
              </svg>
              Menghapus...
            </span>
          )}
        </>
      ) : (
        <>
          {variant === 'icon' ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          ) : (
            'Hapus'
          )}
        </>
      )}
    </button>
  );
}