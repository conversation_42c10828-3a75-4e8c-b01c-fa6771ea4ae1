import Link from 'next/link';
import { formatDate } from '../../../../utils/helpers';
import DeleteButton from '../../../../components/DeleteButton';
import { deletePelatihan } from '../actions';

interface JenjangTarget {
  id: string;
  jenjang: string;
  target_peserta: number;
}

interface BiodataItem {
  id: string;
  jenjang: string | null;
}

interface AbsensiItem {
  id: string;
  nama: string;
  jenjang: string | null;
}

interface Pelatihan {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: Date;
  tgl_berakhir: Date;
  link_registrasi: string;
  link_absensi: string;
  peserta_kegiatan: 'INTERNAL' | 'EKSTERNAL' | 'KEDUANYA' | null;
  link_absensi_internal: string | null;
  link_absensi_eksternal: string | null;
  _count: {
    biodata: number;
    absensi: number;
  };
  jenjangTargets?: JenjangTarget[];
  biodata?: BiodataItem[];
  absensi?: AbsensiItem[];
}

interface PelatihanTableProps {
  pelatihan: Pelatihan[];
}

export default function PelatihanTable({ pelatihan }: PelatihanTableProps) {
  return (
    <div className='overflow-x-auto'>
      <table className='min-w-full divide-y divide-gray-200'>
        <thead className='bg-gray-50'>
          <tr>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Nama
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Tempat
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Tanggal
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Pendaftar
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Absensi
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Rekapitulasi Jenjang
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-6'>
              Link
            </th>
            <th className='px-4 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase sm:px-6'>
              Aksi
            </th>
          </tr>
        </thead>
        <tbody className='bg-white divide-y divide-gray-200'>
          {pelatihan.length === 0 ? (
            <tr>
              <td
                colSpan={8}
                className='px-4 py-4 text-sm text-center text-gray-500 sm:px-6'
              >
                Belum ada data kegiatan
              </td>
            </tr>
          ) : (
            pelatihan.map((item) => (
              <tr key={item.id}>
                <td className='px-4 py-4 text-sm font-medium text-gray-900 sm:px-6'>
                  {item.nama}
                </td>
                <td className='px-4 py-4 text-sm text-gray-500 sm:px-6'>
                  {item.tempat}
                </td>
                <td className='px-4 py-4 text-sm text-gray-500 sm:px-6 whitespace-nowrap'>
                  {formatDate(item.tgl_mulai)} -{' '}
                  {formatDate(item.tgl_berakhir)}
                </td>
                <td className='px-4 py-4 text-sm text-center text-gray-500 sm:px-6 whitespace-nowrap'>
                  {item._count.biodata}
                </td>
                <td className='px-4 py-4 text-sm text-center text-gray-500 sm:px-6 whitespace-nowrap'>
                  {item._count.absensi}
                </td>
                <td className='px-4 py-4 text-sm text-gray-500 sm:px-6'>
                  {item.jenjangTargets && item.jenjangTargets.length > 0 ? (
                    <div className="space-y-1">
                      {item.jenjangTargets.map((jenjang) => {
                        // Count registered participants for this specific jenjang
                        const pendaftarPerJenjang = item.biodata
                          ? item.biodata.filter(b => b.jenjang === jenjang.jenjang).length
                          : 0;
                        
                        return (
                          <div key={jenjang.id} className="flex justify-between">
                            <span>{jenjang.jenjang}:</span>
                            <span className="ml-2">
                              {pendaftarPerJenjang} / {jenjang.target_peserta}
                            </span>
                          </div>
                        );
                      })}
                    </div>                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </td>
                <td className='px-4 py-4 text-sm text-gray-500 sm:px-6 whitespace-nowrap'>
                  <div className='flex flex-col space-y-1'>
                    <Link
                      href={`/public-pages/biodata/${item.link_registrasi}`}
                      target='_blank'                      className='text-blue-600 hover:text-blue-900'
                    >
                      Link Biodata
                    </Link>
                    {/* Display attendance links based on participant type */}
                    {item.peserta_kegiatan === 'INTERNAL' && item.link_absensi_internal && (
                      <Link
                        href={`/public-pages/absensi/internal/${item.link_absensi_internal}`}
                        target='_blank'
                        className='text-indigo-600 hover:text-indigo-900'                      >
                        Link Absensi Internal
                      </Link>
                    )}
                    {item.peserta_kegiatan === 'EKSTERNAL' && item.link_absensi_eksternal && (
                      <Link
                        href={`/public-pages/absensi/eksternal/${item.link_absensi_eksternal}`}
                        target='_blank'
                        className='text-emerald-600 hover:text-emerald-900'
                      >
                        Link Absensi Eksternal                      </Link>
                    )}
                    {item.peserta_kegiatan === 'KEDUANYA' && (
                      <>
                        {item.link_absensi_internal && (
                          <Link
                            href={`/public-pages/absensi/internal/${item.link_absensi_internal}`}
                            target='_blank'
                            className='text-indigo-600 hover:text-indigo-900'
                          >
                            Link Absensi Internal
                          </Link>
                        )}
                        {item.link_absensi_eksternal && (
                          <Link
                            href={`/public-pages/absensi/eksternal/${item.link_absensi_eksternal}`}
                            target='_blank'
                            className='text-emerald-600 hover:text-emerald-900'
                          >
                            Link Absensi Eksternal                          </Link>
                        )}
                      </>
                    )}
                    {/* Fallback to old link if new system not set up */}
                    {!item.peserta_kegiatan && item.link_absensi && (
                      <Link
                        href={`/public-pages/absensi/${item.link_absensi}`}
                        target='_blank'
                        className='text-green-600 hover:text-green-900'
                      >
                        Link Absensi
                      </Link>
                    )}
                  </div>
                </td>
                <td className='px-4 py-4 text-sm font-medium text-right sm:px-6 whitespace-nowrap'>
                  <div className='flex justify-end space-x-2'>
                    <Link
                      href={`/dashboard/pelatihan/${item.id}`}
                      className='text-blue-600 hover:text-blue-900'
                    >
                      Detail
                    </Link>
                    <Link
                      href={`/dashboard/pelatihan/${item.id}/edit`}
                      className='text-blue-600 hover:text-blue-900'
                    >
                      Edit
                    </Link>
                    <DeleteButton id={item.id} onDelete={deletePelatihan} />
                  </div>
                </td>
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}