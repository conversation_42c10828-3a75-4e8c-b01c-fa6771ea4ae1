import { NextRequest, NextResponse } from 'next/server';
import { validateCsrfToken, CSRF } from './csrf';
import { headers } from 'next/headers';

/**
 * Middleware untuk memvalidasi CSRF token dari header atau body request
 */
export async function validateCsrfRequest(req: NextRequest): Promise<boolean | NextResponse> {
  // Gunakan await karena headers() mengembalikan Promise
  const headersList = await headers();
  
  // Cek apakah ada CSRF token di header
  const csrfToken = headersList.get(CSRF.headerName);
  
  // Jika ada token di header, validasi
  if (csrfToken) {
    const isValidCsrf = await validateCsrfToken(csrfToken);
    if (!isValidCsrf) {
      console.warn('CSRF validation failed for token from header');
      return NextResponse.json(
        { message: 'Invalid CSRF token' },
        { status: 403 }
      );
    }
    return true;
  }
  
  // Jika tidak ada di header, coba cek di body untuk POST requests
  if (req.method === 'POST') {
    try {
      // Clone request untuk membaca body
      const clonedReq = req.clone();
      const contentType = req.headers.get('content-type') || '';
      
      // Jika FormData (multipart/form-data)
      if (contentType.includes('multipart/form-data')) {
        const formData = await clonedReq.formData();
        const tokenFromForm = formData.get(CSRF.formFieldName);
        
        if (tokenFromForm) {
          const isValidCsrf = await validateCsrfToken(tokenFromForm.toString());
          if (!isValidCsrf) {
            console.warn('CSRF validation failed for token from FormData');
            return NextResponse.json(
              { message: 'Invalid CSRF token' },
              { status: 403 }
            );
          }
          return true;
        }
      } else {
        // Jika JSON body
        const body = await clonedReq.json();
        
        // Jika token ada di body, validasi
        if (body && body[CSRF.formFieldName]) {
          const isValidCsrf = await validateCsrfToken(body[CSRF.formFieldName]);
          if (!isValidCsrf) {
            console.warn('CSRF validation failed for token from request body');
            return NextResponse.json(
              { message: 'Invalid CSRF token' },
              { status: 403 }
            );
          }
          return true;
        }
      }
    } catch (error) {
      console.error('Error parsing request body for CSRF validation:', error);
    }
  }

  // Jika tidak ada token atau validasi gagal
  console.warn('No CSRF token found in request');
  return NextResponse.json(
    { message: 'CSRF token required' },
    { status: 403 }
  );
}

/**
 * Type untuk route handler Next.js dengan dukungan untuk params
 */
export type RouteContext<T = Record<string, string>> = {
  params: T;
};

/**
 * Type untuk route handler Next.js
 */
export type RouteHandler = <T = Record<string, string>>(
  req: Request | NextRequest,
  context: RouteContext<T>
) => Promise<NextResponse> | NextResponse;

/**
 * HOC untuk membungkus route handler dengan validasi CSRF
 */
export function withCsrfProtection<T = Record<string, string>>(
  handler: (req: Request | NextRequest, context: RouteContext<T>) => Promise<NextResponse> | NextResponse
): (req: Request | NextRequest, context: RouteContext<T>) => Promise<NextResponse> {
  return async (req: Request | NextRequest, context: RouteContext<T>) => {
    // Skip CSRF validation for GET, HEAD, OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes((req as Request).method)) {
      return handler(req, context);
    }
    
    // Validasi CSRF untuk request yang memodifikasi data
    const csrfResult = await validateCsrfRequest(req as NextRequest);
    
    if (csrfResult !== true) {
      return csrfResult as NextResponse;
    }
    
    // Lanjutkan ke handler asli jika validasi berhasil
    return handler(req, context);
  };
}