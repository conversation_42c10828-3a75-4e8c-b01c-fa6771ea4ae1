// File: /app/dashboard/pelatihan/[id]/page.tsx
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { getCurrentUser } from '../../../../lib/auth';
import { prisma } from '../../../../lib/prisma';
import { formatDate } from '../../../../utils/helpers';
import { getBaseUrl } from '../../../../lib/constants';
import CopyLinkButton from '../../../../components/CopyLinkButton';
import QRCodeDisplay from '../../../../components/QRCodeDisplay';
import PaginatedBiodataList from '../../../../components/PaginatedBiodataList';
import PaginatedAbsensiList from '../../../../components/PaginatedAbsensiList';
import MaterialListWrapper from '../../../../components/MaterialListWrapper';
import PanitiaList from '../../../../components/PanitiaList';

import type { absensi, biodata, pelatihan  } from '@prisma/client';

// Define the extended pelatihan type that includes relations
type MaterialPelatihan = {
  id: string;
  pelatihanId: string;
  nama_file: string;
  path_file: string;
  size: number;
  mime_type: string;
  createdAt: Date;
  updatedAt: Date;
};

interface PelatihanWithRelations extends Omit<pelatihan, 'peserta_kegiatan' | 'link_absensi_internal' | 'link_absensi_eksternal'> {
  biodata: biodata[];
  absensi: absensi[];
  materiPelatihan: MaterialPelatihan[];
  user: {
    name: string;
    email: string;
  };
  peserta_kegiatan: string | null;
  link_absensi_internal: string | null;
  link_absensi_eksternal: string | null;
}

export default async function PelatihanDetailPage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = await props.params;
  const user = await getCurrentUser();

  if (!user) {
    redirect('/login');
  }
  // Fetch pelatihan data with materials
  const pelatihan = await prisma.pelatihan.findUnique({
    where: { id: params.id },
    select: {
      id: true,
      nama: true,
      tempat: true,
      tgl_mulai: true,
      tgl_berakhir: true,
      jenjang: true,
      target_peserta: true,
      link_registrasi: true,
      link_absensi: true,
      peserta_kegiatan: true,
      link_absensi_internal: true,
      link_absensi_eksternal: true,
      userId: true,
      createdAt: true,
      updatedAt: true,
      biodata: true,
      absensi: true,
      materiPelatihan: true,
      user: {
        select: {
          name: true,
          email: true,
        },
      },
    },
  }) as PelatihanWithRelations | null;

  if (!pelatihan) {
    notFound();
  }

  // Debug log to see what materials are fetched
  console.log('Pelatihan materials:', pelatihan.materiPelatihan);

  // Cek apakah user adalah pemilik pelatihan atau admin
  const isOwner = pelatihan.userId === user.id;
  const isAdmin = user.role === 'ADMIN';
  const canEdit = isOwner || isAdmin;

  // Fetch related pelatihan records with same base ID (for multi-jenjang)
  // This assumes that related records have IDs in the format "baseId-1", "baseId-2", etc.
  const baseId = pelatihan.id.split('-')[0]; // Get the base ID without any suffix
  const relatedPelatihan = await prisma.pelatihan.findMany({
    where: {
      OR: [
        { id: { startsWith: `${baseId}-` } }, // Find records with IDs like baseId-1, baseId-2, etc.
        { id: baseId } // Include the original record too
      ],
      NOT: { id: pelatihan.id } // Exclude the current record
    }
  });

  // All jenjang options including the current one and related ones
  const allJenjangOptions = [
    { jenjang: pelatihan.jenjang, target_peserta: pelatihan.target_peserta }
  ];

  // Add related pelatihan jenjang/target combinations
  relatedPelatihan.forEach(related => {
    allJenjangOptions.push({
      jenjang: related.jenjang,
      target_peserta: related.target_peserta
    });
  });  // Use getBaseUrl() function to ensure we get the correct URL in all environments
  const baseUrl = getBaseUrl();
  const biodataLink = `${baseUrl}/public-pages/biodata/${pelatihan.link_registrasi}`;
  
  // Generate attendance links based on participant type
  const generateAttendanceLinks = () => {
    const links: { type: string; url: string; label: string; color: string }[] = [];
    
    if (pelatihan.peserta_kegiatan === 'INTERNAL' && pelatihan.link_absensi_internal) {
      links.push({
        type: 'internal',
        url: `${baseUrl}/public-pages/absensi/internal/${pelatihan.link_absensi_internal}`,
        label: 'Link Absensi Internal',
        color: 'indigo'
      });
    } else if (pelatihan.peserta_kegiatan === 'EKSTERNAL' && pelatihan.link_absensi_eksternal) {
      links.push({
        type: 'external',
        url: `${baseUrl}/public-pages/absensi/eksternal/${pelatihan.link_absensi_eksternal}`,
        label: 'Link Absensi Eksternal',
        color: 'emerald'
      });
    } else if (pelatihan.peserta_kegiatan === 'KEDUANYA') {
      if (pelatihan.link_absensi_internal) {
        links.push({
          type: 'internal',
          url: `${baseUrl}/public-pages/absensi/internal/${pelatihan.link_absensi_internal}`,
          label: 'Link Absensi Internal',
          color: 'indigo'
        });
      }
      if (pelatihan.link_absensi_eksternal) {
        links.push({
          type: 'external',
          url: `${baseUrl}/public-pages/absensi/eksternal/${pelatihan.link_absensi_eksternal}`,
          label: 'Link Absensi Eksternal',
          color: 'emerald'
        });
      }
    } else if (pelatihan.link_absensi) {
      // Fallback to legacy single attendance link
      links.push({
        type: 'legacy',
        url: `${baseUrl}/public-pages/absensi/${pelatihan.link_absensi}`,
        label: 'Link Absensi',
        color: 'green'
      });
    }
    
    return links;
  };  
  const attendanceLinks = generateAttendanceLinks();

  // Helper to convert jenjang enum to display text
  const getJenjangDisplay = (jenjang: string) => {
    const jenjangMap: Record<string, string> = {
      'PAUD': 'PAUD',
      'SD': 'Sekolah Dasar',
      'SMP': 'Sekolah Menengah Pertama',
      'SMA': 'Sekolah Menengah Atas',
      'BPMP': 'BPMP',
      'Dinas': 'Dinas',
      'Lainnya': 'Lainnya',
      'UMUM': 'Umum'
    };
    return jenjangMap[jenjang] || jenjang;
  };

  return (
    <div className="container px-4 py-4 mx-auto sm:px-6 lg:px-8">
      <div className="flex flex-col items-start justify-between gap-4 mb-4 sm:mb-6 sm:flex-row sm:items-center">
        <h1 className="text-xl font-semibold sm:text-2xl">Detail Pelatihan</h1>
        <div className="flex flex-col w-full gap-2 sm:flex-row sm:w-auto">
          {canEdit && (
            <Link
              href={`/dashboard/pelatihan/${pelatihan.id}/edit`}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Edit Kegiatan
            </Link>
          )}
          <Link
            href="/dashboard/pelatihan"
            className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors text-center"
          >
            Kembali
          </Link>
        </div>
      </div>

      <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
        <div className="p-4 sm:p-6">
          <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Informasi Kegiatan</h2>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Nama Kegiatan</p>
              <p className="text-sm font-medium break-words sm:text-base">{pelatihan.nama}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Tempat</p>
              <p className="text-sm font-medium break-words sm:text-base">{pelatihan.tempat}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Tanggal Mulai</p>
              <p className="text-sm font-medium sm:text-base">{formatDate(pelatihan.tgl_mulai)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Tanggal Berakhir</p>
              <p className="text-sm font-medium sm:text-base">{formatDate(pelatihan.tgl_berakhir)}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Pembuat</p>
              <p className="text-sm font-medium sm:text-base">{pelatihan.user.name}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Dibuat pada</p>
              <p className="text-sm font-medium sm:text-base">{formatDate(pelatihan.createdAt)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Jenjang dan Target Peserta Section */}
      <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
        <div className="p-4 sm:p-6">
          <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Jenjang dan Target Peserta</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Jenjang</th>
                  <th className="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Target Peserta</th>
                  <th className="px-3 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Peserta Terdaftar</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* Current pelatihan jenjang */}
                <tr className={pelatihan.id === params.id ? "bg-blue-50" : ""}>
                  <td className="px-3 py-4 text-sm text-gray-900">
                    {getJenjangDisplay(pelatihan.jenjang)}
                    {pelatihan.id === params.id && <span className="ml-2 text-xs font-medium text-blue-600">(Saat ini)</span>}
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-900">
                    {pelatihan.target_peserta} orang
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-900">
                    {pelatihan.biodata.length} orang
                  </td>
                </tr>

                {/* Related pelatihan jenjang */}
                {relatedPelatihan.map((related) => (
                  <tr key={related.id}>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      {getJenjangDisplay(related.jenjang)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      {related.target_peserta} orang
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      <Link
                        href={`/dashboard/pelatihan/${related.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Lihat detail
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Panitia Pelatihan Section */}
      <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
        <div className="p-4 sm:p-6">
          <PanitiaList pelatihanId={pelatihan.id} userRole={user.role} />        </div>
      </div>

      {/* Materi Pelatihan Section */}
      <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
        <div className="p-4 sm:p-6">
          <MaterialListWrapper 
            materials={pelatihan.materiPelatihan}
            itemsPerPage={5}
            canUpload={canEdit}
            canDelete={canEdit}
            pelatihanId={pelatihan.id}
          />
        </div>
      </div>

      <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
        <div className="p-4 sm:p-6">
          <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Link Pendaftaran & Absensi</h2>
            {/* Registration Link */}
          <div className="mb-6">
            <div>
              <p className="text-xs text-gray-500 sm:text-sm">Link Pendaftaran</p>
              <div className="flex flex-col items-center gap-2 mt-1 sm:flex-row sm:gap-0">
                <input
                  type="text"
                  readOnly
                  value={biodataLink}
                  className="w-full p-2 text-sm border border-gray-300 rounded-md sm:flex-1"
                />
                <CopyLinkButton link={biodataLink} />
              </div>
            </div>
          </div>

          {/* Dynamic Attendance Links */}
          <div className="mb-6">
            {attendanceLinks.length > 0 ? (
              <div className="space-y-4">
                {attendanceLinks.map((linkData, index) => (
                  <div key={index}>
                    <p className="text-xs text-gray-500 sm:text-sm">{linkData.label}</p>
                    <div className="flex flex-col items-center gap-2 mt-1 sm:flex-row sm:gap-0">
                      <input
                        type="text"
                        readOnly
                        value={linkData.url}
                        className="w-full p-2 text-sm border border-gray-300 rounded-md sm:flex-1"
                      />
                      <CopyLinkButton link={linkData.url} />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500 bg-gray-50 rounded-md">
                <p className="text-sm">Belum ada link absensi yang dikonfigurasi</p>
              </div>
            )}          </div>
          
          {/* QR Code Section */}
          <h3 className="mb-2 text-base font-medium text-gray-700">QR Code Links</h3>
          <p className="mb-3 text-sm text-gray-600">
            Gunakan kode QR berikut untuk memudahkan akses pendaftaran dan absensi. 
            Anda dapat mendownload dan mencetaknya untuk dipasang di lokasi kegiatan.
          </p>
          
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {/* Registration QR Code */}
            <QRCodeDisplay 
              value={biodataLink}
              title="QR Pendaftaran"
              url={biodataLink}
              size={140}
              className="p-3"
            />
              {/* Dynamic Attendance QR Codes */}
            {attendanceLinks.map((linkData, index) => (
              <QRCodeDisplay 
                key={index}
                value={linkData.url}
                title={`QR ${linkData.type === 'internal' ? 'Absensi Internal' : 
                              linkData.type === 'external' ? 'Absensi Eksternal' : 
                              'Absensi'}`}
                url={linkData.url}
                size={140}
                className="p-3"
              />
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 sm:gap-6">
        <div className="overflow-hidden bg-white rounded-lg shadow">
          <div className="p-4 sm:p-6">
            {/* Replace with PaginatedBiodataList component */}
            <PaginatedBiodataList 
              biodataList={pelatihan.biodata} 
              itemsPerPage={5} 
              totalTarget={pelatihan.target_peserta} 
            />
          </div>
        </div>

        <div className="overflow-hidden bg-white rounded-lg shadow">
          <div className="p-4 sm:p-6">
            {/* Replace with PaginatedAbsensiList component */}
            <PaginatedAbsensiList 
              absensiList={pelatihan.absensi}
              itemsPerPage={5}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
