'use client';

interface JenjangTargetInputProps {
  id: string;
  jenjang: string;
  target_peserta: number;
  isRequired: boolean;
  onChangeJenjang: (id: string, value: string) => void;
  onChangeTarget: (id: string, value: number) => void;
  onRemove: (id: string) => void;
  canRemove: boolean;
}

export default function JenjangTargetInput({
  id,
  jenjang,
  target_peserta,
  isRequired,
  onChangeJenjang,
  onChangeTarget,
  onRemove,
  canRemove
}: JenjangTargetInputProps) {
  return (
    <div className="p-4 mt-4 border border-gray-200 rounded-md">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Jenjang */}
        <div>
          <label 
            htmlFor={`jenjang-${id}`} 
            className="block text-sm font-medium text-gray-700"
          >
            Jenjang
          </label>
          <div className="relative">
            <select
              id={`jenjang-${id}`}
              name={`jenjang_${id}`}
              value={jenjang}
              onChange={(e) => onChangeJenjang(id, e.target.value)}
              required={isRequired}
              className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            >
              <option value="" disabled>Pilih Jenjang</option>
              <option value="PAUD">PAUD</option>
              <option value="SD">SD</option>
              <option value="SMP">SMP</option>
              <option value="SMA">SMA</option>
              <option value="BPMP">BPMP</option>
              <option value="DINAS">DINAS</option> {/* Ensure DINAS is capitalized correctly */}
              <option value="LAINNYA">LAINNYA</option>
              <option value="UMUM">UMUM</option>
            </select>
          </div>
        </div>

        {/* Target Peserta */}
        <div>
          <label 
            htmlFor={`target-${id}`} 
            className="block text-sm font-medium text-gray-700"
          >
            Target Peserta
          </label>
          <div className="flex items-center">
            <input
              type="number"
              id={`target-${id}`}
              name={`target_peserta_${id}`}
              value={target_peserta}
              onChange={(e) => onChangeTarget(id, parseInt(e.target.value) || 0)}
              required={isRequired}
              min="1"
              className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
            
            {canRemove && (
              <button
                type="button"
                onClick={() => onRemove(id)}
                className="p-2 mt-1 ml-2 text-red-600 hover:text-red-800"
                title="Hapus jenjang ini"
                aria-label="Hapus jenjang"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
