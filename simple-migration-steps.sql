-- Simple Step-by-Step Migration Script
-- Run these queries one by one in your MySQL interface

-- ========================================
-- STEP 1: Check what indexes currently exist
-- ========================================
SHOW INDEX FROM error_log;
SHOW INDEX FROM biodata;

-- ========================================
-- STEP 2: Check if training_venue table exists
-- ========================================
SHOW TABLES LIKE 'training_venue';

-- ========================================
-- STEP 3: Create training_venue table (only if it doesn't exist)
-- ========================================
CREATE TABLE IF NOT EXISTS `training_venue` (
  `id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nama` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `alamat` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` double NOT NULL,
  `longitude` double NOT NULL,
  `radius` int NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `training_venue_is_active_idx` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- STEP 4: Add indexes to error_log (run only if they don't exist)
-- ========================================

-- Check first if index exists, then run the appropriate command:

-- For error_log_level_idx:
-- ALTER TABLE `error_log` ADD KEY `error_log_level_idx` (`level`);

-- For error_log_userId_idx:
-- ALTER TABLE `error_log` ADD KEY `error_log_userId_idx` (`userId`);

-- For error_log_createdAt_idx:
-- ALTER TABLE `error_log` ADD KEY `error_log_createdAt_idx` (`createdAt`);

-- ========================================
-- STEP 5: Add unique constraints to biodata (run only if they don't exist)
-- ========================================

-- Check first if constraint exists, then run the appropriate command:

-- For email unique constraint:
-- ALTER TABLE `biodata` ADD UNIQUE KEY `Biodata_email_key` (`email`);

-- For nip unique constraint:
-- ALTER TABLE `biodata` ADD UNIQUE KEY `Biodata_nip_key` (`nip`);

-- ========================================
-- STEP 6: Verify changes
-- ========================================
SHOW INDEX FROM error_log;
SHOW INDEX FROM biodata;
DESCRIBE training_venue;
