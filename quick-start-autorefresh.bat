@echo off
setlocal enabledelayedexpansion

:: QUICK START - DEPLOY AUTO-REFRESH FIX IMMEDIATELY
:: One-click solution untuk fix masalah nginx reload

title Quick Start - Auto-Refresh Fix

echo.
echo ===============================================
echo    QUICK START - AUTO-REFRESH FIX
echo    Solusi Cepat untuk kegiatan.bpmpkaltim.id
echo ===============================================
echo.

echo [36m🎯 MASALAH:[0m
echo   Foto upload berhasil tapi perlu reload nginx manual
echo   untuk bisa diakses via URL
echo.
echo [36m🔧 SOLUSI:[0m
echo   Advanced real-time monitoring system yang otomatis
echo   fix permissions dan configure nginx tanpa cache
echo.

set /p quick_start="Deploy solusi sekarang? (y/n): "

if /i not "%quick_start%"=="y" (
    echo [33m[INFO][0m Quick start dibatalkan
    echo [36m[INFO][0m Gunakan management-center.bat untuk opsi lengkap
    pause
    exit /b 0
)

echo.
echo [35m[ACTION][0m Starting quick deployment...

:: Step 1: Upload dan deploy advanced fix
echo [36m[INFO][0m Step 1/3: Deploying advanced auto-refresh fix...

if not exist "%~dp0advanced-cloudpanel-autorefresh-fix.sh" (
    echo [31m[ERROR][0m Advanced fix script tidak ditemukan!
    echo [33m[INFO][0m Pastikan file advanced-cloudpanel-autorefresh-fix.sh ada
    pause
    exit /b 1
)

:: Upload script
scp "%~dp0advanced-cloudpanel-autorefresh-fix.sh" root@*************:/tmp/

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Upload failed!
    pause
    exit /b 1
)

:: Execute script
ssh root@************* "chmod +x /tmp/advanced-cloudpanel-autorefresh-fix.sh && /tmp/advanced-cloudpanel-autorefresh-fix.sh"

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Deployment failed!
    echo [33m[INFO][0m Check VPS logs for details
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m ✅ Advanced fix deployed successfully!

:: Step 2: Upload monitoring tools
echo.
echo [36m[INFO][0m Step 2/3: Uploading monitoring tools...

for %%s in (monitor-autorefresh-status.sh troubleshoot-autorefresh.sh) do (
    if exist "%~dp0%%s" (
        scp "%~dp0%%s" root@*************:/tmp/
        ssh root@************* "chmod +x /tmp/%%s"
    )
)

echo [32m[SUCCESS][0m ✅ Monitoring tools uploaded!

:: Step 3: Quick system test
echo.
echo [36m[INFO][0m Step 3/3: Running quick system test...

:: Check services
ssh root@************* "systemctl is-active nginx" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Nginx: ACTIVE[0m
) else (
    echo [31m❌ Nginx: INACTIVE[0m
)

ssh root@************* "systemctl is-active advanced-photo-monitor" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Photo Monitor: ACTIVE[0m
) else (
    echo [31m❌ Photo Monitor: INACTIVE[0m
)

:: Test upload directory
ssh root@************* "ls -la /home/<USER>/htdocs/public/uploads/ | head -5" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Upload Directory: ACCESSIBLE[0m
) else (
    echo [31m❌ Upload Directory: NOT FOUND[0m
)

echo.
echo [32m[SUCCESS][0m ✅ QUICK DEPLOYMENT COMPLETED!
echo.

echo ===============================================
echo    🎉 FOTO UPLOAD SEKARANG REAL-TIME!
echo ===============================================
echo.

echo [33m🧪 TEST SEKARANG:[0m
echo   1. Buka: https://kegiatan.bpmpkaltim.id
echo   2. Login dan upload foto baru
echo   3. Langsung buka detail absensi
echo   4. Foto harus muncul TANPA reload nginx!
echo.

set /p open_app="Buka aplikasi untuk test? (y/n): "

if /i "%open_app%"=="y" (
    echo [35m[ACTION][0m Opening application...
    start "" "https://kegiatan.bpmpkaltim.id"
    
    echo [35m[ACTION][0m Starting real-time monitoring...
    start "" cmd /c "title Photo Monitor && ssh root@************* tail -f /var/log/advanced-photo-monitor.log"
    
    echo [36m[INFO][0m Application dan monitoring telah dibuka
)

echo.
echo [33m📊 MONITORING COMMANDS:[0m
echo   Real-time logs: ssh root@************* "tail -f /var/log/advanced-photo-monitor.log"
echo   Service status: ssh root@************* "systemctl status advanced-photo-monitor"
echo   Dashboard: ssh root@************* "/tmp/monitor-autorefresh-status.sh"
echo.

echo [33m🔧 MANAGEMENT:[0m
echo   Gunakan management-center.bat untuk kontrol lengkap
echo   Troubleshoot: troubleshoot-autorefresh.bat
echo.

echo [33m⚠️ JIKA MASIH ADA MASALAH:[0m
echo   1. Restart monitor: ssh root@************* "systemctl restart advanced-photo-monitor"
echo   2. Reload nginx: ssh root@************* "systemctl reload nginx"
echo   3. Check logs: ssh root@************* "journalctl -u advanced-photo-monitor -f"
echo.

echo [32m🎯 QUICK TEST RESULT:[0m
set /p test_result="Apakah foto upload sudah real-time? (y/n): "

if /i "%test_result%"=="y" (
    echo [32m[SUCCESS][0m ✅ PERFECT! Auto-refresh fix berhasil!
    echo [36m[INFO][0m Foto upload sekarang langsung accessible tanpa reload
) else (
    echo [33m[WARNING][0m ⚠️ Masih ada masalah. Running troubleshooter...
    
    if exist "%~dp0troubleshoot-autorefresh.bat" (
        call "%~dp0troubleshoot-autorefresh.bat"
    ) else (
        echo [36m[INFO][0m Manual troubleshooting:
        echo   ssh root@************* "/tmp/troubleshoot-autorefresh.sh"
    )
)

echo.
echo [36m[INFO][0m Quick start completed. Use management-center.bat for advanced control.
echo.
pause
