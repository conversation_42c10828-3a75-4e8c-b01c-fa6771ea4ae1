'use client';

import Link from 'next/link';
import { use } from 'react';
import UserDetail from './UserDetail';

interface _UserData {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export default function UserDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  
  return (
    <div className="max-w-4xl p-6 mx-auto bg-white rounded-lg shadow-md">
      <div className="pb-4 mb-6 border-b">
        <h1 className="text-2xl font-semibold text-gray-800">Detail Pengguna</h1>
        <p className="text-sm text-gray-500">ID: {resolvedParams.id}</p>
      </div>
      
      <UserDetail userId={resolvedParams.id} />
      
      <div className="flex justify-end mt-6 space-x-3">
        <Link 
          href={`/dashboard/users/${resolvedParams.id}/edit`}
          className="px-4 py-2 text-white transition-colors bg-blue-600 rounded-md hover:bg-blue-700"
        >
          Edit Pengguna
        </Link>
        <Link 
          href="/dashboard/users"
          className="px-4 py-2 text-gray-700 transition-colors bg-gray-200 rounded-md hover:bg-gray-300"
        >
          Kembali ke Daftar
        </Link>
      </div>
    </div>
  );
}
