#!/bin/bash

# CloudPanel Real-time Photo Monitor Status Checker
# Script untuk monitoring status real-time photo fix

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

clear_screen() {
    clear
    echo -e "${BOLD}${CYAN}"
    echo "=========================================="
    echo "  CLOUDPANEL REALTIME PHOTO MONITORING"
    echo "=========================================="
    echo -e "${NC}"
}

check_services() {
    echo -e "${BOLD}${BLUE}🔧 SERVICES STATUS:${NC}"
    
    # Real-time Photo Monitor
    if systemctl is-active --quiet realtime-photo-monitor.service 2>/dev/null; then
        echo -e "  • Real-time Monitor: ${GREEN}✅ ACTIVE${NC}"
        
        # Service uptime
        UPTIME=$(systemctl show realtime-photo-monitor.service --property=ActiveEnterTimestamp --value)
        if [ -n "$UPTIME" ]; then
            echo -e "    Uptime: ${CYAN}$(systemctl show realtime-photo-monitor.service --property=StatusText --value)${NC}"
        fi
    else
        echo -e "  • Real-time Monitor: ${RED}❌ INACTIVE${NC}"
        echo -e "    ${YELLOW}⚠️ Service not running - photos may need nginx reload${NC}"
    fi
    
    # Nginx
    if systemctl is-active --quiet nginx; then
        echo -e "  • Nginx: ${GREEN}✅ ACTIVE${NC}"
    else
        echo -e "  • Nginx: ${RED}❌ INACTIVE${NC}"
    fi
    
    # Next.js App detection
    if netstat -tlnp 2>/dev/null | grep -q ":3000"; then
        echo -e "  • Next.js App: ${GREEN}✅ RUNNING (Port 3000)${NC}"
    elif pgrep -f "next" >/dev/null 2>&1; then
        echo -e "  • Next.js App: ${GREEN}✅ RUNNING${NC}"
    else
        echo -e "  • Next.js App: ${YELLOW}⚠️ NOT DETECTED${NC}"
    fi
    
    echo ""
}

show_realtime_stats() {
    echo -e "${BOLD}${PURPLE}📊 REAL-TIME MONITORING STATS:${NC}"
    
    # Find upload directories
    UPLOAD_DIRS=()
    while IFS= read -r -d '' dir; do
        if [ -d "$dir" ]; then
            UPLOAD_DIRS+=("$dir")
        fi
    done < <(find /home -path "*/htdocs/public/uploads" -type d -print0 2>/dev/null || true)
    
    if [ ${#UPLOAD_DIRS[@]} -eq 0 ]; then
        echo -e "  ${YELLOW}⚠️ No CloudPanel upload directories found${NC}"
        return
    fi
    
    for upload_dir in "${UPLOAD_DIRS[@]}"; do
        DOMAIN=$(basename "$(dirname "$(dirname "$(dirname "$upload_dir")")")")
        
        echo -e "  📂 ${CYAN}Domain: $DOMAIN${NC}"
        echo -e "     Upload Dir: ${BLUE}$upload_dir${NC}"
        
        if [ -d "$upload_dir" ]; then
            # File statistics
            TOTAL_PHOTOS=$(find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" 2>/dev/null | wc -l)
            RECENT_PHOTOS=$(find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -o -name "*.webp" -mtime -1 2>/dev/null | wc -l)
            DIR_SIZE=$(du -sh "$upload_dir" 2>/dev/null | cut -f1 || echo "0")
            
            echo -e "     Total Photos: ${GREEN}$TOTAL_PHOTOS${NC}"
            echo -e "     Recent (24h): ${YELLOW}$RECENT_PHOTOS${NC}"
            echo -e "     Directory Size: ${BLUE}$DIR_SIZE${NC}"
            
            # Permission check
            DIR_OWNER=$(stat -c '%U:%G' "$upload_dir" 2>/dev/null || echo "unknown")
            echo -e "     Permissions: ${CYAN}$DIR_OWNER${NC}"
        fi
        echo ""
    done
}

show_monitoring_logs() {
    echo -e "${BOLD}${GREEN}📋 REAL-TIME MONITORING LOGS:${NC}"
    
    if [ -f "/var/log/realtime-photo-monitor.log" ]; then
        echo -e "${CYAN}Recent monitoring events:${NC}"
        
        # Count events in last hour
        RECENT_EVENTS=$(grep "$(date '+%Y-%m-%d %H')" /var/log/realtime-photo-monitor.log 2>/dev/null | wc -l)
        echo -e "  Events last hour: ${BLUE}$RECENT_EVENTS${NC}"
        
        # Show last 5 events with color coding
        echo -e "\n${CYAN}Last 5 events:${NC}"
        tail -5 /var/log/realtime-photo-monitor.log 2>/dev/null | while read line; do
            if [[ "$line" =~ SUCCESS ]]; then
                echo -e "  ${GREEN}✅${NC} $line"
            elif [[ "$line" =~ WARNING ]]; then
                echo -e "  ${YELLOW}⚠️${NC} $line"
            elif [[ "$line" =~ ERROR ]]; then
                echo -e "  ${RED}❌${NC} $line"
            else
                echo -e "  ${BLUE}ℹ️${NC} $line"
            fi
        done
    else
        echo -e "  ${YELLOW}⚠️ Monitor log not found${NC}"
        echo -e "    Log should be at: /var/log/realtime-photo-monitor.log${NC}"
    fi
    echo ""
}

show_nginx_photo_stats() {
    echo -e "${BOLD}${CYAN}🌐 NGINX PHOTO ACCESS STATS:${NC}"
    
    if [ -f "/var/log/nginx/photo_realtime.log" ]; then
        # Recent requests
        RECENT_REQUESTS=$(tail -100 /var/log/nginx/photo_realtime.log 2>/dev/null | wc -l)
        SUCCESSFUL_200=$(tail -100 /var/log/nginx/photo_realtime.log 2>/dev/null | grep " 200 " | wc -l)
        NOT_FOUND_404=$(tail -100 /var/log/nginx/photo_realtime.log 2>/dev/null | grep " 404 " | wc -l)
        
        echo -e "  Recent Photo Requests: ${BLUE}$RECENT_REQUESTS${NC}"
        echo -e "  Successful (200): ${GREEN}$SUCCESSFUL_200${NC}"
        echo -e "  Not Found (404): ${RED}$NOT_FOUND_404${NC}"
        
        if [ $RECENT_REQUESTS -gt 0 ]; then
            SUCCESS_RATE=$(( (SUCCESSFUL_200 * 100) / RECENT_REQUESTS ))
            if [ $SUCCESS_RATE -ge 90 ]; then
                echo -e "  Success Rate: ${GREEN}$SUCCESS_RATE%${NC}"
            elif [ $SUCCESS_RATE -ge 70 ]; then
                echo -e "  Success Rate: ${YELLOW}$SUCCESS_RATE%${NC}"
            else
                echo -e "  Success Rate: ${RED}$SUCCESS_RATE%${NC}"
            fi
        fi
        
        # Check for real-time headers
        if tail -10 /var/log/nginx/photo_realtime.log 2>/dev/null | grep -q "realtime"; then
            echo -e "  Real-time Headers: ${GREEN}✅ ACTIVE${NC}"
        else
            echo -e "  Real-time Headers: ${YELLOW}⚠️ CHECK CONFIG${NC}"
        fi
        
    else
        echo -e "  ${YELLOW}⚠️ Nginx photo log not found${NC}"
        echo -e "    Log should be at: /var/log/nginx/photo_realtime.log${NC}"
    fi
    echo ""
}

test_realtime_functionality() {
    echo -e "${BOLD}${YELLOW}🧪 REAL-TIME FUNCTIONALITY TEST:${NC}"
    
    # Find first upload directory
    UPLOAD_DIR=""
    DOMAIN=""
    while IFS= read -r -d '' dir; do
        if [ -d "$dir" ]; then
            UPLOAD_DIR="$dir"
            DOMAIN=$(basename "$(dirname "$(dirname "$(dirname "$dir")")")")
            break
        fi
    done < <(find /home -path "*/htdocs/public/uploads" -type d -print0 2>/dev/null || true)
    
    if [ -z "$UPLOAD_DIR" ]; then
        echo -e "  ${RED}❌ No upload directory found for testing${NC}"
        return
    fi
    
    echo -e "  Testing with domain: ${CYAN}$DOMAIN${NC}"
    echo -e "  Upload directory: ${BLUE}$UPLOAD_DIR${NC}"
    
    # Create test file
    TEST_DIR="$UPLOAD_DIR/test-realtime-$(date +%s)"
    mkdir -p "$TEST_DIR"
    TEST_FILE="$TEST_DIR/test-$(date +%s).jpg"
    
    echo -e "  ${CYAN}Creating test photo...${NC}"
    
    # Create minimal JPEG
    printf '\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\x27 ($\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9' > "$TEST_FILE"
    
    if [ ! -f "$TEST_FILE" ]; then
        echo -e "  ${RED}❌ Failed to create test file${NC}"
        return
    fi
    
    # Wait for real-time processing
    echo -e "  ${CYAN}Waiting for real-time processing...${NC}"
    sleep 2
    
    # Test HTTP access
    RELATIVE_PATH="${TEST_FILE#$UPLOAD_DIR}"
    TEST_URL="http://localhost/uploads$RELATIVE_PATH"
    
    echo -e "  ${CYAN}Testing URL: $TEST_URL${NC}"
    
    # Test immediate access
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 5 2>/dev/null || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo -e "  ${GREEN}✅ SUCCESS: Photo accessible immediately!${NC}"
        echo -e "    HTTP Status: ${GREEN}$HTTP_STATUS${NC}"
        
        # Check cache headers
        CACHE_HEADERS=$(curl -s -I "$TEST_URL" --max-time 5 2>/dev/null | grep -i "cache-control\|pragma\|expires" || echo "")
        if [[ "$CACHE_HEADERS" =~ no-cache ]]; then
            echo -e "    Cache Headers: ${GREEN}✅ No-cache active${NC}"
        else
            echo -e "    Cache Headers: ${YELLOW}⚠️ May be cached${NC}"
        fi
        
        # Check real-time header
        REALTIME_HEADER=$(curl -s -I "$TEST_URL" --max-time 5 2>/dev/null | grep -i "x-photo-served" || echo "")
        if [[ "$REALTIME_HEADER" =~ realtime ]]; then
            echo -e "    Real-time Header: ${GREEN}✅ Active${NC}"
        fi
        
    else
        echo -e "  ${RED}❌ FAILED: Photo not immediately accessible${NC}"
        echo -e "    HTTP Status: ${RED}$HTTP_STATUS${NC}"
        echo -e "    ${YELLOW}This indicates the real-time fix is not working properly${NC}"
    fi
    
    # Cleanup
    rm -rf "$TEST_DIR" 2>/dev/null || true
    echo ""
}

show_system_resources() {
    echo -e "${BOLD}${PURPLE}💻 SYSTEM RESOURCES:${NC}"
    
    # Memory usage
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", ($3/$2) * 100.0}')
    echo -e "  Memory Usage: ${BLUE}$MEM_USAGE%${NC}"
    
    # inotify usage
    INOTIFY_WATCHES=$(find /proc/*/fd -lname anon_inode:inotify 2>/dev/null | wc -l)
    INOTIFY_LIMIT=$(cat /proc/sys/fs/inotify/max_user_watches 2>/dev/null || echo "unknown")
    echo -e "  inotify Watches: ${BLUE}$INOTIFY_WATCHES${NC} / ${CYAN}$INOTIFY_LIMIT${NC}"
    
    if [ "$INOTIFY_LIMIT" != "unknown" ] && [ $INOTIFY_WATCHES -gt $((INOTIFY_LIMIT * 80 / 100)) ]; then
        echo -e "    ${YELLOW}⚠️ High inotify usage${NC}"
    fi
    
    echo ""
}

show_quick_commands() {
    echo -e "${BOLD}${CYAN}🛠️ QUICK MANAGEMENT COMMANDS:${NC}"
    echo -e "  Restart monitor: ${YELLOW}systemctl restart realtime-photo-monitor${NC}"
    echo -e "  Check logs: ${YELLOW}tail -f /var/log/realtime-photo-monitor.log${NC}"
    echo -e "  Nginx photo logs: ${YELLOW}tail -f /var/log/nginx/photo_realtime.log${NC}"
    echo -e "  Service status: ${YELLOW}systemctl status realtime-photo-monitor${NC}"
    echo -e "  Test nginx config: ${YELLOW}nginx -t${NC}"
    echo ""
}

# Main monitoring function
main_monitor() {
    local mode="$1"
    
    if [ "$mode" = "continuous" ]; then
        while true; do
            clear_screen
            check_services
            show_realtime_stats
            show_monitoring_logs
            show_nginx_photo_stats
            show_system_resources
            
            echo -e "${BOLD}${CYAN}🔄 Auto-refresh in 10 seconds... (Press Ctrl+C to exit)${NC}"
            sleep 10
        done
    else
        clear_screen
        check_services
        show_realtime_stats
        show_monitoring_logs
        show_nginx_photo_stats
        
        if [ "$mode" = "test" ]; then
            test_realtime_functionality
        fi
        
        show_system_resources
        show_quick_commands
    fi
}

# Usage
show_usage() {
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  -c, --continuous    Run continuous monitoring (auto-refresh every 10s)"
    echo "  -t, --test         Run single check with real-time test"
    echo "  -s, --single       Run single check only (default)"
    echo "  -h, --help         Show this help"
    echo ""
}

# Command line options
case "${1:-}" in
    -c|--continuous)
        main_monitor "continuous"
        ;;
    -t|--test)
        main_monitor "test"
        ;;
    -s|--single|"")
        main_monitor "single"
        ;;
    -h|--help)
        show_usage
        ;;
    *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac

echo -e "${BOLD}${GREEN}Monitoring completed.${NC}"
