/**
 * Helper untuk mendeteksi apakah kode berjalan di middleware
 */
export function isRunningInMiddleware(): boolean {
  // Middleware tidak memiliki window
  if (typeof window !== 'undefined') {
    return false;
  }
  
  // Middleware tidak memiliki process.browser
  if (typeof process !== 'undefined' && process.browser) {
    return false;
  }
  
  // Middleware tidak memiliki document
  if (typeof document !== 'undefined') {
    return false;
  }
  
  // Middleware tidak memiliki fetch yang berfungsi penuh
  try {
    // Jika fetch gagal atau tidak tersedia, kemungkinan kita di middleware
    if (!globalThis.fetch) {
      return true;
    }
  } catch (_e) {
    return true;
  }
  
  // Cek stack trace untuk petunjuk middleware
  const stack = new Error().stack || '';
  return stack.includes('middleware') || stack.includes('edge');
}

export default isRunningInMiddleware;