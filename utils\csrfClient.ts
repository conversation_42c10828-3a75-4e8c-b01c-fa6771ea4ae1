'use client';

// Constants for CSRF token handling
export const CSRF = {
  cookieName: 'csrf_token',
  headerName: 'X-CSRF-Token',
  formFieldName: 'csrfToken',
};

/**
 * Gets the CSRF token from cookies on the client side
 */
export function getCsrfTokenFromCookie(): string | null {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const csrfCookie = cookies
    .find(cookie => cookie.trim().startsWith(`${CSRF.cookieName}=`));
  
  if (csrfCookie) {
    return csrfCookie.split('=')[1];
  }
  
  return null;
}

/**
 * Fetches a CSRF token from the server
 */
export async function fetchCsrfToken(): Promise<string> {
  try {
    // First check if we already have one in cookies
    const existingToken = getCsrfTokenFromCookie();
    if (existingToken) return existingToken;
    
    // Otherwise request a new one from the server
    const response = await fetch('/api/auth/csrf', {
      method: 'GET',
      credentials: 'include',
    });
    
    if (!response.ok) {
      throw new Error('Failed to get CSRF token');
    }
    
    const data = await response.json();
    return data.csrfToken;  } catch (error) {
    throw error;
  }
}

/**
 * CSRF-protected fetch function for client-side use
 */
export async function csrfFetch(url: string, options: RequestInit = {}): Promise<Response> {
  try {
    // Only add CSRF for non-GET methods
    if (options.method && options.method !== 'GET') {
      const csrfToken = await fetchCsrfToken();
      
      // Create new headers object
      const headers = new Headers(options.headers || {});
      
      // Add CSRF token to headers
      headers.set(CSRF.headerName, csrfToken);
      
      // If JSON body, add CSRF token to body as well
      if (headers.get('Content-Type') === 'application/json' && options.body) {
        try {
          const bodyObj = JSON.parse(options.body.toString());
          bodyObj[CSRF.formFieldName] = csrfToken;
          options.body = JSON.stringify(bodyObj);
        } catch (_e) {
          // Not a JSON body, continue
        }
      }
      
      // If FormData, add CSRF token to FormData
      if (options.body instanceof FormData) {
        options.body.append(CSRF.formFieldName, csrfToken);
      }
      
      options.headers = headers;
    }
    
    return fetch(url, {
      ...options,
      credentials: 'include', // Always send cookies
    });  } catch (error) {
    throw error;
  }
}

export default csrfFetch;