'use client';

import React from 'react';
import Image from 'next/image';

interface ProfilePhotoProps {
  src: string | null;
  alt: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ProfilePhoto: React.FC<ProfilePhotoProps> = ({
  src,
  alt,
  size = 'md',
  className = '',
}) => {
  // Default fallback image
  const fallbackSrc = '/globe.svg';
  
  // Size definitions
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-20 h-20',
  };
  
  const sizeClass = sizeClasses[size];
  
  return (
    <div className={`relative rounded-full overflow-hidden bg-gray-100 ${sizeClass} ${className}`}>
      <Image
        src={src || fallbackSrc}
        alt={alt}
        fill
        sizes="(max-width: 768px) 100vw, 33vw"
        className="object-cover"
        onError={(e) => {
          // If image fails to load, use fallback
          const target = e.target as HTMLImageElement;
          target.src = fallbackSrc;
        }}
      />
    </div>
  );
};

export default ProfilePhoto;
