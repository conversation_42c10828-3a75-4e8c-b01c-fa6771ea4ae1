'use client';

import { useState } from 'react';
import Link from 'next/link';
import SimplePagination from './SimplePagination';
import ProfilePhoto from './ProfilePhoto';

interface Biodata {
  id: string;
  nama: string;
  unit_kerja: string;
  foto_path?: string | null;
}

interface PaginatedBiodataListProps {
  biodataList: Biodata[];
  itemsPerPage?: number;
  totalTarget?: number;
}

export default function PaginatedBiodataList({ 
  biodataList, 
  itemsPerPage = 10, 
  totalTarget = 0 
}: PaginatedBiodataListProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const totalPages = Math.ceil(biodataList.length / itemsPerPage);
  
  // Calculate the current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = biodataList.slice(indexOfFirstItem, indexOfLastItem);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <>
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <h2 className="text-base font-medium sm:text-lg">
          Daftar Peserta ({biodataList.length})
        </h2>
        <div className="text-sm text-gray-500">
          {biodataList.length} dari {totalTarget} peserta
        </div>
      </div>
        {biodataList.length === 0 ? (
        <p className="text-sm text-gray-500">Belum ada peserta terdaftar</p>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3 w-10"></th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Nama</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Unit Kerja</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-right text-gray-500 uppercase sm:px-3">Aksi</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentItems.map((peserta) => (
                  <tr key={peserta.id}>
                    <td className="px-2 sm:px-3 py-2">
                      <ProfilePhoto 
                        src={peserta.foto_path || null}
                        alt={peserta.nama}
                        size="sm"
                      />
                    </td>
                    <td className="px-2 sm:px-3 py-2 text-sm font-medium text-gray-900 truncate max-w-[120px] sm:max-w-full">
                      {peserta.nama}
                    </td>
                    <td className="px-2 sm:px-3 py-2 text-sm text-gray-500 truncate max-w-[120px] sm:max-w-full">
                      {peserta.unit_kerja}
                    </td>
                    <td className="px-2 py-2 text-sm font-medium text-right sm:px-3">
                      <Link
                        href={`/dashboard/biodata/${peserta.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Detail
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="pt-4">
            <SimplePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </>
      )}
    </>
  );
}