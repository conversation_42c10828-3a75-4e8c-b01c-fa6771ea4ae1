-- Migration: Add photo support to absensi table
-- This script adds foto_path and foto_url columns to the absensi table

ALTER TABLE `absensi` 
ADD COLUMN `foto_path` VARCHAR(500) NULL COMMENT 'Path to attendance photo file',
ADD COLUMN `foto_url` VARCHAR(500) NULL COMMENT 'URL to access attendance photo';

-- Add indexes for better performance
CREATE INDEX `idx_absensi_foto_path` ON `absensi`(`foto_path`);

-- Update table comment
ALTER TABLE `absensi` COMMENT = 'Table for storing attendance records with photo support';
