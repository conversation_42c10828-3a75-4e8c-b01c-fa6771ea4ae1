'use client';

import { useState, useEffect } from 'react';
import { Card } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';

// Dynamic import for Map component
const MapPicker = dynamic(
  () => import('../../../components/MapPicker'),
  { 
    ssr: false,
    loading: () => <div className="h-[300px] bg-gray-100 animate-pulse flex items-center justify-center">Memuat peta...</div>
  }
);

interface TrainingVenue {
  id: string;
  nama: string;
  alamat: string;
  latitude: number;
  longitude: number;
  radius: number;
  is_active: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function VenueManagementPage() {
  const [venues, setVenues] = useState<TrainingVenue[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingVenue, setEditingVenue] = useState<TrainingVenue | null>(null);
  const [showForm, setShowForm] = useState(false);
  
  const [formData, setFormData] = useState({
    nama: '',
    alamat: '',
    latitude: 0,
    longitude: 0,
    radius: 150 // Default 150 meters
  });

  useEffect(() => {
    loadVenues();
  }, []);

  const loadVenues = async () => {
    try {
      const response = await fetch('/api/dashboard/training-venues');
      const data = await response.json();
      
      if (data.success) {
        setVenues(data.venues);
      } else {
        toast.error('Gagal memuat data venue');
      }
    } catch (error) {
      console.error('Error loading venues:', error);
      toast.error('Terjadi kesalahan saat memuat data venue');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.nama || !formData.alamat || !formData.latitude || !formData.longitude) {
      toast.error('Semua field harus diisi');
      return;
    }

    setIsSubmitting(true);

    try {
      const url = editingVenue 
        ? `/api/dashboard/training-venues/${editingVenue.id}`
        : '/api/dashboard/training-venues';
      
      const method = editingVenue ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        toast.success(editingVenue ? 'Venue berhasil diupdate' : 'Venue berhasil ditambahkan');
        resetForm();
        loadVenues();
      } else {
        toast.error(data.error || 'Gagal menyimpan venue');
      }
    } catch (error) {
      console.error('Error saving venue:', error);
      toast.error('Terjadi kesalahan saat menyimpan venue');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLocationSelect = (lat: number, lng: number, address: string) => {
    setFormData(prev => ({
      ...prev,
      latitude: lat,
      longitude: lng,
      alamat: address
    }));
  };

  const handleEdit = (venue: TrainingVenue) => {
    setEditingVenue(venue);
    setFormData({
      nama: venue.nama,
      alamat: venue.alamat,
      latitude: venue.latitude,
      longitude: venue.longitude,
      radius: venue.radius
    });
    setShowForm(true);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus venue ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/dashboard/training-venues/${id}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Venue berhasil dihapus');
        loadVenues();
      } else {
        toast.error(data.error || 'Gagal menghapus venue');
      }
    } catch (error) {
      console.error('Error deleting venue:', error);
      toast.error('Terjadi kesalahan saat menghapus venue');
    }
  };

  const handleToggleActive = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/dashboard/training-venues/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ is_active: !isActive })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Venue berhasil ${!isActive ? 'diaktifkan' : 'dinonaktifkan'}`);
        loadVenues();
      } else {
        toast.error(data.error || 'Gagal mengubah status venue');
      }
    } catch (error) {
      console.error('Error toggling venue status:', error);
      toast.error('Terjadi kesalahan saat mengubah status venue');
    }
  };

  const resetForm = () => {
    setFormData({
      nama: '',
      alamat: '',
      latitude: 0,
      longitude: 0,
      radius: 150
    });
    setEditingVenue(null);
    setShowForm(false);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="w-1/4 h-8 mb-4 bg-gray-200 rounded"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Manajemen Venue Pelatihan
          </h1>          <p className="text-gray-600">
            Kelola lokasi venue untuk pelatihan eksternal dan dokumentasi
          </p>
        </div>
        <Button
          onClick={() => setShowForm(!showForm)}
          className="bg-indigo-600 hover:bg-indigo-700"
        >
          {showForm ? 'Batal' : 'Tambah Venue'}
        </Button>
      </div>

      {/* Form Add/Edit Venue */}
      {showForm && (
        <Card className="p-6">
          <h2 className="mb-4 text-lg font-semibold">
            {editingVenue ? 'Edit Venue' : 'Tambah Venue Baru'}
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="nama">Nama Venue *</Label>
                <Input
                  id="nama"
                  value={formData.nama}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, nama: e.target.value }))}
                  placeholder="Contoh: BPMP Kaltim"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="radius">Radius Geofence (meter) *</Label>
                <Input
                  id="radius"
                  type="number"
                  min="50"
                  max="500"
                  value={formData.radius}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, radius: parseInt(e.target.value) }))}
                  placeholder="150"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="alamat">Alamat *</Label>
              <Input
                id="alamat"
                value={formData.alamat}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFormData(prev => ({ ...prev, alamat: e.target.value }))}
                placeholder="Alamat lengkap venue"
                required
              />
            </div>

            <div>
              <Label>Pilih Lokasi pada Peta *</Label>
              <div className="overflow-hidden border rounded-lg">
                <MapPicker onLocationSelect={handleLocationSelect} />
              </div>
              {formData.latitude && formData.longitude && (
                <p className="mt-2 text-sm text-green-600">
                  ✓ Koordinat: {formData.latitude.toFixed(6)}, {formData.longitude.toFixed(6)}
                </p>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                {isSubmitting ? 'Menyimpan...' : editingVenue ? 'Update Venue' : 'Simpan Venue'}
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
              >
                Batal
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Venues List */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Daftar Venue ({venues.length})</h2>
        
        {venues.length === 0 ? (
          <Card className="p-8 text-center">
            <p className="text-gray-500">Belum ada venue yang ditambahkan</p>
          </Card>
        ) : (
          venues.map((venue) => (
            <Card key={venue.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2 space-x-2">
                    <h3 className="text-lg font-semibold">{venue.nama}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      venue.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {venue.is_active ? 'Aktif' : 'Nonaktif'}
                    </span>
                  </div>
                  
                  <p className="mb-1 text-gray-600">{venue.alamat}</p>
                  <div className="flex space-x-4 text-sm text-gray-500">
                    <span>Koordinat: {venue.latitude.toFixed(6)}, {venue.longitude.toFixed(6)}</span>
                    <span>Radius: {venue.radius}m</span>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEdit(venue)}
                  >
                    Edit
                  </Button>
                  
                  <Button
                    size="sm"
                    variant={venue.is_active ? "outline" : "default"}
                    onClick={() => handleToggleActive(venue.id, venue.is_active)}
                  >
                    {venue.is_active ? 'Nonaktifkan' : 'Aktifkan'}
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDelete(venue.id)}
                  >
                    Hapus
                  </Button>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
