'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { csrfFetch } from '@/utils/csrfClient';
import Toast, { ToastType } from '@/components/Toast';
import { PlusIcon, PencilIcon, TrashIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { formatIndonesiaDate } from '@/utils/dateUtils';

interface Location {
  id: string;
  kab_kota: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginationData {
  currentPage: number;
  pageSize: number;
  totalPages: number;
  totalItems: number;
}

export default function LocationsContent() {
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    currentPage: 1,
    pageSize: 10,
    totalPages: 1,
    totalItems: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [toast, setToast] = useState<{message: string, type: ToastType} | null>(null);
  const [locationToDelete, setLocationToDelete] = useState<Location | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();

  const fetchLocations = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const page = searchParams.get('page') || '1';
      const search = searchParams.get('search') || '';
      
      const queryParams = new URLSearchParams();
      queryParams.set('page', page);
      queryParams.set('pageSize', '10');
      if (search) queryParams.set('search', search);
      
      const response = await fetch(`/api/tugas-lokasi?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      setLocations(data.locations);
      setPagination(data.pagination);
    } catch (error) {
      setError('Gagal memuat data lokasi. Silakan coba lagi.');
      console.error('Error fetching locations:', error);
    } finally {
      setLoading(false);
    }
  }, [searchParams]);

  useEffect(() => {
    fetchLocations();
  }, [fetchLocations]);

  useEffect(() => {
    setSearchTerm(searchParams.get('search') || '');
  }, [searchParams]);

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`/dashboard/tempat-tugas?${params.toString()}`);
  };

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    params.set('page', '1');
    router.push(`/dashboard/tempat-tugas?${params.toString()}`);
  };

  const handleDeleteClick = (location: Location) => {
    setLocationToDelete(location);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!locationToDelete) return;

    try {
      const response = await csrfFetch(`/api/tugas-lokasi/${locationToDelete.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menghapus lokasi');
      }

      setToast({
        message: 'Lokasi berhasil dihapus',
        type: 'success'
      });
      
      fetchLocations();
    } catch (error) {
      setToast({
        message: error instanceof Error ? error.message : 'Gagal menghapus lokasi',
        type: 'error'
      });
    } finally {
      setShowDeleteModal(false);
      setLocationToDelete(null);
    }
  };

  const formatDate = (dateString: string) => {
    return formatIndonesiaDate(dateString);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Tempat Tugas</h1>
        <Link 
          href="/dashboard/tempat-tugas/add" 
          className="flex items-center px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
        >
          <PlusIcon className="w-5 h-5 mr-1" />
          Tambah Lokasi
        </Link>
      </div>
      
      {/* Search Bar */}
      <form onSubmit={handleSearch} className="flex space-x-4">
        <div className="relative flex-grow">
          <input
            type="text"
            placeholder="Cari lokasi..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md"
          />
          <MagnifyingGlassIcon className="absolute w-5 h-5 text-gray-400 top-3 left-3" />
        </div>
        <button
          type="submit"
          className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
        >
          Cari
        </button>
        {searchParams.get('search') && (
          <button
            type="button"
            onClick={() => router.push('/dashboard/tempat-tugas')}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Reset
          </button>
        )}
      </form>

      {/* Error Message */}
      {error && (
        <div className="p-4 text-red-700 bg-red-100 rounded-md">
          <p>{error}</p>
        </div>
      )}
      
      {/* Toast Notification */}
      {toast && (
        <Toast
          toast={{id: 'location-toast', message: toast.message, type: toast.type}}
          onClose={() => setToast(null)}
        />
      )}

      {/* Locations Table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Kabupaten/Kota
              </th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Dibuat Pada
              </th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                Diperbarui Pada
              </th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                Aksi
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={4} className="px-6 py-4 text-center">
                  <div className="flex items-center justify-center">
                    <div className="w-8 h-8 border-t-2 border-b-2 border-blue-600 rounded-full animate-spin"></div>
                  </div>
                </td>
              </tr>
            ) : locations.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                  Tidak ada data lokasi tugas yang ditemukan
                </td>
              </tr>
            ) : (
              locations.map((location) => (
                <tr key={location.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    {location.kab_kota}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {formatDate(location.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {formatDate(location.updatedAt)}
                  </td>
                  <td className="px-6 py-4 text-center whitespace-nowrap">
                    <div className="flex justify-center space-x-2">
                      <Link
                        href={`/dashboard/tempat-tugas/${location.id}/edit`}
                        className="p-2 text-blue-600 rounded-full hover:bg-blue-100"
                        title="Edit"
                      >
                        <PencilIcon className="w-5 h-5" />
                      </Link>
                      <button
                        onClick={() => handleDeleteClick(location)}
                        className="p-2 text-red-600 rounded-full hover:bg-red-100"
                        title="Hapus"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
          <div className="flex items-center text-sm text-gray-700">
            <span>
              Menampilkan {((pagination.currentPage - 1) * pagination.pageSize) + 1} - {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalItems)} dari {pagination.totalItems} lokasi
            </span>
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage <= 1}
              className={`relative inline-flex items-center px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md ${pagination.currentPage <= 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              Sebelumnya
            </button>
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNumber) => (
              <button
                key={pageNumber}
                onClick={() => handlePageChange(pageNumber)}
                className={`relative inline-flex items-center px-4 py-2 text-sm font-medium border ${pagination.currentPage === pageNumber ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'}`}
              >
                {pageNumber}
              </button>
            ))}
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage >= pagination.totalPages}
              className={`relative inline-flex items-center px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md ${pagination.currentPage >= pagination.totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              Berikutnya
            </button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-lg">
            <h3 className="text-lg font-medium text-gray-900">Konfirmasi Hapus</h3>
            <p className="mt-2 text-sm text-gray-500">
              Apakah Anda yakin ingin menghapus lokasi <strong>{locationToDelete?.kab_kota}</strong>?
              Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex justify-end mt-4 space-x-3">
              <button
                type="button"
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                onClick={() => setShowDeleteModal(false)}
              >
                Batal
              </button>
              <button
                type="button"
                className="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700"
                onClick={handleDeleteConfirm}
              >
                Hapus
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
