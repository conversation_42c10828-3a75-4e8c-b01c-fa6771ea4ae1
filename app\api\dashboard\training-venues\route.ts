import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - List all training venues
export async function GET() {
  try {
    const venues = await prisma.training_venue.findMany({
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      venues
    });

  } catch (error) {
    console.error('Get Training Venues Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data venue' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POST - Create new training venue
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { nama, alamat, latitude, longitude, radius } = body;

    // Validation
    if (!nama || !alamat || !latitude || !longitude || !radius) {
      return NextResponse.json(
        { error: 'Semua field harus diisi' },
        { status: 400 }
      );
    }

    if (radius < 50 || radius > 500) {
      return NextResponse.json(
        { error: 'Radius harus antara 50-500 meter' },
        { status: 400 }
      );
    }

    // Check if venue name already exists
    const existingVenue = await prisma.training_venue.findFirst({
      where: { nama }
    });

    if (existingVenue) {
      return NextResponse.json(
        { error: 'Nama venue sudah digunakan' },
        { status: 400 }
      );
    }

    // Create new venue
    const venue = await prisma.training_venue.create({
      data: {
        nama,
        alamat,
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        radius: parseInt(radius),
        is_active: true
      }
    });

    return NextResponse.json({
      success: true,
      venue,
      message: 'Venue berhasil ditambahkan'
    });

  } catch (error) {
    console.error('Create Training Venue Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat membuat venue' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
