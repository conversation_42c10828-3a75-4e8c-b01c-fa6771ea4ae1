import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import PDFDocument from 'pdfkit';
import { PDFDocument as PDFLib } from 'pdf-lib';
import { Prisma } from '@prisma/client';
import path from 'path';
import { formatIndonesiaDate } from '@/utils/dateUtils';

// Type for Biodata with Pelatihan relationship
type BiodataWithPelatihan = Prisma.biodataGetPayload<{
  include: { pelatihan: true }
}>;

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { biodataIds } = body; // Only use biodataIds from the selected checkboxes
    
    if (!biodataIds || biodataIds.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data biodata yang dipilih' },
        { status: 400 }
      );
    }
    
    // Build query for biodata with proper typing
    const biodataQuery: Prisma.biodataFindManyArgs = {
      include: {
        pelatihan: true,
      },
      orderBy: {
        nama: 'asc',
      },
      where: {
        id: { in: biodataIds }
      },
    };
    
    // Query database
    const biodataList = await prisma.biodata.findMany(biodataQuery);
    
    if (biodataList.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data biodata yang ditemukan' },
        { status: 404 }
      );
    }
    
    // Generate PDFs for each selected biodata
    const pdfBuffers: Buffer[] = [];
    
    // Ensure each biodataItem has the pelatihan property for TypeScript
    const typedBiodataList = biodataList as BiodataWithPelatihan[];
    
    for (const biodataItem of typedBiodataList) {
      const pdfBuffer = await generatePDF(biodataItem);
      pdfBuffers.push(pdfBuffer);
    }
    
    // If only one PDF, return it directly
    if (pdfBuffers.length === 1) {
      const headers = new Headers();
      headers.set('Content-Type', 'application/pdf');
      headers.set('Content-Disposition', 'attachment; filename=Biodata_Peserta.pdf');
      
      return new NextResponse(pdfBuffers[0], {
        status: 200,
        headers,
      });
    } 
    // If multiple PDFs, merge them
    else {
      try {
        // Implement PDF merging using pdf-lib
        const mergedPdf = await mergePDFs(pdfBuffers);
        
        const headers = new Headers();
        headers.set('Content-Type', 'application/pdf');
        headers.set('Content-Disposition', 'attachment; filename=Biodata_Peserta_Bulk.pdf');
        
        return new NextResponse(mergedPdf, {
          status: 200,
          headers,
        });
      } catch (error) {
        console.error('Error merging PDFs:', error);
        return NextResponse.json(
          { error: 'Terjadi kesalahan saat menggabungkan file PDF' },
          { status: 500 }
        );
      }
    }
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghasilkan PDF' },
      { status: 500 }
    );
  }
}

// Fungsi untuk memformat tanggal ke format Indonesia dengan penyesuaian zona waktu
function formatTanggal(date: Date): string {
  return formatIndonesiaDate(date);
}

// Helper function to validate and clean base64 data
function cleanBase64Image(base64String: string | null) {
  if (!base64String) return null;
  
  try {
    // Remove data URL prefix if present
    const base64Data = base64String.replace(/^data:image\/png;base64,/, '');
    
    // Check if the string is valid base64
    if (!/^[A-Za-z0-9+/]+[=]{0,2}$/.test(base64Data)) {
      console.error('Invalid base64 format');
      return null;
    }
    
    return base64Data;
  } catch (error) {
    console.error('Error cleaning base64:', error);
    return null;
  }
}

// Helper function to draw signature and name
function drawSignatureAndName(doc: PDFKit.PDFDocument, ttdY: number, nama: string, nip: string | null, signatureData?: string) {
  // Position to align with "Samarinda" text (x=300, width=200, right-aligned)
  const signatureAreaX = 300; // Match with Samarinda text x-position
  const signatureAreaWidth = 200; // Match with Samarinda text width
    // Draw signature if available
  if (signatureData) {
    const cleanedBase64 = cleanBase64Image(signatureData);
    if (cleanedBase64) {
      const buffer = Buffer.from(cleanedBase64, 'base64');
      // Position signature to align with right-aligned text layout
      const signatureX = signatureAreaX + signatureAreaWidth - 150; // Right-align the signature within the area
      doc.image(buffer, signatureX, ttdY + 5, {
        fit: [150, 40],
        align: 'center',
        valign: 'center'
      });
    }
  }
    // Add name text above the line - aligned with Samarinda text area (right-aligned)
  doc.font('Helvetica')
     .fontSize(10)
     .text(nama, signatureAreaX, ttdY + 45, {
       width: signatureAreaWidth,
       align: 'right'
     });
  // Draw the line below name - positioned to align with right-aligned text
  const lineStartX = signatureAreaX + 80; // Start line to align with typical right-aligned text position
  const lineEndX = signatureAreaX + signatureAreaWidth; // End line at the right edge of the area
  doc.moveTo(lineStartX, ttdY + 60)
     .lineTo(lineEndX, ttdY + 60)
     .stroke();

  // Add NIP below the line - aligned with Samarinda text area (right-aligned)
  doc.font('Helvetica')
     .fontSize(10)
     .text(`NIP. ${nip || '-'}`, signatureAreaX, ttdY + 65, {
       width: signatureAreaWidth,
       align: 'right'
     });
}

async function generatePDF(
  biodataItem: BiodataWithPelatihan
): Promise<Buffer> {
  return new Promise<Buffer>((resolve, reject) => {
    try {
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        bufferPages: true,
        info: {
          Title: 'Biodata Peserta',
          Author: 'BPMP KALIMANTAN TIMUR',
          Subject: 'Biodata Peserta Pelatihan',
          Keywords: 'biodata, peserta, pelatihan',
        }
      });
      
      // Collect PDF data into buffer
      const chunks: Buffer[] = [];
      doc.on('data', (chunk: Buffer) => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
      
      // Path ke file gambar
      const headerPath = path.join(process.cwd(), 'public', 'header.png');
      const footerLeftPath = path.join(process.cwd(), 'public', 'footerkiri.png');
      const footerRightPath = path.join(process.cwd(), 'public', 'footerkanan.png');
      
      const headerHeight = 130; // Perkiraan tinggi header dalam piksel
      
      // Tambahkan header sebagai gambar
      try {
        doc.image(headerPath, 50, 30, { width: 495 });
      } catch (error) {
        console.error('Error loading header image:', error);
        
        // Fallback ke text header jika gambar tidak ditemukan
        doc.font('Helvetica-Bold').fontSize(12);
        doc.text('KEMENTERIAN PENDIDIKAN DASAR DAN MENENGAH', 150, 30, {
          align: 'center'
        });
        
        doc.fontSize(11);
        doc.text('BALAI PENJAMINAN MUTU PENDIDIKAN', 150, 45, {
          align: 'center'
        });
        
        doc.text('PROVINSI KALIMANTAN TIMUR', 150, 60, {
          align: 'center'
        });
        
        doc.font('Helvetica').fontSize(9);
        doc.text('Jalan Cipto Mangunkusumo Km 2, Samarinda Seberang, Samarinda 75132', 150, 75, {
          align: 'center'
        });
        
        doc.text('Telepon (0541) 260304, Faksimile (0541) 262059', 150, 88, {
          align: 'center'
        });
        
        doc.text('Laman www.bpmpkaltim.kemdikbud.go.id, pos-el <EMAIL>', 150, 101, {
          align: 'center'
        });
      }
      
      // Posisi Y untuk memulai data biodata, langsung di bawah header dengan jarak yang cukup
      const startY = 30 + headerHeight + 40;
      
      // Data Biodata
      doc.font('Helvetica').fontSize(11);
      
      const labelX = 112;
      const valueX = 270;
      const lineHeight = 25;
      
      // Format field-field biodata
      doc.text('Nama Lengkap', labelX, startY);
      doc.text(':', valueX - 10, startY);
      doc.text(biodataItem.nama || '', valueX, startY);
      
      doc.text('Tempat/Tanggal Lahir', labelX, startY + lineHeight * 1);
      doc.text(':', valueX - 10, startY + lineHeight * 1);
      const tanggalLahir = biodataItem.tanggal_lahir 
        ? formatTanggal(new Date(biodataItem.tanggal_lahir)) 
        : '';
      doc.text(`${biodataItem.tempat_lahir || ''}, ${tanggalLahir}`, valueX, startY + lineHeight * 1);
      
      doc.text('Pendidikan', labelX, startY + lineHeight * 2);
      doc.text(':', valueX - 10, startY + lineHeight * 2);
      doc.text(biodataItem.pendidikan || '', valueX, startY + lineHeight * 2);
      
      doc.text('Jenis Kelamin', labelX, startY + lineHeight * 3);
      doc.text(':', valueX - 10, startY + lineHeight * 3);
      doc.text(biodataItem.jenis_kelamin || '', valueX, startY + lineHeight * 3);
      
      doc.text('NIP', labelX, startY + lineHeight * 4);
      doc.text(':', valueX - 10, startY + lineHeight * 4);
      doc.text(biodataItem.nip || '-', valueX, startY + lineHeight * 4);
      
      doc.text('Pangkat/Golongan', labelX, startY + lineHeight * 5);
      doc.text(':', valueX - 10, startY + lineHeight * 5);
      doc.text(biodataItem.pangkat_golongan || '-', valueX, startY + lineHeight * 5);
      
      doc.text('Jabatan', labelX, startY + lineHeight * 6);
      doc.text(':', valueX - 10, startY + lineHeight * 6);
      doc.text(biodataItem.jabatan || '', valueX, startY + lineHeight * 6);
      
      doc.text('Unit Kerja / Asal Sekolah', labelX, startY + lineHeight * 7);
      doc.text(':', valueX - 10, startY + lineHeight * 7);
      doc.text(biodataItem.unit_kerja || '', valueX, startY + lineHeight * 7);
      
      doc.text('Alamat Unit Kerja / Sekolah', labelX, startY + lineHeight * 8);
      doc.text(':', valueX - 10, startY + lineHeight * 8);
      doc.text(biodataItem.alamat_unit_kerja || '', valueX, startY + lineHeight * 8);
      
      doc.text('NPWP', labelX, startY + lineHeight * 9);
      doc.text(':', valueX - 10, startY + lineHeight * 9);
      doc.text(biodataItem.npwp || '-', valueX, startY + lineHeight * 9);
      
      doc.text('Email', labelX, startY + lineHeight * 10);
      doc.text(':', valueX - 10, startY + lineHeight * 10);
      doc.text(biodataItem.email || '', valueX, startY + lineHeight * 10);
      
      doc.text('No. HP/WA', labelX, startY + lineHeight * 11);
      doc.text(':', valueX - 10, startY + lineHeight * 11);
      doc.text(biodataItem.no_hp || '', valueX, startY + lineHeight * 11);
      
      // Area untuk tanda tangan
      const signatureY = startY + lineHeight * 13;
        // Tanggal dan lokasi - menggunakan kota dari biodata
      const today = new Date();
      const formattedDate = formatTanggal(today);
      doc.text(`${biodataItem.kota}, ${formattedDate}`, 300, signatureY, { 
        align: 'right',
        width: 200
      });
      
      // Kosongkan tempat tanda tangan
      const ttdY = signatureY + 30;
      
      // Draw signature, name, and line with NIP
      drawSignatureAndName(doc, ttdY, biodataItem.nama, biodataItem.nip, biodataItem.tanda_tangan);
      
      // Footer
      const footerY = 750;
      
      // Tambahkan footer kiri
      try {
        doc.image(footerLeftPath, 50, footerY, { width: 50 });
      } catch (error) {
        console.error('Error loading footer left image:', error);
        // Fallback jika gambar tidak ditemukan
        doc.circle(80, footerY + 25, 20).stroke();
      }
      
      // Tambahkan teks footer tengah
      doc.fontSize(8).font('Helvetica-Oblique');
      doc.text('BPMP PROVINSI KALIMANTAN TIMUR', 0, footerY + 10, { align: 'center' });
      doc.text('MENUJU ZONA INTEGRITAS WILAYAH BEBAS DARI KORUPSI', 0, footerY + 25, { align: 'center' });
      
      // Tambahkan footer kanan
      try {
        doc.image(footerRightPath, 495, footerY, { width: 50 });
      } catch (error) {
        console.error('Error loading footer right image:', error);
        // Fallback jika gambar tidak ditemukan
        doc.circle(520, footerY + 25, 20).stroke();
      }
      
      // Finalize PDF
      doc.end();
      
    } catch (error) {
      reject(error);
    }
  });
}

// Implement PDF merging function using pdf-lib
async function mergePDFs(pdfBuffers: Buffer[]): Promise<Buffer> {
  // Create a new PDF document
  const mergedPdf = await PDFLib.create();
  
  // For each buffer, create a PDFDocument and copy its pages
  for (const pdfBuffer of pdfBuffers) {
    try {
      // Load the PDF from the buffer
      const pdf = await PDFLib.load(pdfBuffer);
      
      // Get the page count
      const pageCount = pdf.getPageCount();
      
      // Copy all pages from the PDF to the merged document
      for (let i = 0; i < pageCount; i++) {
        const [copiedPage] = await mergedPdf.copyPages(pdf, [i]);
        mergedPdf.addPage(copiedPage);
      }
    } catch (error) {
      console.error('Error processing PDF in merger:', error);
      throw new Error('Failed to process PDF for merging');
    }
  }
  
  // Save the merged PDF
  const mergedPdfBytes = await mergedPdf.save();
  
  // Return as Buffer
  return Buffer.from(mergedPdfBytes);
}
