import { NextRequest, NextResponse } from 'next/server';
import { 
  validateGpsLocation, 
  getNearestVenue,
  getRiskLevelDescription,
  DEFAULT_TRAINING_VENUES,
  type GeofenceConfig 
} from '../../../../lib/geofencing';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { latitude, longitude, pelatihanId, accuracy } = body;

    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: 'Koordinat GPS diperlukan' },
        { status: 400 }
      );
    }

    // Convert venues from database format to GeofenceConfig format
    let geofenceVenues: GeofenceConfig[] = DEFAULT_TRAINING_VENUES;
    
    try {
      const dbVenues = await prisma.training_venue.findMany({
        where: { is_active: true }
      });
      
      if (dbVenues.length > 0) {
        geofenceVenues = dbVenues.map(venue => ({
          center: {
            lat: venue.latitude,
            lng: venue.longitude
          },
          radius: venue.radius,
          name: venue.nama,
          address: venue.alamat
        }));
      }
    } catch (dbError) {
      console.warn('Failed to fetch training venues from database, using defaults:', dbError);
    }

    // If pelatihanId is provided, try to get venue-specific validation
    if (pelatihanId) {
      try {
        const pelatihan = await prisma.pelatihan.findUnique({
          where: { id: pelatihanId },
          include: { venue: true }
        });

        if (pelatihan?.venue) {
          // Use specific venue for this pelatihan
          geofenceVenues = [{
            center: {
              lat: pelatihan.venue.latitude,
              lng: pelatihan.venue.longitude
            },
            radius: pelatihan.venue.radius,
            name: pelatihan.venue.nama,
            address: pelatihan.venue.alamat
          }];
        }
      } catch (error) {
        console.warn('Failed to fetch pelatihan venue:', error);
      }
    }

    // Prepare location data with timestamp
    const locationData = {
      latitude,
      longitude,
      accuracy: accuracy || 10,
      timestamp: Date.now()
    };

    // Validate GPS location
    const validation = validateGpsLocation(
      locationData,
      geofenceVenues,
      undefined, // no previous location
      {
        maxAccuracy: 50,
        requireGeofence: false, // Don't require geofence for now, just warn
        spoofingDetection: true
      }
    );

    // Get nearest venue information
    const nearestVenue = getNearestVenue(locationData, geofenceVenues);
    const riskInfo = getRiskLevelDescription(validation.riskScore);

    return NextResponse.json({
      success: true,
      validation: {
        isValid: validation.isValid,
        riskScore: validation.riskScore,
        riskLevel: riskInfo.level,
        riskDescription: riskInfo.description,
        warnings: validation.warnings,
        venueMatch: nearestVenue.venue.name,
        distance: Math.round(nearestVenue.distance),
        inGeofence: validation.validationFlags.withinGeofence,
        spoofingDetected: !validation.validationFlags.noSpoofingDetected,
        validationFlags: validation.validationFlags
      }
    });

  } catch (error) {
    console.error('GPS Validation Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memvalidasi lokasi GPS' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'GPS Validation API - Use POST method' },
    { status: 405 }
  );
}
