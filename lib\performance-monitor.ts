// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Measure operation performance
  async measureAsync<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      this.recordMetric(operation, performance.now() - start);
      return result;
    } catch (error) {
      this.recordMetric(`${operation}_error`, performance.now() - start);
      throw error;
    }
  }

  // Record metric
  private recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    this.metrics.get(operation)!.push(duration);
    
    // Keep only last 100 measurements
    const measurements = this.metrics.get(operation)!;
    if (measurements.length > 100) {
      measurements.shift();
    }
  }

  // Get performance report
  getReport(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const report: Record<string, { avg: number; min: number; max: number; count: number }> = {};
    
    for (const [operation, measurements] of this.metrics) {
      if (measurements.length > 0) {
        report[operation] = {
          avg: measurements.reduce((a, b) => a + b) / measurements.length,
          min: Math.min(...measurements),
          max: Math.max(...measurements),
          count: measurements.length,
        };
      }
    }
    
    return report;
  }

  // Clear metrics
  clear(): void {
    this.metrics.clear();
  }
}

// Web Vitals monitoring
export function initWebVitals() {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    import('web-vitals').then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
      onCLS(console.log);
      onINP(console.log);
      onFCP(console.log);
      onLCP(console.log);
      onTTFB(console.log);
    });
  }
}
