'use client';

import Image from 'next/image';
import { useState } from 'react';

interface AttendancePhotoProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
}

export default function AttendancePhoto({ 
  src, 
  alt, 
  width, 
  height, 
  className 
}: AttendancePhotoProps) {
  const [hasError, setHasError] = useState(false);

  if (hasError) {
    return (
      <div className="p-4 text-center text-gray-500 border rounded-lg">
        Foto tidak dapat dimuat
      </div>
    );
  }

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      unoptimized
      onError={() => setHasError(true)}
    />
  );
}
