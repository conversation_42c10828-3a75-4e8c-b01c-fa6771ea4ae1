'use server';

import { revalidatePath } from 'next/cache';
import { randomUUID } from 'crypto';
import { PelatihanFormData } from './hooks/usePelatihanForm';
import { prisma } from '../../../lib/prisma';
import { getCurrentUser } from '@/lib/auth';
import { pelatihan_jenjang } from '@prisma/client';
import { logger } from '@/utils/logger';

// Función auxiliar para generar un enlace único
async function generateUniqueLink() {
  const uniqueId = randomUUID().substring(0, 8);
  return uniqueId;
}

// Función auxiliar para convertir string a enum de jenjang
function convertToJenjangEnum(jenjang: string): pelatihan_jenjang {
  // Validar y convertir a enum
  const validJenjang = Object.values(pelatihan_jenjang).includes(jenjang as pelatihan_jenjang)
    ? jenjang as pelatihan_jenjang
    : pelatihan_jenjang.UMUM;
  
  return validJenjang;
}


export async function addPelatihan(data: PelatihanFormData, _csrfToken?: string | null) {
  logger.info("addPelatihan action called", { 
    nama: data.nama, 
    tempat: data.tempat,
    jenjangTargetsCount: data.jenjangTargets.length
  });
  
  try {
    const currentUser = await getCurrentUser();
    
    if (!currentUser) {
      logger.warning("Unauthorized access attempt", { path: "addPelatihan" });
      return { success: false, message: 'Unauthorized' };
    }
    
    // Validasi data dasar
    if (!data.nama || !data.tempat || !data.tgl_mulai || !data.tgl_berakhir) {
      logger.warning("Incomplete pelatihan data", { path: "addPelatihan" });
      return { success: false, message: 'Data pelatihan tidak lengkap' };
    }
    
    // Validasi jenjangTargets
    if (!data.jenjangTargets || data.jenjangTargets.length === 0) {
      logger.warning("No jenjangTargets provided", { path: "addPelatihan" });
      return { success: false, message: 'Minimal satu jenjang target harus diisi' };
    }
    
    // Tentukan jenjang utama (yang memiliki target peserta terbanyak)
    const mainJenjangTarget = [...data.jenjangTargets].sort((a, b) => 
      b.target_peserta - a.target_peserta
    )[0];
    
    const mainJenjang = convertToJenjangEnum(mainJenjangTarget.jenjang);
    const pelatihanId = randomUUID();
      // Generate link unik dengan verifikasi keunikan
    const link_registrasi = await generateUniqueLink();
    const link_absensi = await generateUniqueLink();
    
    // Generate link untuk internal dan eksternal berdasarkan peserta_kegiatan
    let link_absensi_internal = null;
    let link_absensi_eksternal = null;
    
    if (data.peserta_kegiatan === 'internal' || data.peserta_kegiatan === 'keduanya') {
      link_absensi_internal = await generateUniqueLink();
    }
    
    if (data.peserta_kegiatan === 'eksternal' || data.peserta_kegiatan === 'keduanya') {
      link_absensi_eksternal = await generateUniqueLink();
    }

    logger.info("Saving data to database", { path: "addPelatihan", pelatihanId });
    
    // Simpan langsung ke database menggunakan prisma
    try {
      // Buat transaction untuk memastikan semua data tersimpan atau tidak sama sekali
      const pelatihan = await prisma.$transaction(async (tx) => {        // 1. Buat pelatihan terlebih dahulu
        const newPelatihan = await tx.pelatihan.create({
          data: {
            id: pelatihanId,
            nama: data.nama,
            tempat: data.tempat,
            tgl_mulai: new Date(data.tgl_mulai),
            tgl_berakhir: new Date(data.tgl_berakhir),
            jenjang: mainJenjang,
            target_peserta: data.jenjangTargets.reduce((sum, jt) => sum + jt.target_peserta, 0),
            link_registrasi,
            link_absensi,
            link_absensi_internal,
            link_absensi_eksternal,
            peserta_kegiatan: data.peserta_kegiatan.toUpperCase() as any, // Convert to enum
            userId: currentUser.id,
            updatedAt: new Date(),
          }
        });
        
        // 2. Buat jenjangTargets untuk pelatihan tersebut
        const jenjangTargets = await Promise.all(
          data.jenjangTargets.map(async (jt) => {
            return await tx.pelatihan_jenjang_target.create({
              data: {
                pelatihanId: newPelatihan.id,
                jenjang: convertToJenjangEnum(jt.jenjang),
                target_peserta: jt.target_peserta
              }
            });
          })
        );
        
        logger.info("Created jenjangTargets", { 
          path: "addPelatihan", 
          pelatihanId: newPelatihan.id,
          count: jenjangTargets.length 
        });
        
        // 3. Ambil pelatihan beserta relasinya untuk dikembalikan ke client
        return await tx.pelatihan.findUnique({
          where: { id: newPelatihan.id },
          include: {
            jenjangTargets: true
          }
        });
      });
      
      if (!pelatihan) {
        logger.error("Failed to save pelatihan - prisma returned null", { path: "addPelatihan" });
        return { 
          success: false, 
          message: 'Terjadi kesalahan saat menyimpan pelatihan' 
        };
      }
      
      logger.info('Pelatihan berhasil ditambahkan', { id: pelatihan.id });
      
      // Revalidasi path untuk memperbarui UI
      revalidatePath('/dashboard/pelatihan');
      
      return {
        success: true,
        message: 'Pelatihan berhasil ditambahkan',
        data: pelatihan
      };
    } catch (dbError: any) {
      logger.error('Database error in addPelatihan', dbError, { errorCode: dbError.code });
      
      // Handle specific Prisma errors
      if (dbError.code === 'P2002') {
        return {
          success: false,
          message: 'Pelatihan dengan ID tersebut sudah ada',
        };
      }
      
      return {
        success: false,
        message: `Database error: ${dbError.message || 'Unknown error'}`,
      };
    }
  } catch (error) {
    logger.error('Error adding pelatihan', error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Terjadi kesalahan saat menambahkan pelatihan'
    };
  }
}

export async function updatePelatihan(id: string, data: PelatihanFormData, _csrfToken?: string | null) {
  try {
    const currentUser = await getCurrentUser();
    
    if (!currentUser) {
      logger.warning("Unauthorized access attempt", { path: "updatePelatihan" });
      return { success: false, message: 'Unauthorized' };
    }
    
    // Validasi data dasar
    if (!data.nama || !data.tempat || !data.tgl_mulai || !data.tgl_berakhir) {
      logger.warning("Incomplete pelatihan data", { path: "updatePelatihan" });
      return { success: false, message: 'Data pelatihan tidak lengkap' };
    }
    
    // Validasi jenjangTargets
    if (!data.jenjangTargets || data.jenjangTargets.length === 0) {
      logger.warning("No jenjangTargets provided", { path: "updatePelatihan" });
      return { success: false, message: 'Minimal satu jenjang target harus diisi' };
    }
      // Cek apakah pelatihan ada dan milik user saat ini
    const existingPelatihan = await prisma.pelatihan.findUnique({
      where: { id },
      select: { 
        userId: true,
        peserta_kegiatan: true,
        link_absensi_internal: true,
        link_absensi_eksternal: true
      }    });
    
    if (!existingPelatihan) {
      logger.warning("Pelatihan not found", { path: "updatePelatihan", id });
      return { success: false, message: 'Pelatihan tidak ditemukan' };
    }
    
    if (existingPelatihan.userId !== currentUser.id && currentUser.role !== 'ADMIN') {
      logger.warning("Access denied to pelatihan", { 
        path: "updatePelatihan", 
        pelatihanId: id, 
        userId: currentUser.id,
        userRole: currentUser.role
      });
      return { success: false, message: 'Anda tidak memiliki akses ke pelatihan ini' };
    }
    
    // Tentukan jenjang utama (yang memiliki target peserta terbanyak)
    const mainJenjangTarget = [...data.jenjangTargets].sort((a, b) => 
      b.target_peserta - a.target_peserta
    )[0];
      const mainJenjang = convertToJenjangEnum(mainJenjangTarget.jenjang);
      // Cek apakah peserta_kegiatan berubah dan perlu regenerasi link
    const pesertaKegiatanBerubah = existingPelatihan.peserta_kegiatan !== data.peserta_kegiatan.toUpperCase();
    
    // Generate link baru berdasarkan peserta_kegiatan
    let link_absensi_internal = existingPelatihan.link_absensi_internal;
    let link_absensi_eksternal = existingPelatihan.link_absensi_eksternal;
      if (pesertaKegiatanBerubah) {
      // Reset link berdasarkan pilihan baru
      if (data.peserta_kegiatan === 'internal' || data.peserta_kegiatan === 'keduanya') {
        link_absensi_internal = await generateUniqueLink();
      } else {
        link_absensi_internal = null;
      }
      
      if (data.peserta_kegiatan === 'eksternal' || data.peserta_kegiatan === 'keduanya') {
        link_absensi_eksternal = await generateUniqueLink();
      } else {
        link_absensi_eksternal = null;
      }
    }
    
    logger.info("Starting database transaction", { path: "updatePelatihan", pelatihanId: id });
    try {
      // Update menggunakan transaction untuk memastikan konsistensi data
      const pelatihan = await prisma.$transaction(async (tx) => {        // 1. Update pelatihan
        const updatedPelatihan = await tx.pelatihan.update({
          where: { id },
          data: {
            nama: data.nama,
            tempat: data.tempat,
            tgl_mulai: new Date(data.tgl_mulai),
            tgl_berakhir: new Date(data.tgl_berakhir),
            jenjang: mainJenjang,
            target_peserta: data.jenjangTargets.reduce((sum, jt) => sum + jt.target_peserta, 0),
            peserta_kegiatan: data.peserta_kegiatan.toUpperCase() as any, // Update peserta_kegiatan
            link_absensi_internal, // Update link internal
            link_absensi_eksternal, // Update link eksternal            updatedAt: new Date()
          }
        });
        
        // 2. Hapus jenjangTargets lama
        await tx.pelatihan_jenjang_target.deleteMany({
          where: { pelatihanId: id }
        });
        
        // 3. Buat jenjangTargets baru
        const jenjangTargets = await Promise.all(
          data.jenjangTargets.map(async (jt) => {
            return await tx.pelatihan_jenjang_target.create({
              data: {
                pelatihanId: updatedPelatihan.id,
                jenjang: convertToJenjangEnum(jt.jenjang),
                target_peserta: jt.target_peserta
              }
            });
          })
        );
        
        logger.info("Created new jenjangTargets", { 
          path: "updatePelatihan", 
          pelatihanId: id, 
          count: jenjangTargets.length 
        });
        
        // 4. Ambil pelatihan terbaru dengan relasinya
        return await tx.pelatihan.findUnique({
          where: { id },
          include: {
            jenjangTargets: true
          }
        });
      });

      if (!pelatihan) {
        logger.error("Failed to update pelatihan - prisma returned null", { path: "updatePelatihan", id });
        return { 
          success: false, 
          message: 'Terjadi kesalahan saat mengupdate pelatihan' 
        };
      }
      
      logger.info('Pelatihan berhasil diupdate', { id });
      
      // Revalidasi path untuk memperbarui UI
      revalidatePath('/dashboard/pelatihan');
      
      return {
        success: true,
        message: 'Pelatihan berhasil diperbarui',
        data: pelatihan
      };
    } catch (dbError: any) {
      logger.error('Database error in updatePelatihan', dbError, { errorCode: dbError.code });
      
      return {
        success: false,
        message: `Database error: ${dbError.message || 'Unknown error'}`,
      };
    }
  } catch (error) {
    logger.error('Error updating pelatihan', error instanceof Error ? error : new Error(String(error)));
    
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Terjadi kesalahan saat memperbarui pelatihan'
    };
  }
}

export async function deletePelatihan(id: string) {
  logger.info("Server action: deletePelatihan called", { id });
  
  try {
    const currentUser = await getCurrentUser();
    
    if (!currentUser) {
      return { success: false, message: 'Unauthorized' };
    }
    
    // Verifikasi kepemilikan pelatihan
    const existingPelatihan = await prisma.pelatihan.findUnique({
      where: { id },
      select: { userId: true }
    });
    
    if (!existingPelatihan) {
      return { success: false, message: 'Pelatihan tidak ditemukan' };
    }
    
    if (existingPelatihan.userId !== currentUser.id) {
      return { success: false, message: 'Anda tidak memiliki akses ke pelatihan ini' };
    }
    
    // Implement the actual database operation with transaction
    await prisma.$transaction(async (tx) => {
      // Hapus jenjangTargets terlebih dahulu
      await tx.pelatihan_jenjang_target.deleteMany({
        where: { pelatihanId: id }
      });
      
      // Hapus pelatihan
      await tx.pelatihan.delete({
        where: { id }
      });
    });
    
    // Revalidate the path to update the UI
    revalidatePath('/dashboard/pelatihan');
    
    return { success: true, message: 'Pelatihan berhasil dihapus' };
  } catch (error) {
    logger.error("Error in deletePelatihan", { error, id });
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Terjadi kesalahan saat menghapus pelatihan'
    };
  }
}
