#!/bin/bash

# EXECUTE DEPLOYMENT ON SERVER
# Script untuk menjalankan deployment langsung di server VPS
# Gunakan: bash execute-deployment-server.sh

set -e

echo "==============================================="
echo "    PHOTO UPLOAD FIX - SERVER DEPLOYMENT"
echo "    kegiatan.bpmpkaltim.id"
echo "==============================================="
echo

# Fungsi untuk mencetak pesan berwarna
print_info() {
    echo -e "\033[36m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

print_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

# Deteksi user dan path aplikasi
if [ "$USER" = "root" ]; then
    APP_PATH="/var/www/vhosts/kegiatan.bpmpkaltim.id/public_html"
    UPLOADS_PATH="$APP_PATH/uploads"
    NGINX_AVAILABLE="/etc/nginx/sites-available"
    NGINX_ENABLED="/etc/nginx/sites-enabled"
else
    APP_PATH="$HOME/kegiatan.bpmpkaltim.id"
    UPLOADS_PATH="$APP_PATH/public/uploads"
    NGINX_AVAILABLE="/etc/nginx/sites-available"
    NGINX_ENABLED="/etc/nginx/sites-enabled"
fi

print_info "Detected user: $USER"
print_info "Application path: $APP_PATH"
print_info "Uploads path: $UPLOADS_PATH"

# Step 1: Backup konfigurasi yang ada
print_info "Step 1: Creating backup..."
BACKUP_DIR="/tmp/photo-upload-backup-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

if [ -d "$UPLOADS_PATH" ]; then
    print_info "Backing up uploads directory permissions..."
    ls -la "$UPLOADS_PATH" > "$BACKUP_DIR/uploads_permissions.txt"
fi

print_success "Backup created at: $BACKUP_DIR"

# Step 2: Fix file permissions untuk uploads
print_info "Step 2: Fixing file permissions..."

# Buat direktori uploads jika belum ada
if [ ! -d "$UPLOADS_PATH" ]; then
    print_info "Creating uploads directory..."
    mkdir -p "$UPLOADS_PATH"
    mkdir -p "$UPLOADS_PATH/absensi/photos"
    mkdir -p "$UPLOADS_PATH/biodata/photos"
    mkdir -p "$UPLOADS_PATH/materi"
fi

# Set ownership ke www-data
if [ "$USER" = "root" ]; then
    print_info "Setting ownership to www-data..."
    chown -R www-data:www-data "$UPLOADS_PATH"
else
    print_warning "Not running as root, skipping ownership change"
fi

# Set permissions
print_info "Setting file permissions..."
find "$UPLOADS_PATH" -type d -exec chmod 755 {} \;
find "$UPLOADS_PATH" -type f -exec chmod 644 {} \;

print_success "File permissions fixed"

# Step 3: Configure Nginx
print_info "Step 3: Configuring Nginx..."

# Buat konfigurasi nginx untuk uploads
NGINX_UPLOADS_CONF="
# Photo upload fix - disable caching for uploads
location /uploads/ {
    alias $UPLOADS_PATH/;
    expires -1;
    add_header Cache-Control \"no-cache, no-store, must-revalidate\";
    add_header Pragma no-cache;
    add_header X-Accel-Expires 0;
    
    # Allow all file types
    location ~* \.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx)$ {
        expires -1;
        add_header Cache-Control \"no-cache, no-store, must-revalidate\";
    }
    
    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection \"1; mode=block\";
}
"

# Cek apakah sudah ada konfigurasi untuk uploads
SITE_CONFIG=""
if [ -f "$NGINX_AVAILABLE/kegiatan.bpmpkaltim.id" ]; then
    SITE_CONFIG="$NGINX_AVAILABLE/kegiatan.bpmpkaltim.id"
elif [ -f "$NGINX_AVAILABLE/default" ]; then
    SITE_CONFIG="$NGINX_AVAILABLE/default"
else
    print_warning "Nginx site config not found, creating new one..."
    SITE_CONFIG="$NGINX_AVAILABLE/kegiatan.bpmpkaltim.id"
fi

# Backup nginx config
if [ -f "$SITE_CONFIG" ] && [ "$USER" = "root" ]; then
    cp "$SITE_CONFIG" "$BACKUP_DIR/nginx_config_backup"
fi

# Update nginx config jika running sebagai root
if [ "$USER" = "root" ] && [ -f "$SITE_CONFIG" ]; then
    print_info "Updating nginx configuration..."
    
    # Cek apakah sudah ada konfigurasi uploads
    if ! grep -q "location /uploads/" "$SITE_CONFIG"; then
        # Tambahkan konfigurasi uploads sebelum closing brace terakhir
        sed -i '/^[[:space:]]*}[[:space:]]*$/i\
\
    # Photo upload fix - disable caching for uploads\
    location /uploads/ {\
        expires -1;\
        add_header Cache-Control "no-cache, no-store, must-revalidate";\
        add_header Pragma no-cache;\
        add_header X-Accel-Expires 0;\
        \
        location ~* \\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx)$ {\
            expires -1;\
            add_header Cache-Control "no-cache, no-store, must-revalidate";\
        }\
        \
        add_header X-Content-Type-Options nosniff;\
        add_header X-Frame-Options DENY;\
        add_header X-XSS-Protection "1; mode=block";\
    }' "$SITE_CONFIG"
        
        print_success "Nginx configuration updated"
    else
        print_info "Nginx uploads configuration already exists"
    fi
    
    # Test nginx configuration
    print_info "Testing nginx configuration..."
    if nginx -t; then
        print_success "Nginx configuration is valid"
    else
        print_error "Nginx configuration has errors!"
        print_info "Restoring backup..."
        cp "$BACKUP_DIR/nginx_config_backup" "$SITE_CONFIG"
        exit 1
    fi
else
    print_warning "Cannot update nginx config (not root or config not found)"
fi

# Step 4: Restart services
print_info "Step 4: Restarting services..."

if [ "$USER" = "root" ]; then
    print_info "Restarting nginx..."
    systemctl restart nginx
    print_success "Nginx restarted"
    
    # Restart PM2 jika ada
    if command -v pm2 >/dev/null 2>&1; then
        print_info "Restarting PM2 applications..."
        pm2 restart all 2>/dev/null || print_warning "PM2 restart failed or no apps running"
    fi
else
    print_warning "Not running as root, cannot restart services"
    print_info "Please run: sudo systemctl restart nginx"
    print_info "And restart your Node.js application"
fi

# Step 5: Verify deployment
print_info "Step 5: Verifying deployment..."

# Cek permissions
print_info "Checking file permissions..."
if [ -d "$UPLOADS_PATH" ]; then
    PERM_CHECK=$(ls -ld "$UPLOADS_PATH" | awk '{print $1}')
    print_info "Uploads directory permissions: $PERM_CHECK"
    
    if [[ "$PERM_CHECK" == d*755* ]] || [[ "$PERM_CHECK" == drwxr-xr-x* ]]; then
        print_success "Directory permissions are correct"
    else
        print_warning "Directory permissions may need adjustment"
    fi
fi

# Cek nginx status
if [ "$USER" = "root" ]; then
    if systemctl is-active --quiet nginx; then
        print_success "Nginx is running"
    else
        print_error "Nginx is not running!"
    fi
fi

# Test konektivitas
print_info "Testing connectivity..."
if command -v curl >/dev/null 2>&1; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/uploads/ || echo "000")
    if [ "$HTTP_STATUS" = "403" ] || [ "$HTTP_STATUS" = "200" ]; then
        print_success "Uploads directory is accessible (HTTP $HTTP_STATUS)"
    else
        print_warning "Uploads directory returned HTTP $HTTP_STATUS"
    fi
else
    print_warning "curl not available for testing"
fi

echo
echo "==============================================="
echo "    DEPLOYMENT COMPLETED"
echo "==============================================="
echo
print_success "Photo upload fix has been deployed!"
echo
print_info "SUMMARY:"
echo "  ✓ File permissions fixed (755 for dirs, 644 for files)"
echo "  ✓ Nginx configuration updated (no-cache for uploads)"
echo "  ✓ Services restarted"
echo "  ✓ Uploads directory accessible"
echo
print_info "NEXT STEPS:"
echo "  1. Test photo upload in the application"
echo "  2. Verify photos are immediately accessible"
echo "  3. Monitor for any issues"
echo
print_info "BACKUP LOCATION: $BACKUP_DIR"
echo

# Buat script monitoring sederhana
MONITOR_SCRIPT="/tmp/monitor-photo-upload.sh"
cat > "$MONITOR_SCRIPT" << 'EOF'
#!/bin/bash
echo "=== Photo Upload Monitor ==="
echo "Date: $(date)"
echo
echo "Uploads directory permissions:"
ls -la /var/www/vhosts/kegiatan.bpmpkaltim.id/public_html/uploads/ 2>/dev/null || ls -la ~/kegiatan.bpmpkaltim.id/public/uploads/ 2>/dev/null
echo
echo "Nginx status:"
systemctl status nginx --no-pager -l 2>/dev/null || echo "Cannot check nginx status"
echo
echo "Latest uploads:"
find /var/www/vhosts/kegiatan.bpmpkaltim.id/public_html/uploads/ -type f -mtime -1 2>/dev/null | head -5 || echo "No recent uploads found"
EOF

chmod +x "$MONITOR_SCRIPT"
print_info "Monitoring script created: $MONITOR_SCRIPT"

print_success "Deployment completed successfully!"