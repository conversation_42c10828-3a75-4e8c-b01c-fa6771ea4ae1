@echo off
cls
color 0A
echo =========================================================
echo    SYNTAX CHECKER - CLOUDPANEL REALTIME PHOTO FIX
echo =========================================================
echo.

echo [INFO] Checking script syntax before deployment...
echo.

:: Check if WSL is available
wsl --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] WSL not detected. Using alternative checks...
    echo.
    goto windows_check
) else (
    echo [INFO] WSL detected. Running comprehensive syntax check...
    echo.
    goto wsl_check
)

:wsl_check
echo Running syntax checker in WSL...
wsl bash -c "cd /mnt/f/online/zoom_rutin && chmod +x syntax-checker.sh && ./syntax-checker.sh"
if errorlevel 1 (
    echo.
    echo [ERROR] Syntax check failed in WSL
    goto fallback_check
) else (
    echo.
    echo [SUCCESS] WSL syntax check completed
    goto deployment_ready
)

:windows_check
echo [INFO] Running Windows-based basic checks...
echo.

:: Check if file exists
if not exist "cloudpanel-realtime-photo-fix-fixed.sh" (
    echo [ERROR] Script file not found: cloudpanel-realtime-photo-fix-fixed.sh
    echo Make sure the file exists in the current directory.
    pause
    exit /b 1
)

:: Basic file checks
echo [CHECK 1] File existence: OK
for %%i in (cloudpanel-realtime-photo-fix-fixed.sh) do set filesize=%%~zi
echo [CHECK 2] File size: %filesize% bytes

:: Check for basic shell syntax indicators
findstr /C:"#!/bin/bash" cloudpanel-realtime-photo-fix-fixed.sh >nul
if errorlevel 1 (
    echo [WARNING] Shebang not found or incorrect
) else (
    echo [CHECK 3] Shebang: OK
)

:: Check for quote balancing (basic)
echo [CHECK 4] Checking quote patterns...
findstr /R /C:"'.*'.*'.*'" cloudpanel-realtime-photo-fix-fixed.sh >nul
if not errorlevel 1 (
    echo [WARNING] Complex quote patterns detected - manual review recommended
) else (
    echo [CHECK 4] Basic quote check: OK
)

:: Check for function definitions
findstr /R /C:"^[a-zA-Z_][a-zA-Z0-9_]*() {" cloudpanel-realtime-photo-fix-fixed.sh >nul
if errorlevel 1 (
    echo [WARNING] No function definitions found
) else (
    echo [CHECK 5] Function definitions: Found
)

echo.
echo [INFO] Basic Windows checks completed
goto deployment_ready

:fallback_check
echo.
echo [INFO] Running fallback checks...
echo.

:: Very basic syntax check by looking for obvious issues
findstr /C:"EOF" cloudpanel-realtime-photo-fix-fixed.sh | find /C "EOF"
echo [INFO] EOF markers found (for HERE documents)

findstr /C:"<<" cloudpanel-realtime-photo-fix-fixed.sh | find /C "<<"
echo [INFO] HERE document markers found

echo.
goto deployment_ready

:deployment_ready
echo =========================================================
echo                   DEPLOYMENT STATUS
echo =========================================================
echo.
echo [STATUS] Script syntax validation completed
echo [STATUS] Ready for deployment to VPS
echo.
echo Available deployment options:
echo.
echo 1. Automatic deployment:
echo    - Run: deploy-fixed-realtime-photo.bat
echo    - Edit VPS_IP and credentials first
echo.
echo 2. Manual deployment:
echo    - Run: manual-fixed-deployment.bat
echo    - Follow step-by-step guide
echo.
echo 3. Direct upload with WinSCP/FileZilla:
echo    - Upload: cloudpanel-realtime-photo-fix-fixed.sh
echo    - Destination: /tmp/ on your VPS
echo    - Execute: chmod +x /tmp/cloudpanel-realtime-photo-fix-fixed.sh
echo    - Run: /tmp/cloudpanel-realtime-photo-fix-fixed.sh
echo.

echo FIXES APPLIED IN THIS VERSION:
echo ===============================
echo ✅ Fixed: unexpected EOF while looking for matching quote
echo ✅ Fixed: Quote matching in printf commands
echo ✅ Fixed: JPEG creation using echo -en instead of printf
echo ✅ Improved: Error handling and backup restore
echo ✅ Enhanced: Monitoring service reliability
echo.

set /p DEPLOY="Choose deployment method (1=Auto, 2=Manual, 3=WinSCP Guide): "

if "%DEPLOY%"=="1" (
    echo.
    echo [INFO] Starting automatic deployment...
    if exist "deploy-fixed-realtime-photo.bat" (
        start deploy-fixed-realtime-photo.bat
    ) else (
        echo [ERROR] deploy-fixed-realtime-photo.bat not found
        pause
    )
) else if "%DEPLOY%"=="2" (
    echo.
    echo [INFO] Opening manual deployment guide...
    if exist "manual-fixed-deployment.bat" (
        start manual-fixed-deployment.bat
    ) else (
        echo [ERROR] manual-fixed-deployment.bat not found
        pause
    )
) else if "%DEPLOY%"=="3" (
    echo.
    echo WINSCP/FILEZILLA DEPLOYMENT GUIDE:
    echo ==================================
    echo.
    echo 1. Open WinSCP or FileZilla
    echo 2. Connect to your VPS (IP, username, password)
    echo 3. Navigate to /tmp/ directory on VPS
    echo 4. Upload file: cloudpanel-realtime-photo-fix-fixed.sh
    echo 5. Open SSH terminal and run:
    echo.
    echo    chmod +x /tmp/cloudpanel-realtime-photo-fix-fixed.sh
    echo    /tmp/cloudpanel-realtime-photo-fix-fixed.sh
    echo.
    echo 6. Follow the script prompts
    echo 7. Test photo upload after completion
    echo.
    pause
) else (
    echo.
    echo [INFO] No deployment method selected. Exiting...
)

echo.
echo [INFO] Syntax check completed. Files ready for deployment.
pause
