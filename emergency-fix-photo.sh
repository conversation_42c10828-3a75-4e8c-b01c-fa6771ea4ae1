#!/bin/bash

# EMERGENCY FIX untuk Photo Upload 404 - Auto Detection
# Script ini akan mencari dan memperbaiki masalah photo upload secara otomatis

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

echo -e "${GREEN}"
echo "============================================"
echo "   EMERGENCY FIX PHOTO UPLOAD 404"
echo "============================================"
echo -e "${NC}"

# STEP 1: Auto-detect Next.js app locations
log_info "Mencari aplikasi Next.js di VPS..."

POSSIBLE_APPS=()

# Cari berdasarkan server.js (Next.js standalone)
while IFS= read -r -d '' app_path; do
    app_dir=$(dirname "$app_path")
    POSSIBLE_APPS+=("$app_dir")
    log_success "Ditemukan Next.js app: $app_dir"
done < <(find /home /var/www /opt /usr/local -name "server.js" -path "*/standalone/*" -print0 2>/dev/null || true)

# Cari berdasarkan package.json dengan next
while IFS= read -r -d '' package_path; do
    if grep -q '"next"' "$package_path" 2>/dev/null; then
        app_dir=$(dirname "$package_path")
        if [[ ! " ${POSSIBLE_APPS[@]} " =~ " ${app_dir} " ]]; then
            POSSIBLE_APPS+=("$app_dir")
            log_success "Ditemukan Next.js project: $app_dir"
        fi
    fi
done < <(find /home /var/www /opt /usr/local -name "package.json" -print0 2>/dev/null || true)

# Jika tidak ada yang ditemukan, gunakan default locations
if [ ${#POSSIBLE_APPS[@]} -eq 0 ]; then
    log_warning "Tidak ditemukan aplikasi Next.js otomatis, menggunakan default paths..."
    POSSIBLE_APPS=(
        "/var/www/html"
        "/home/<USER>/app"
        "/opt/app"
        "/usr/local/app"
        "/home/<USER>/app"
    )
fi

# STEP 2: Untuk setiap app yang ditemukan, lakukan fix
for APP_DIR in "${POSSIBLE_APPS[@]}"; do
    if [ ! -d "$APP_DIR" ]; then
        continue
    fi
    
    echo ""
    log_action "Memproses aplikasi di: $APP_DIR"
    
    # Buat backup timestamp
    BACKUP_DIR="/tmp/photo-fix-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # STEP 2.1: Buat struktur folder uploads jika belum ada
    if [ ! -d "$APP_DIR/public" ]; then
        log_action "Membuat folder public..."
        mkdir -p "$APP_DIR/public"
    fi
    
    if [ ! -d "$APP_DIR/public/uploads" ]; then
        log_action "Membuat folder uploads..."
        mkdir -p "$APP_DIR/public/uploads/absensi/photos"
        log_success "Folder uploads/absensi/photos dibuat"
    fi
    
    # STEP 2.2: Perbaiki duplikasi folder public/public
    if [ -d "$APP_DIR/public/public" ]; then
        log_warning "Ditemukan duplikasi folder public/public"
        
        # Backup duplikasi folder
        cp -r "$APP_DIR/public/public" "$BACKUP_DIR/" 2>/dev/null || true
        
        # Pindahkan konten dari public/public ke public
        if [ -d "$APP_DIR/public/public/uploads" ]; then
            log_action "Memindahkan file dari public/public/uploads ke public/uploads..."
            
            # Pastikan folder tujuan ada
            mkdir -p "$APP_DIR/public/uploads"
            
            # Pindahkan semua konten
            cp -r "$APP_DIR/public/public/uploads/"* "$APP_DIR/public/uploads/" 2>/dev/null || true
            
            log_success "File dipindahkan ke lokasi yang benar"
        fi
        
        # Hapus folder duplikasi
        rm -rf "$APP_DIR/public/public"
        log_success "Folder duplikasi dihapus"
    fi
    
    # STEP 2.3: Set permissions
    log_action "Mengatur permissions..."
    chown -R www-data:www-data "$APP_DIR/public/uploads" 2>/dev/null || true
    chmod -R 755 "$APP_DIR/public/uploads" 2>/dev/null || true
    log_success "Permissions diatur"
    
    # STEP 2.4: Hitung jumlah foto
    PHOTO_COUNT=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l)
    log_info "Total foto ditemukan: $PHOTO_COUNT"
    
done

# STEP 3: Setup Nginx Configuration
echo ""
log_action "Mengkonfigurasi Nginx..."

# Cari file konfigurasi nginx yang ada
NGINX_CONFIGS=()
for config_path in \
    "/etc/nginx/sites-available/default" \
    "/etc/nginx/sites-available/kegiatan.bpmpkaltim.id" \
    "/etc/nginx/conf.d/default.conf" \
    "/etc/nginx/nginx.conf"; do
    
    if [ -f "$config_path" ]; then
        NGINX_CONFIGS+=("$config_path")
    fi
done

if [ ${#NGINX_CONFIGS[@]} -eq 0 ]; then
    log_warning "Tidak ditemukan konfigurasi Nginx, membuat konfigurasi baru..."
    
    # Buat konfigurasi nginx dasar
    NGINX_CONFIG="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
    
    # Tentukan app directory yang akan digunakan (pilih yang pertama)
    if [ ${#POSSIBLE_APPS[@]} -gt 0 ] && [ -d "${POSSIBLE_APPS[0]}" ]; then
        MAIN_APP_DIR="${POSSIBLE_APPS[0]}"
    else
        MAIN_APP_DIR="/var/www/html"
    fi
    
    cat > "$NGINX_CONFIG" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name kegiatan.bpmpkaltim.id;

    # Static files untuk uploads
    location /uploads/ {
        root $MAIN_APP_DIR/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        
        # Fallback jika file tidak ditemukan
        try_files \$uri \$uri/ =404;
    }

    # Next.js app (sesuaikan dengan setup PM2 Anda)
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

    # Aktifkan site
    ln -sf "$NGINX_CONFIG" "/etc/nginx/sites-enabled/" 2>/dev/null || true
    log_success "Konfigurasi Nginx dibuat: $NGINX_CONFIG"
    
else
    # Update konfigurasi yang ada
    for config in "${NGINX_CONFIGS[@]}"; do
        log_action "Updating konfigurasi: $config"
        
        # Backup
        cp "$config" "$BACKUP_DIR/nginx-$(basename $config).backup"
        
        # Cek apakah sudah ada konfigurasi uploads
        if ! grep -q "location /uploads/" "$config"; then
            # Tentukan app directory
            if [ ${#POSSIBLE_APPS[@]} -gt 0 ] && [ -d "${POSSIBLE_APPS[0]}" ]; then
                MAIN_APP_DIR="${POSSIBLE_APPS[0]}"
            else
                MAIN_APP_DIR="/var/www/html"
            fi
            
            # Tambahkan konfigurasi uploads sebelum location /
            sed -i '/location \/ {/i\
    # Static files untuk uploads\
    location /uploads/ {\
        root '"$MAIN_APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        add_header Access-Control-Allow-Origin "*";\
        try_files $uri $uri/ =404;\
    }\
' "$config"
            
            log_success "Konfigurasi uploads ditambahkan ke $config"
        else
            log_info "Konfigurasi uploads sudah ada di $config"
        fi
    done
fi

# STEP 4: Restart services
log_action "Restart services..."

# Test nginx configuration
if nginx -t 2>/dev/null; then
    systemctl reload nginx 2>/dev/null || service nginx reload 2>/dev/null || true
    log_success "Nginx direload"
else
    log_error "Nginx configuration error - tidak direload"
fi

# Restart PM2 jika ada
if command -v pm2 >/dev/null 2>&1; then
    pm2 restart all 2>/dev/null || true
    log_success "PM2 direstart"
fi

# STEP 5: Test akses file
echo ""
log_action "Testing akses file..."

# Sample file untuk test
SAMPLE_PHOTO="test-photo.jpg"
TEST_URL="https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/test-folder/$SAMPLE_PHOTO"

# Buat file test
for APP_DIR in "${POSSIBLE_APPS[@]}"; do
    if [ -d "$APP_DIR/public/uploads" ]; then
        mkdir -p "$APP_DIR/public/uploads/absensi/photos/test-folder"
        echo "Test file" > "$APP_DIR/public/uploads/absensi/photos/test-folder/$SAMPLE_PHOTO"
        break
    fi
done

# Test dengan curl
if command -v curl >/dev/null 2>&1; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        log_success "Test akses berhasil! Status: $HTTP_STATUS"
    else
        log_warning "Test akses gagal. Status: $HTTP_STATUS"
        log_info "URL test: $TEST_URL"
    fi
else
    log_info "Curl tidak tersedia, skip test otomatis"
fi

# STEP 6: Summary
echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   EMERGENCY FIX SELESAI"
echo "============================================"
echo -e "${NC}"

log_success "Backup dibuat di: $BACKUP_DIR"

echo ""
log_info "LANGKAH SELANJUTNYA:"
echo "1. Test upload foto baru di aplikasi"
echo "2. Cek apakah foto lama sekarang bisa diakses"
echo "3. Monitor log nginx untuk error"

echo ""
log_info "UNTUK DEBUG LEBIH LANJUT:"
echo "- Cek log nginx: tail -f /var/log/nginx/error.log"
echo "- Cek log aplikasi: pm2 logs"
echo "- Test URL langsung: curl -I https://kegiatan.bpmpkaltim.id/uploads/..."

echo ""
log_warning "Jika masih ada masalah, jalankan:"
echo "ls -la /path/to/app/public/uploads/absensi/photos/"
echo "nginx -t"
echo "systemctl status nginx"
