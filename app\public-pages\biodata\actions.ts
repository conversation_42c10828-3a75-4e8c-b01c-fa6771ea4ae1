// File: /app/public-pages/biodata/actions.ts
'use server';

import { prisma } from '../../../lib/prisma';
import { validateRegistrationLink } from '../../../lib/validateLink';
import { pelatihan_jenjang } from '@prisma/client';
import { randomUUID } from 'crypto';

// Interface untuk data formulir biodata
interface BiodataFormData {
  nama: string;
  tempat_lahir: string;
  tanggal_lahir: string;
  pendidikan: string;
  jenis_kelamin: string;
  jenjang: pelatihan_jenjang;
  nip?: string;
  pangkat_golongan?: string;
  jabatan: string;
  unit_kerja: string;
  alamat_unit_kerja: string;
  npwp?: string;
  email: string;
  no_hp: string;
  kota: string; // Field kota untuk eksport PDF
  tanda_tangan: string;
  foto_cloudinary_id?: string; // Cloudinary public_id
  foto_url?: string; // Cloudinary secure_url
}

// Fungsi validasi email
function validateEmail(email: string): boolean {
  const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return pattern.test(email);
}

// Fungsi validasi nomor telepon (format Indonesia)
function validatePhoneNumber(phone: string): boolean {
  const pattern = /^(\+62|62|0)8[1-9][0-9]{6,10}$/;
  return pattern.test(phone);
}

// Fungsi validasi NPWP (jika diisi)
function validateNPWP(npwp?: string): boolean {
  if (!npwp || npwp.trim() === '') return true; // Opsional, jadi valid jika kosong
  
  // Format NPWP: xx.xxx.xxx.x-xxx.xxx (15 digit plus pemisah)
  const cleanNPWP = npwp.replace(/[^\d]/g, '');
  return cleanNPWP.length === 15;
}

// Fungsi untuk sanitasi input
function sanitizeInput(input: string): string {
  // Hapus tag HTML dan trim
  return input
    .replace(/<[^>]*>/g, '')
    .trim();
}

export async function submitBiodata(link: string, formData: BiodataFormData) {
  try {
    // Validasi link registrasi
    const validation = await validateRegistrationLink(link);
    if (!validation.valid || !validation.pelatihan) {
      return {
        success: false,
        message: validation.message || 'Link registrasi tidak valid'
      };
    }
    
    // Validasi input penting
    if (!validateEmail(formData.email)) {
      return {
        success: false,
        message: 'Format email tidak valid'
      };
    }
    
    if (!validatePhoneNumber(formData.no_hp)) {
      return {
        success: false,
        message: 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx'
      };
    }
    
    if (!validateNPWP(formData.npwp)) {
      return {
        success: false,
        message: 'Format NPWP tidak valid'
      };
    }
    
    // Validasi tanda tangan
    if (!formData.tanda_tangan || formData.tanda_tangan.length < 100) {
      return {
        success: false,
        message: 'Tanda tangan tidak valid'
      };
    }
    
    // Validasi tanggal lahir
    const birthDate = new Date(formData.tanggal_lahir);
    const today = new Date();
    if (isNaN(birthDate.getTime()) || birthDate > today) {
      return {
        success: false,
        message: 'Tanggal lahir tidak valid'
      };
    }
      // Sanitasi input untuk mencegah XSS dan injeksi
    const sanitizedData = {
      ...formData,
      nama: sanitizeInput(formData.nama),
      tempat_lahir: sanitizeInput(formData.tempat_lahir),
      // tanggal_lahir tidak perlu sanitasi karena format Date
      pendidikan: sanitizeInput(formData.pendidikan),
      jenis_kelamin: sanitizeInput(formData.jenis_kelamin),
      // jenjang tidak perlu sanitasi karena enum
      nip: formData.nip ? sanitizeInput(formData.nip) : null,
      pangkat_golongan: formData.pangkat_golongan ? sanitizeInput(formData.pangkat_golongan) : null,
      jabatan: sanitizeInput(formData.jabatan),
      unit_kerja: sanitizeInput(formData.unit_kerja),
      alamat_unit_kerja: sanitizeInput(formData.alamat_unit_kerja),
      npwp: formData.npwp ? sanitizeInput(formData.npwp) : null,
      email: sanitizeInput(formData.email.toLowerCase()),
      no_hp: sanitizeInput(formData.no_hp),
      kota: sanitizeInput(formData.kota),
      // tanda_tangan adalah data binary, tidak perlu sanitasi
    };
      // Handle photo data if provided
    let foto_path = null;
    let foto_url = null;
    
    if (formData.foto_cloudinary_id && formData.foto_url) {
      // Use Cloudinary data directly
      foto_path = formData.foto_cloudinary_id; // Store cloudinary public_id as foto_path for compatibility
      foto_url = formData.foto_url; // Store cloudinary secure_url
    }
    
    // Simpan data biodata ke database
    const biodata = await prisma.biodata.create({
      data: {
        id: randomUUID(),
        pelatihanId: validation.pelatihan.id,
        nama: sanitizedData.nama,
        tempat_lahir: sanitizedData.tempat_lahir,
        tanggal_lahir: new Date(sanitizedData.tanggal_lahir),
        pendidikan: sanitizedData.pendidikan,
        jenis_kelamin: sanitizedData.jenis_kelamin,
        jenjang: sanitizedData.jenjang,
        nip: sanitizedData.nip,
        pangkat_golongan: sanitizedData.pangkat_golongan,
        jabatan: sanitizedData.jabatan,
        unit_kerja: sanitizedData.unit_kerja,
        alamat_unit_kerja: sanitizedData.alamat_unit_kerja,
        npwp: sanitizedData.npwp,
        email: sanitizedData.email,
        no_hp: sanitizedData.no_hp,
        kota: sanitizedData.kota,
        tanda_tangan: sanitizedData.tanda_tangan,
        foto_path: foto_path,
        foto_url: foto_url
      }
    });
    
    return {
      success: true,
      data: biodata
    };
  } catch (error) {
    console.error('Error submitting biodata:', error);
    return {
      success: false,
      message: 'Terjadi kesalahan saat menyimpan data'
    };
  }
}