# 🚀 SOLUSI IMMEDIATE - PHOTO 404 FIX

## 📊 HASIL DIAGNOSA ANDA:
- ❌ **Folder uploads tidak ditemukan**: Path aplikasi mungkin salah
- ❌ **Nginx config tidak ditemukan**: Lokasi konfigurasi berbeda
- ✅ **File foto mungkin ada**: Tapi di lokasi yang salah

## 🎯 SOLUSI IMMEDIATE (3 LANGKAH)

### **STEP 1: Upload Script Advanced**
```cmd
cd f:\online\zoom_rutin
scp advanced-diagnosa.sh <EMAIL>:/home/<USER>/
scp universal-fix.sh <EMAIL>:/home/<USER>/
```

### **STEP 2: SSH dan Jalankan Pencarian**
```bash
ssh <EMAIL>
chmod +x /home/<USER>/advanced-diagnosa.sh
chmod +x /home/<USER>/universal-fix.sh

# Jalankan pencarian otomatis
./advanced-diagnosa.sh
```

**Expected Output:**
- Lokasi aplikasi Next.js yang benar
- Path file konfigurasi nginx
- Lokasi file foto (jika ada)

### **STEP 3: Jalankan Perbaikan Universal**
```bash
# Script ini otomatis mendeteksi struktur VPS
sudo ./universal-fix.sh
```

**Script akan:**
- ✅ Otomatis mencari aplikasi Next.js
- ✅ Memperbaiki duplikasi folder public/public/
- ✅ Mengupdate nginx config
- ✅ Set permissions yang benar
- ✅ Restart services

## 🔍 JIKA ADVANCED-DIAGNOSA MENEMUKAN STRUKTUR

Setelah `./advanced-diagnosa.sh`, Anda akan melihat output seperti:
```
[SUCCESS] Kemungkinan lokasi aplikasi ditemukan:
   - /home/<USER>/app
     ✅ Folder public ditemukan
     ✅ Folder uploads ditemukan
     📸 15 foto ditemukan
     ⚠️  DUPLIKASI: public/public/ ditemukan!
     📸 12 foto di duplikasi

[SUCCESS] File konfigurasi nginx ditemukan:
   - /etc/nginx/sites-available/kegiatan.bpmpkaltim.id
     ✅ Mengandung domain kegiatan.bpmpkaltim.id
     📁 Root directory: /var/www/html

[SUCCESS] File foto ditemukan di:
   - /home/<USER>/app/public/public/uploads/absensi/photos/...
   ⚠️  File di lokasi duplikasi!
```

**Jika hasil seperti ini, langsung jalankan:**
```bash
sudo ./universal-fix.sh
```

## 🛠️ MANUAL FIX (Jika Script Gagal)

Jika script universal gagal, gunakan informasi dari diagnosa untuk fix manual:

### 1. Pindahkan File dari Duplikasi
```bash
# Ganti dengan path yang ditemukan diagnosa
APP_DIR="/home/<USER>/app"  # Sesuaikan dengan temuan

# Pindahkan dari duplikasi
sudo mkdir -p $APP_DIR/public/uploads
sudo cp -r $APP_DIR/public/public/uploads/* $APP_DIR/public/uploads/
sudo rm -rf $APP_DIR/public/public

# Set permissions
sudo chown -R www-data:www-data $APP_DIR/public/uploads
sudo chmod -R 755 $APP_DIR/public/uploads
```

### 2. Update Nginx Config
```bash
# Edit config yang ditemukan diagnosa
sudo nano /etc/nginx/sites-available/kegiatan.bpmpkaltim.id

# Tambahkan di dalam server block:
location /uploads/ {
    root /home/<USER>/app/public;  # Sesuaikan path
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri $uri/ =404;
}

# Test dan reload
sudo nginx -t
sudo systemctl reload nginx
```

### 3. Restart Aplikasi
```bash
pm2 restart all
```

## ✅ VERIFIKASI BERHASIL

### Test URL Direct:
```bash
curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/915b8bfd-14ad-48d0-805f-6509f9540dd3/absensi-2025-06-07T08-48-47-545Z-db7abc29-1921-46dd-8fa8-d1a6f83ec1a0.jpg
```
**Expected:** `HTTP/1.1 200 OK`

### Test Upload Baru:
1. Buka form absensi
2. Ambil foto
3. Submit
4. Cek detail absensi
5. **Expected:** Foto muncul tanpa error

## ⏱️ ESTIMASI WAKTU
- **Diagnosa**: 2 menit
- **Universal fix**: 3 menit
- **Manual fix**: 5 menit
- **Total**: < 10 menit

## 🆘 EMERGENCY CONTACT

Jika semua gagal, kirim output dari:
```bash
./advanced-diagnosa.sh > diagnosa-result.txt 2>&1
cat diagnosa-result.txt
```

---
**PRIORITAS**: Jalankan `advanced-diagnosa.sh` dulu untuk mendapat informasi akurat tentang struktur VPS Anda.
