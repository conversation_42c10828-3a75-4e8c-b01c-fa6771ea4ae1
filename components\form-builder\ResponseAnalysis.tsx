'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { ApexOptions } from 'apexcharts';

// Import ApexCharts secara dinamis untuk menghindari error SSR
const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
  loading: () => {
    return <Skeleton className="w-full h-[300px]" />;
  }
});

type ResponseAnalysisProps = {
  question: {
    id: string;
    questionText: string;
    questionType: string;
    options?: {
      choices?: string[];
    };
  };
  submissions: Array<{
    id: string;
    answers: Array<{
      questionId: string;
      answerValue?: string;
      answerJson?: any;
    }>;
  }>;
};

export default function ResponseAnalysis({ question, submissions }: ResponseAnalysisProps) {
  const [chartData, setChartData] = useState<any>(null);
  const [isClient, setIsClient] = useState(false);
  const [activeTab, setActiveTab] = useState('pie');
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Pastikan kode hanya dijalankan di client-side
  useEffect(() => {
    setIsClient(true);
    
    // Cleanup function untuk mencegah error saat komponen di-unmount
    return () => {
      // Reset state untuk mencegah update pada komponen yang sudah di-unmount
      setChartData(null);
    };
  }, []);
  // Proses data untuk chart
  useEffect(() => {
    if (!isClient || !question || !submissions || submissions.length === 0) {
      return;
    }

    // Fungsi untuk memproses data berdasarkan tipe pertanyaan
    const processData = () => {
      try {
        // Hanya proses tipe pertanyaan yang relevan
        if (!['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'DROPDOWN', 'CHECKLIST'].includes(question.questionType)) {
          return { labels: [], series: [] };
        }

        // Pastikan ada pilihan untuk dianalisis
        const choices = question.options?.choices || [];
        
        if (choices.length === 0) {
          return { labels: [], series: [] };
        }

        // Inisialisasi counter untuk setiap pilihan
        const choiceCounts: Record<string, number> = {};
        choices.forEach(choice => {
          choiceCounts[choice] = 0;
        });        // Hitung jawaban
        let hasValidAnswers = false;
        
        submissions.forEach((submission, _idx) => {
          const answer = submission.answers.find(a => a.questionId === question.id);
          if (!answer) {
            return;
          }
          
          if (question.questionType === 'MULTIPLE_CHOICE') {
            try {
              let selectedChoices: string[] = [];
                // Coba parse dari answerValue
              if (answer.answerValue) {
                if (typeof answer.answerValue === 'string') {
                  try {
                    const parsed = JSON.parse(answer.answerValue);
                    
                    if (Array.isArray(parsed)) {
                      selectedChoices = parsed;
                      hasValidAnswers = true;
                    }
                  } catch (_e) {
                    // Jika bukan JSON valid, mungkin string tunggal
                    selectedChoices = [answer.answerValue];
                    hasValidAnswers = true;
                  }
                } else if (Array.isArray(answer.answerValue)) {
                  selectedChoices = answer.answerValue;
                  hasValidAnswers = true;
                }
              }
              
              // Coba parse dari answerJson jika answerValue tidak berhasil
              if (selectedChoices.length === 0 && answer.answerJson) {
                if (typeof answer.answerJson === 'string') {
                  try {
                    const parsed = JSON.parse(answer.answerJson);
                    
                    if (Array.isArray(parsed)) {
                      selectedChoices = parsed;
                      hasValidAnswers = true;
                    }
                  } catch (_e) {
                    // Error parsing answerJson string
                  }
                } else if (Array.isArray(answer.answerJson)) {
                  selectedChoices = answer.answerJson;
                  hasValidAnswers = true;                } else if (typeof answer.answerJson === 'object' && answer.answerJson !== null) {
                  // Format {index: boolean} untuk CHECKLIST
                  Object.entries(answer.answerJson).forEach(([index, checked]) => {
                    if (checked && choices[parseInt(index)]) {
                      selectedChoices.push(choices[parseInt(index)]);
                      hasValidAnswers = true;
                    }
                  });
                }              }              // Hitung pilihan yang dipilih
              if (selectedChoices.length > 0) {
                selectedChoices.forEach(choice => {
                  if (choiceCounts[choice] !== undefined) {
                    choiceCounts[choice]++;
                  }
                });
              }
            } catch (_e) {
              // Silently handle errors in processing multiple choice answers
            }
          } else {
            // Untuk SINGLE_CHOICE, DROPDOWN, dan format lainnya
            if (answer.answerValue && choiceCounts[answer.answerValue] !== undefined) {
              choiceCounts[answer.answerValue]++;
              hasValidAnswers = true;
            }
          }
        });
        
        // Jika tidak ada jawaban valid, kembalikan data kosong
        if (!hasValidAnswers) {
          return { labels: [], series: [] };
        }
          // Format data untuk chart
        const labels = Object.keys(choiceCounts);
        const series = Object.values(choiceCounts);
        
        return {
          labels,
          series
        };      } catch (_error) {
        return { labels: [], series: [] };
      }
    };
      // Set timeout untuk memastikan DOM sudah siap
    const timer = setTimeout(() => {
      const data = processData();
      setChartData(data);
    }, 0);
    
    return () => clearTimeout(timer);
  }, [isClient, question, submissions]);

  // Handler untuk perubahan tab
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Cek apakah ada data yang valid untuk chart
  const hasValidData = chartData && 
                      chartData.series &&                      chartData.series.length > 0 && 
                      chartData.series.some((val: number) => val > 0);

  // Jika belum di client-side atau data belum siap, tampilkan skeleton
  if (!isClient) {
    return <Skeleton className="w-full h-[300px]" />;
  }

  // Jika tidak ada data valid, tampilkan pesan
  if (!hasValidData) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{question.questionText}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[200px]">
            <p className="text-sm text-gray-500">
              Tidak ada data yang cukup untuk analisis atau tipe pertanyaan tidak didukung.
              <br />
              (Debug: {JSON.stringify({
                hasChartData: !!chartData,
                hasSeries: chartData?.series?.length > 0,
                seriesValues: chartData?.series
              })})
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Konfigurasi untuk pie chart
  const pieOptions: ApexOptions = {
    chart: {
      type: 'pie',
    },
    labels: chartData.labels,
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 300
        },
        legend: {
          position: 'bottom'
        }
      }
    }],
    tooltip: {
      y: {
        formatter: function(val) {
          return val.toString();
        }
      }
    }
  };

  // Konfigurasi untuk bar chart
  const barOptions: ApexOptions = {
    chart: {
      type: 'bar',
    },
    plotOptions: {
      bar: {
        horizontal: false,
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      categories: chartData.labels,
    },
    tooltip: {
      y: {
        formatter: function(val) {
          return val.toString();
        }
      }
    }  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{question.questionText}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="pie">Pie Chart</TabsTrigger>
            <TabsTrigger value="bar">Bar Chart</TabsTrigger>
            <TabsTrigger value="table">Table</TabsTrigger>
          </TabsList>
          
          <TabsContent value="pie" className="mt-0">
            <div ref={chartContainerRef}>
              {hasValidData ? (
                <div className="chart-container" style={{ minHeight: '400px' }}>
                  {activeTab === 'pie' && (
                    <Chart
                      options={{
                        ...pieOptions,
                        labels: chartData.labels,
                      }}
                      series={chartData.series}
                      type="pie"
                      height={400}
                    />
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-[300px]">
                  <p className="text-gray-500">No data available</p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="bar" className="mt-0">
            <div ref={chartContainerRef}>
              {hasValidData ? (
                <div className="chart-container" style={{ minHeight: '400px' }}>
                  {activeTab === 'bar' && (
                    <Chart
                      options={barOptions}
                      series={[{ name: 'Responses', data: chartData.series }]}
                      type="bar"
                      height={400}
                    />
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-[300px]">
                  <p className="text-gray-500">No data available</p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="table" className="mt-0">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="p-2 text-left border">Option</th>
                    <th className="p-2 text-left border">Count</th>
                    <th className="p-2 text-left border">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {chartData.labels.map((label: string, index: number) => {
                    const total = chartData.series.reduce((a: number, b: number) => a + b, 0);
                    const percentage = total > 0 
                      ? ((chartData.series[index] / total) * 100).toFixed(1) 
                      : '0';
                    
                    return (
                      <tr key={label}>
                        <td className="p-2 border">{label}</td>
                        <td className="p-2 border">{chartData.series[index]}</td>
                        <td className="p-2 border">{percentage}%</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
