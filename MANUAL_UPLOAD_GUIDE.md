# 📁 PANDUAN UPLOAD MANUAL KE VPS

## 🎯 File yang Perlu Diupload

| File | Ukuran | Lokasi | Tujuan VPS |
|------|--------|---------|------------|
| `deployment.tar.gz` | 34.9 MB | `.next\deployment.tar.gz` | `/home/<USER>/` |
| `fix-photo-upload-vps.sh` | 6.8 KB | `fix-photo-upload-vps.sh` | `/home/<USER>/` |
| `nginx-config.txt` | 2.1 KB | `nginx-config.txt` | `/home/<USER>/` |

## 🔧 METODE 1: SCP (Secure Copy) - DIREKOMENDASIKAN

### Persiapan
```cmd
cd f:\online\zoom_rutin
```

### Upload File Satu per Satu
```cmd
REM Upload deployment archive (file terbesar)
scp .next\deployment.tar.gz <EMAIL>:/home/<USER>/

REM Upload script perbaikan
scp fix-photo-upload-vps.sh <EMAIL>:/home/<USER>/

REM Upload konfigurasi nginx
scp nginx-config.txt <EMAIL>:/home/<USER>/
```

### Verifikasi Upload
```cmd
ssh <EMAIL> "ls -la /home/<USER>/"
```

## 🔧 METODE 2: SFTP (Secure File Transfer Protocol)

### Membuka SFTP
```cmd
sftp <EMAIL>
```

### Navigasi dan Upload
```bash
# Di dalam SFTP session
cd /home/<USER>/

# Upload file satu per satu
put .next/deployment.tar.gz
put fix-photo-upload-vps.sh
put nginx-config.txt

# Cek file yang sudah diupload
ls -la

# Keluar dari SFTP
quit
```

## 🔧 METODE 3: WinSCP (GUI) - PALING MUDAH

### Download dan Install WinSCP
1. Download dari: https://winscp.net/eng/download.php
2. Install WinSCP

### Setup Koneksi
1. Buka WinSCP
2. **Host name**: `kegiatan.bpmpkaltim.id`
3. **User name**: `user` (ganti dengan username VPS Anda)
4. **Password**: (masukkan password VPS)
5. **Port**: 22
6. Klik **Login**

### Upload Files
1. **Panel Kiri**: Navigate ke `f:\online\zoom_rutin`
2. **Panel Kanan**: Navigate ke `/home/<USER>/`
3. **Drag & Drop** file berikut dari kiri ke kanan:
   - `.next\deployment.tar.gz`
   - `fix-photo-upload-vps.sh`
   - `nginx-config.txt`

## 🔧 METODE 4: FileZilla (GUI Alternative)

### Setup FileZilla
1. Download FileZilla Client
2. **Host**: `sftp://kegiatan.bpmpkaltim.id`
3. **Username**: `user`
4. **Password**: (password VPS)
5. **Port**: 22

### Upload Process
1. **Local Site**: Navigate ke `f:\online\zoom_rutin`
2. **Remote Site**: Navigate ke `/home/<USER>/`
3. **Double-click** files untuk upload

## 🔧 METODE 5: rsync (Advanced)

```cmd
REM Upload dengan progress dan resume capability
rsync -avz --progress .next\deployment.tar.gz <EMAIL>:/home/<USER>/
rsync -avz --progress fix-photo-upload-vps.sh <EMAIL>:/home/<USER>/
rsync -avz --progress nginx-config.txt <EMAIL>:/home/<USER>/
```

## ⚠️ TROUBLESHOOTING

### Jika SCP/SSH Tidak Berfungsi
```cmd
REM Test koneksi SSH terlebih dahulu
ssh <EMAIL> "echo 'Koneksi berhasil'"

REM Jika ada masalah permission, coba:
ssh <EMAIL> "mkdir -p /home/<USER>/home/<USER>"
```

### Jika Upload Terputus
```cmd
REM Resume upload dengan rsync
rsync -avz --partial --progress .next\deployment.tar.gz <EMAIL>:/home/<USER>/
```

### Verifikasi File Size
```cmd
REM Cek ukuran file lokal
dir .next\deployment.tar.gz

REM Cek ukuran file di VPS
ssh <EMAIL> "ls -lh /home/<USER>/deployment.tar.gz"
```

## 🚀 SETELAH UPLOAD SELESAI

### Langkah Selanjutnya di VPS
```bash
# SSH ke VPS
ssh <EMAIL>

# Verifikasi file sudah ada
ls -la /home/<USER>/

# Lanjutkan dengan deployment
mkdir -p /home/<USER>/deployment
cd /home/<USER>/deployment
tar -xzf ../deployment.tar.gz
chmod +x /home/<USER>/fix-photo-upload-vps.sh
sudo /home/<USER>/fix-photo-upload-vps.sh
```

## 💡 TIPS

1. **File Terbesar**: `deployment.tar.gz` (34.9 MB) akan memakan waktu paling lama
2. **Koneksi Lambat**: Gunakan rsync untuk resume capability
3. **GUI Preferred**: WinSCP adalah yang paling user-friendly
4. **Command Line**: SCP adalah yang paling cepat dan reliable
5. **Progress Monitoring**: rsync menampilkan progress bar

---
**Rekomendasi**: Gunakan **WinSCP** jika Anda prefer GUI, atau **SCP** jika familiar dengan command line.
