'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Chart as ChartJS, 
  ArcElement, 
  Tooltip, 
  Legend, 
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title 
} from 'chart.js';
import { Doughnut, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  ArcElement, 
  Tooltip, 
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

// Tambahkan prop opsional chartContainerRef agar bisa di-inject dari parent
interface ResponseAnalysisProps {
  question: {
    id: string;
    questionText: string;
    questionType: string;
    options?: {
      choices?: string[];
    };
  };
  submissions: Array<{
    id: string;
    answers: Array<{
      questionId: string;
      answerValue?: string;
      answerJson?: any;
    }>;
  }>;
  chartContainerRef?: (el: HTMLDivElement | null) => void;
  onChartInstance?: (chart: ChartJS | null) => void; // Tambahan prop baru
}

export default function ResponseAnalysis({ question, submissions, chartContainerRef, onChartInstance }: ResponseAnalysisProps) {
  const [chartData, setChartData] = useState<{
    labels: string[];
    datasets: Array<{
      data: number[];
      backgroundColor: string[];
      borderColor?: string[];
      borderWidth?: number;
    }>;
  }>({
    labels: [],
    datasets: [{
      data: [],
      backgroundColor: [],
    }]
  });
  const [isClient, setIsClient] = useState(false);
  const [activeTab, setActiveTab] = useState('pie');
  const chartContainerRefLocal = useRef<HTMLDivElement>(null);
  const doughnutRef = useRef<any>(null);
  const barRef = useRef<any>(null);

  // Ensure code only runs client-side
  useEffect(() => {
    setIsClient(true);
    
    // Cleanup function to prevent errors when component is unmounted
    return () => {
      // No cleanup needed for Chart.js as it will be automatically unmounted
    };
  }, []);

  // Process data for chart
  useEffect(() => {
    if (!isClient || !question || !submissions || submissions.length === 0) {
      return;
    }

    const processData = () => {
      try {
        // Only process relevant question types
        if (!['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'DROPDOWN', 'CHECKLIST'].includes(question.questionType)) {
          return { 
            labels: [], 
            datasets: [{ data: [], backgroundColor: [] }] 
          };
        }

        // Get all possible choices
        const choices = question.options?.choices || [];
        if (choices.length === 0) {
          return { 
            labels: [], 
            datasets: [{ data: [], backgroundColor: [] }] 
          };
        }

        // Initialize counter for each choice
        const choiceCounts: Record<string, number> = {};
        choices.forEach(choice => {
          choiceCounts[choice] = 0;
        });

        // Count responses for each choice
        submissions.forEach(submission => {
          const answer = submission.answers.find(a => a.questionId === question.id);

          if (answer) {
            if (question.questionType === 'SINGLE_CHOICE' || question.questionType === 'DROPDOWN') {
              // For single choice and dropdown
              if (answer.answerValue && choiceCounts[answer.answerValue] !== undefined) {
                choiceCounts[answer.answerValue]++;
              }
            } else if (question.questionType === 'MULTIPLE_CHOICE' || question.questionType === 'CHECKLIST') {
              // For multiple choice and checklist
              let selectedChoices: string[] = [];

              // Try different possible answer formats
              if (Array.isArray(answer.answerJson)) {
                selectedChoices = answer.answerJson;
              } else if (answer.answerJson && typeof answer.answerJson === 'object') {
                // {index: boolean} format for CHECKLIST
                Object.entries(answer.answerJson).forEach(([index, checked]) => {
                  if (checked && choices[parseInt(index)]) {
                    selectedChoices.push(choices[parseInt(index)]);
                  }
                });
              } else if (answer.answerValue) {
                try {
                  const parsed = JSON.parse(answer.answerValue);
                  if (Array.isArray(parsed)) {
                    selectedChoices = parsed;
                  }
                } catch {
                  // If not valid JSON, might be a single string
                  selectedChoices = [answer.answerValue];
                }
              }

              selectedChoices.forEach((choice: string) => {
                if (choiceCounts[choice] !== undefined) {
                  choiceCounts[choice]++;
                }
              });
            }
          }
        });
          
        // Format data for chart
        const labels = Object.keys(choiceCounts);
        const data = Object.values(choiceCounts);
        
        // Generate colors for chart
        const backgroundColors = [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 206, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
          'rgba(255, 159, 64, 0.8)',
          'rgba(199, 199, 199, 0.8)',
          'rgba(83, 102, 255, 0.8)',
          'rgba(255, 99, 255, 0.8)',
          'rgba(178, 102, 255, 0.8)'
        ];
        
        const borderColors = backgroundColors.map(color => color.replace('0.8', '1'));
        
        // Generate enough colors for all data points
        const generateColors = (count: number) => {
          const colors = [];
          for (let i = 0; i < count; i++) {
            colors.push(backgroundColors[i % backgroundColors.length]);
          }
          return colors;
        };
        
        return {
          labels,
          datasets: [{
            data,
            backgroundColor: generateColors(data.length),
            borderColor: borderColors.slice(0, data.length),
            borderWidth: 1
          }]
        };
      } catch (error) {
        console.error('Error processing chart data:', error);
        return { 
          labels: [], 
          datasets: [{ data: [], backgroundColor: [] }] 
        };
      }
    };
    
    // Set timeout to ensure DOM is ready
    const timer = setTimeout(() => {
      setChartData(processData());
    }, 0);
    
    return () => clearTimeout(timer);
  }, [isClient, question, submissions]);

  // Add function to calculate percentage
  const calculatePercentage = (value: number, total: number) => {
    if (total === 0) return '0%';
    return `${((value / total) * 100).toFixed(1)}%`;
  };

  // Kirim instance Chart.js ke parent saat tab aktif berubah
  useEffect(() => {
    let chart: ChartJS | null = null;
    if (
      activeTab === 'pie' &&
      doughnutRef.current &&
      doughnutRef.current instanceof Object &&
      doughnutRef.current.chartInstance
    ) {
      chart = doughnutRef.current.chartInstance;
    } else if (
      activeTab === 'bar' &&
      barRef.current &&
      barRef.current instanceof Object &&
      barRef.current.chartInstance
    ) {
      chart = barRef.current.chartInstance;
    }
    if (typeof onChartInstance === 'function') {
      onChartInstance(chart);
    }
  }, [activeTab, chartData, onChartInstance]);

  // Check if data is valid for display
  const hasValidData = chartData.datasets[0].data.length > 0 && 
                      chartData.datasets[0].data.some(value => value > 0);

  // Doughnut chart options
  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '50%',
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = context.chart.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) + '%' : '0%';
            return `${label}: ${value} (${percentage})`;
          }
        }
      }
    }
  };

  // Bar chart options
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      title: {
        display: true,
        text: 'Jumlah Respons'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  };

  // If not client-side or data isn't ready, show skeleton
  if (!isClient) {
    return <Skeleton className="w-full h-[300px]" />;
  }

  // If no valid data, show message
  if (!hasValidData) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{question?.questionText || 'Pertanyaan'}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-500">
            Tidak ada data yang cukup untuk analisis atau tipe pertanyaan tidak didukung.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{question.questionText}</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="pie">Doughnut Chart</TabsTrigger>
            <TabsTrigger value="bar">Bar Chart</TabsTrigger>
            <TabsTrigger value="table">Table</TabsTrigger>
          </TabsList>
          
          <TabsContent value="pie" className="mt-0">
            <div
              ref={chartContainerRef ? chartContainerRef : chartContainerRefLocal}
              style={{ height: '400px', position: 'relative', background: '#fff' }}
            >
              {isClient && activeTab === 'pie' && (
                <Doughnut
                  ref={doughnutRef}
                  data={{
                    labels: chartData.labels,
                    datasets: chartData.datasets
                  }} 
                  options={doughnutOptions}
                />
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="bar" className="mt-0">
            <div
              ref={chartContainerRef ? chartContainerRef : chartContainerRefLocal}
              style={{ height: '400px', position: 'relative', background: '#fff' }}
            >
              {isClient && activeTab === 'bar' && (
                <Bar
                  ref={barRef}
                  data={{
                    labels: chartData.labels,
                    datasets: [{
                      label: 'Jumlah Respons',
                      data: chartData.datasets[0].data,
                      backgroundColor: chartData.datasets[0].backgroundColor,
                      borderColor: chartData.datasets[0].borderColor,
                      borderWidth: 1
                    }]
                  }}
                  options={barOptions}
                />
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="table" className="mt-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Pilihan</TableHead>
                  <TableHead>Jumlah Respons</TableHead>
                  <TableHead>Persentase</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {chartData.labels.map((label, index) => {
                  const value = chartData.datasets[0].data[index];
                  const total = chartData.datasets[0].data.reduce((sum, val) => sum + val, 0);
                  return (
                    <TableRow key={index}>
                      <TableCell>{label}</TableCell>
                      <TableCell>{value}</TableCell>
                      <TableCell>{calculatePercentage(value, total)}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
