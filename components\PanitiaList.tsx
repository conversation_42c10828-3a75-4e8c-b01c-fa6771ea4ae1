'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Button from './Button';
import { formatIndonesiaDate } from '../utils/dateUtils';

interface Panitia {
  id: string;
  pegawai: {
    nama: string;
    nip: string;
    jabatan: string;
  };
  jabatan: string; // This will be enum value
  lokasi: {
    kab_kota: string;
  };
  no_surat: string | null;
  keterangan: string | null;
}

interface PelatihData {
  tgl_mulai: string;
  tgl_berakhir: string;
}

interface PanitiaListProps {
  pelatihanId: string;
  userRole: string;
}

export default function PanitiaList({ pelatihanId, userRole }: PanitiaListProps) {
  const [panitiaList, setPanitiaList] = useState<Panitia[]>([]);
  const [pelatihan, setPelatihan] = useState<PelatihData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConfirmingDelete, setIsConfirmingDelete] = useState<string | null>(null);

  const isAdmin = ['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(userRole);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch panitia list
        const response = await fetch(`/api/panitia?pelatihanId=${pelatihanId}`);
        if (!response.ok) {
          throw new Error('Gagal mengambil data panitia');
        }
        const data = await response.json();
        setPanitiaList(data);

        // Fetch pelatihan data to get dates
        const pelatihanResponse = await fetch(`/api/pelatihan/${pelatihanId}`);
        if (!pelatihanResponse.ok) {
          throw new Error('Gagal mengambil data pelatihan');
        }
        const pelatihanData = await pelatihanResponse.json();
        
        // Check if response has nested data structure
        if (pelatihanData.success && pelatihanData.data) {
          setPelatihan(pelatihanData.data);
        } else {
          setPelatihan(pelatihanData);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Terjadi kesalahan');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [pelatihanId]);
  // Format tanggal ke format Indonesia
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '-';
    
    try {
      const date = new Date(dateString);
      // Check if date is valid before formatting
      if (isNaN(date.getTime())) {
        return '-';
      }
      
      return formatIndonesiaDate(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  };

  // Format jabatan enum to display text
  const formatJabatan = (jabatan: string): string => {
    return jabatan.replace('_', ' ').replace(/(^\w|\s\w)/g, m => m.toUpperCase());
  };

  const handleDelete = async (id: string) => {
    if (isConfirmingDelete !== id) {
      setIsConfirmingDelete(id);
      return;
    }

    try {
      const response = await fetch(`/api/panitia/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Gagal menghapus data panitia');
      }

      setPanitiaList((current) => current.filter((panitia) => panitia.id !== id));
      setIsConfirmingDelete(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan');
    }
  };

  const handleCancelDelete = () => {
    setIsConfirmingDelete(null);
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">Panitia Kegiatan</h2>
        </div>
        <div className="animate-pulse">
          <div className="h-10 mb-4 bg-gray-200 rounded"></div>
          <div className="h-40 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-white rounded-lg shadow-md">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">Panitia Kegiatan</h2>
        </div>
        <div className="p-4 border-l-4 border-red-500 bg-red-50">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">Panitia Kegiatan</h2>
        {isAdmin && (
          <Link href={`/dashboard/pelatihan/${pelatihanId}/panitia/create`}>
            <Button variant="primary">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Tambah Panitia
            </Button>
          </Link>
        )}
      </div>

      {pelatihan && (
        <div className="p-4 mb-4 rounded-md bg-gray-50">
          <h3 className="mb-2 text-sm font-medium text-gray-700">Periode Penugasan</h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <span className="text-xs text-gray-500">Tanggal Mulai:</span>
              <p className="text-sm font-medium">{formatDate(pelatihan.tgl_mulai)}</p>
            </div>
            <div>
              <span className="text-xs text-gray-500">Tanggal Selesai:</span>
              <p className="text-sm font-medium">{formatDate(pelatihan.tgl_berakhir)}</p>
            </div>
          </div>
        </div>
      )}

      {panitiaList.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Nama Pegawai</th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Jabatan</th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Lokasi</th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">No. Surat</th>
                {isAdmin && (
                  <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase">Aksi</th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {panitiaList.map((panitia) => (
                <tr key={panitia.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{panitia.pegawai.nama}</div>
                    <div className="text-sm text-gray-500">{panitia.pegawai.nip}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <span className="inline-flex px-2 text-xs font-semibold leading-5 text-blue-800 bg-blue-100 rounded-full">
                        {formatJabatan(panitia.jabatan)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">{panitia.pegawai.jabatan}</div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    {panitia.lokasi.kab_kota}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                    {panitia.no_surat || '-'}
                  </td>
                  {isAdmin && (
                    <td className="px-6 py-4 text-sm font-medium text-right whitespace-nowrap">
                      {isConfirmingDelete === panitia.id ? (
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleCancelDelete()}
                            className="text-gray-500 hover:text-gray-700"
                          >
                            Batal
                          </button>
                          <button
                            onClick={() => handleDelete(panitia.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            Konfirmasi
                          </button>
                        </div>
                      ) : (
                        <div className="flex justify-end space-x-2">
                          <Link
                            href={`/dashboard/pelatihan/${pelatihanId}/panitia/edit/${panitia.id}`}
                            className="text-blue-600 hover:text-blue-800"
                          >
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDelete(panitia.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            Hapus
                          </button>
                        </div>
                      )}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="py-8 text-center">
          <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada panitia</h3>
          <p className="mt-1 text-sm text-gray-500">Mulai dengan menambahkan panitia baru.</p>
          {isAdmin && (
            <div className="mt-6">
              <Link href={`/dashboard/pelatihan/${pelatihanId}/panitia/create`}>
                <Button variant="primary">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  Tambah Panitia
                </Button>
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}