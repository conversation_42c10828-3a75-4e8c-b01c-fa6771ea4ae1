#!/bin/bash

# Commands untuk dijalankan di VPS setelah upload emergency scripts
# Copy dan jalankan command ini satu per satu di VPS

echo "=================================================="
echo "  EMERGENCY FIX - COMMANDS UNTUK VPS"
echo "=================================================="
echo ""

echo "🔧 STEP 1: Set permissions untuk scripts"
echo "chmod +x emergency-fix-photo.sh"
echo "chmod +x debug-photo-status.sh" 
echo "chmod +x advanced-diagnosa.sh"
echo ""

echo "🚀 STEP 2: Jalankan emergency fix (AUTO REPAIR)"
echo "sudo ./emergency-fix-photo.sh"
echo ""

echo "📊 STEP 3: Verifikasi hasil"
echo "./debug-photo-status.sh"
echo ""

echo "🧪 STEP 4: Test manual jika diperlukan"
echo "# Cari lokasi aplikasi:"
echo "find /home /var/www /opt -name server.js -path '*/standalone/*' 2>/dev/null"
echo ""
echo "# Test URL akses:"
echo "curl -I https://kegiatan.bpmpkaltim.id/uploads/"
echo ""

echo "📋 STEP 5: Monitor logs jika ada masalah"
echo "tail -f /var/log/nginx/error.log"
echo "tail -f /var/log/nginx/access.log | grep uploads"
echo ""

echo "=================================================="
echo "  COPY COMMANDS DI ATAS KE VPS"
echo "=================================================="

# Untuk kemudahan, siapkan command dalam satu blok
cat << 'EOF'

# JALANKAN BLOCK INI DI VPS:
chmod +x emergency-fix-photo.sh debug-photo-status.sh advanced-diagnosa.sh
sudo ./emergency-fix-photo.sh
echo "=== EMERGENCY FIX SELESAI ==="
./debug-photo-status.sh

EOF
