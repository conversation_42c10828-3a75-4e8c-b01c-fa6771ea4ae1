#!/bin/bash

# IMMEDIATE FIX - Nginx Static Files Configuration
# Problem: Files uploaded to public/uploads/ but nginx can't serve them

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${GREEN}"
echo "============================================"
echo "   IMMEDIATE NGINX STATIC FILES FIX"
echo "============================================"
echo -e "${NC}"

log_info "Problem: Files exist in public/uploads/ but nginx can't serve them"
log_info "Solution: Add nginx location block for /uploads/ path"

# STEP 1: Find aplikasi Next.js
log_info "Mencari lokasi aplikasi Next.js..."

APP_DIRS=(
    "/var/www/html"
    "/home/<USER>/app"
    "/opt/app"
    "/usr/local/app"
)

# Auto-detect berdasarkan file yang ada
DETECTED_DIRS=()
while IFS= read -r -d '' app_path; do
    app_dir=$(dirname "$app_path")
    DETECTED_DIRS+=("$app_dir")
done < <(find /home /var/www /opt /usr/local -name "server.js" -path "*/standalone/*" -print0 2>/dev/null || true)

# Gabungkan detected dengan default
ALL_DIRS=("${DETECTED_DIRS[@]}" "${APP_DIRS[@]}")

# Pilih directory yang ada dan memiliki public folder
APP_DIR=""
for dir in "${ALL_DIRS[@]}"; do
    if [ -d "$dir/public" ]; then
        APP_DIR="$dir"
        log_success "Menggunakan app directory: $APP_DIR"
        break
    elif [ -d "$dir" ]; then
        APP_DIR="$dir"
        log_warning "Directory ditemukan tapi belum ada public folder: $APP_DIR"
        mkdir -p "$APP_DIR/public"
        break
    fi
done

if [ -z "$APP_DIR" ]; then
    log_error "Tidak dapat menemukan aplikasi directory"
    APP_DIR="/var/www/html"
    mkdir -p "$APP_DIR/public"
    log_warning "Menggunakan default: $APP_DIR"
fi

# STEP 2: Pastikan struktur folder uploads ada
log_info "Memastikan struktur folder uploads..."

if [ ! -d "$APP_DIR/public/uploads/absensi/photos" ]; then
    mkdir -p "$APP_DIR/public/uploads/absensi/photos"
    log_success "Folder uploads dibuat"
else
    log_success "Folder uploads sudah ada"
fi

# Set permissions
chown -R www-data:www-data "$APP_DIR/public/uploads" 2>/dev/null || true
chmod -R 755 "$APP_DIR/public/uploads"
log_success "Permissions diatur: www-data:www-data 755"

# Count existing photos
EXISTING_PHOTOS=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l)
log_info "📸 Foto yang sudah ada: $EXISTING_PHOTOS"

# STEP 3: Backup nginx configs
BACKUP_DIR="/tmp/nginx-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"

log_info "Backup nginx configs ke: $BACKUP_DIR"

# Backup main config
cp /etc/nginx/nginx.conf "$BACKUP_DIR/"

# Backup semua config yang ada
find /etc/nginx -name "*.conf" -exec cp {} "$BACKUP_DIR/" \; 2>/dev/null || true

# STEP 4: Cari dan update nginx config yang tepat
log_info "Mencari nginx config untuk kegiatan.bpmpkaltim.id..."

# Cari file config yang mengandung domain
CONFIG_FILES=(
    "/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
    "/etc/nginx/sites-available/default"
    "/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
    "/etc/nginx/conf.d/default.conf"
)

FOUND_CONFIG=""
for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$config" ]; then
        if grep -q "kegiatan.bpmpkaltim.id\|server_name.*bpmpkaltim" "$config" 2>/dev/null; then
            FOUND_CONFIG="$config"
            log_success "Config ditemukan: $config"
            break
        fi
    fi
done

# Jika tidak ditemukan, buat config baru
if [ -z "$FOUND_CONFIG" ]; then
    log_warning "Config untuk domain tidak ditemukan, membuat baru..."
    
    # Tentukan lokasi berdasarkan struktur nginx
    if [ -d "/etc/nginx/sites-available" ]; then
        FOUND_CONFIG="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
        mkdir -p /etc/nginx/sites-enabled
        USE_SITES_ENABLED=true
    else
        FOUND_CONFIG="/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
        mkdir -p /etc/nginx/conf.d
        USE_SITES_ENABLED=false
        
        # Pastikan include conf.d ada di nginx.conf
        if ! grep -q "include.*conf.d" /etc/nginx/nginx.conf; then
            sed -i '/http {/a\    include /etc/nginx/conf.d/*.conf;' /etc/nginx/nginx.conf
            log_info "Menambahkan include conf.d ke nginx.conf"
        fi
    fi
    
    # Buat config baru
    cat > "$FOUND_CONFIG" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name kegiatan.bpmpkaltim.id;

    # Static files untuk uploads
    location /uploads/ {
        root $APP_DIR/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        try_files \$uri \$uri/ =404;
    }

    # Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

    log_success "Config baru dibuat: $FOUND_CONFIG"
    
    # Aktifkan jika menggunakan sites-enabled
    if [ "$USE_SITES_ENABLED" = true ]; then
        ln -sf "$FOUND_CONFIG" /etc/nginx/sites-enabled/
        log_success "Site diaktifkan"
    fi
    
else
    # Update config yang ada
    log_info "Mengupdate config yang ada..."
    
    # Backup config yang akan diubah
    cp "$FOUND_CONFIG" "$BACKUP_DIR/$(basename $FOUND_CONFIG).backup"
    
    # Cek apakah sudah ada location /uploads/
    if grep -q "location /uploads/" "$FOUND_CONFIG"; then
        log_warning "Location /uploads/ sudah ada, mengupdate..."
        
        # Update root path jika berbeda
        current_root=$(grep -A 10 "location /uploads/" "$FOUND_CONFIG" | grep "root" | awk '{print $2}' | sed 's/;//' | head -1)
        expected_root="$APP_DIR/public"
        
        if [ "$current_root" != "$expected_root" ]; then
            sed -i "/location \/uploads\//,/}/ s|root.*|root $expected_root;|" "$FOUND_CONFIG"
            log_success "Root path diupdate: $expected_root"
        else
            log_info "Root path sudah benar: $current_root"
        fi
        
    else
        log_info "Menambahkan location /uploads/ ke config..."
        
        # Tambahkan location block sebelum location /
        sed -i '/location \/ {/i\
    # Static files untuk uploads\
    location /uploads/ {\
        root '"$APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        add_header Access-Control-Allow-Origin "*";\
        try_files $uri $uri/ =404;\
    }\
' "$FOUND_CONFIG"
        
        log_success "Location /uploads/ ditambahkan"
    fi
fi

# STEP 5: Test dan reload nginx
log_info "Testing nginx configuration..."

if nginx -t; then
    log_success "✅ Nginx config valid"
    
    systemctl reload nginx
    log_success "✅ Nginx direload"
    
else
    log_error "❌ Nginx config error!"
    log_warning "Restoring backup..."
    
    # Restore backup
    cp "$BACKUP_DIR/nginx.conf" /etc/nginx/nginx.conf
    if [ -f "$BACKUP_DIR/$(basename $FOUND_CONFIG).backup" ]; then
        cp "$BACKUP_DIR/$(basename $FOUND_CONFIG).backup" "$FOUND_CONFIG"
    fi
    
    systemctl reload nginx || true
    log_error "Fix gagal, backup direstore"
    exit 1
fi

# STEP 6: Test akses
log_info "Testing akses static files..."

# Buat test file
TEST_DIR="$APP_DIR/public/uploads/test"
mkdir -p "$TEST_DIR"
echo "Test access file $(date)" > "$TEST_DIR/test.txt"
chown www-data:www-data "$TEST_DIR/test.txt" 2>/dev/null || true

# Test lokal
LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost/uploads/test/test.txt" --max-time 5 2>/dev/null || echo "000")

if [ "$LOCAL_STATUS" = "200" ]; then
    log_success "✅ Test lokal berhasil! Status: $LOCAL_STATUS"
else
    log_warning "⚠️ Test lokal gagal - Status: $LOCAL_STATUS"
fi

# Test external
EXTERNAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://kegiatan.bpmpkaltim.id/uploads/test/test.txt" --max-time 10 2>/dev/null || echo "000")

if [ "$EXTERNAL_STATUS" = "200" ]; then
    log_success "✅ Test external berhasil! Status: $EXTERNAL_STATUS"
elif [ "$EXTERNAL_STATUS" = "404" ]; then
    log_warning "⚠️ External 404 - mungkin DNS cache atau CDN issue"
else
    log_warning "⚠️ Test external - Status: $EXTERNAL_STATUS"
fi

# Test specific photo path (contoh dari URL yang Anda berikan)
SAMPLE_URL="https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/915b8bfd-14ad-48d0-805f-6509f9540dd3/"
SAMPLE_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$SAMPLE_URL" --max-time 10 2>/dev/null || echo "000")

log_info "Test sample photo path: $SAMPLE_STATUS"

# Cleanup test file
rm -f "$TEST_DIR/test.txt"
rmdir "$TEST_DIR" 2>/dev/null || true

# STEP 7: Summary
echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   NGINX STATIC FILES FIX SELESAI"
echo "============================================"
echo -e "${NC}"

log_success "✅ App directory: $APP_DIR"
log_success "✅ Uploads folder: $APP_DIR/public/uploads/absensi/photos/"
log_success "✅ Nginx config: $FOUND_CONFIG"
log_success "✅ Backup: $BACKUP_DIR"
log_success "✅ Foto existing: $EXISTING_PHOTOS"

echo ""
log_info "🧪 HASIL TEST:"
echo "• Test lokal: $LOCAL_STATUS"
echo "• Test external: $EXTERNAL_STATUS"
echo "• Sample path: $SAMPLE_STATUS"

echo ""
log_info "🔍 VERIFIKASI SEGERA:"
echo "1. Buka admin panel dan upload foto absensi baru"
echo "2. Cek detail absensi - foto harus muncul tanpa 404"
echo "3. Test URL langsung: https://kegiatan.bpmpkaltim.id/uploads/"

echo ""
log_info "📋 TROUBLESHOOTING jika masih 404:"
echo "• tail -f /var/log/nginx/access.log | grep uploads"
echo "• tail -f /var/log/nginx/error.log"
echo "• curl -I https://kegiatan.bpmpkaltim.id/uploads/"

echo ""
log_warning "⚠️ Restore backup jika ada masalah:"
echo "sudo cp $BACKUP_DIR/nginx.conf /etc/nginx/nginx.conf"
echo "sudo systemctl reload nginx"

log_success "🎉 Fix completed! Upload foto baru sekarang harus bisa diakses langsung."
