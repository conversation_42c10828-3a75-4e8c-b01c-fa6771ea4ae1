@echo off
setlocal enabledelayedexpansion

:: Advanced Troubleshooter Windows Wrapper
:: Upload dan execute troubleshooter untuk auto-refresh fix

title Advanced Auto-Refresh Troubleshooter - Remote Execution

echo.
echo ================================================
echo    ADVANCED AUTO-REFRESH TROUBLESHOOTER
echo ================================================
echo.

:: Configuration
set VPS_IP=*************
set VPS_USER=root
set LOCAL_SCRIPT=%~dp0troubleshoot-autorefresh.sh
set REMOTE_SCRIPT=/tmp/troubleshoot-autorefresh.sh

echo [36m[INFO][0m Target VPS: %VPS_IP%
echo [36m[INFO][0m Troubleshooter: %LOCAL_SCRIPT%
echo.

:: Check if local script exists
if not exist "%LOCAL_SCRIPT%" (
    echo [31m[ERROR][0m Troubleshooter script tidak ditemukan: %LOCAL_SCRIPT%
    echo.
    echo Pastikan file troubleshoot-autorefresh.sh ada di direktori yang sama.
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Troubleshooter script found

:: Step 1: Upload troubleshooter
echo.
echo [35m[ACTION][0m Uploading troubleshooter to VPS...

scp "%LOCAL_SCRIPT%" %VPS_USER%@%VPS_IP%:%REMOTE_SCRIPT%

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to upload troubleshooter
    echo.
    echo [33mTroubleshooting:[0m
    echo 1. Check internet connection
    echo 2. Verify SSH access to %VPS_IP%
    echo 3. Try manual: scp troubleshoot-autorefresh.sh %VPS_USER%@%VPS_IP%:/tmp/
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Troubleshooter uploaded successfully

:: Step 2: Execute troubleshooter
echo.
echo [35m[ACTION][0m Running troubleshooter on VPS...
echo [33m[INFO][0m This may take a few minutes...
echo.

ssh %VPS_USER%@%VPS_IP% "chmod +x %REMOTE_SCRIPT% && %REMOTE_SCRIPT%"

set TROUBLESHOOT_RESULT=%errorlevel%

if %TROUBLESHOOT_RESULT% neq 0 (
    echo.
    echo [31m[ERROR][0m Troubleshooter execution failed
    goto :show_manual_commands
) else (
    echo.
    echo [32m[SUCCESS][0m Troubleshooter completed successfully!
)

:: Step 3: Show results and recommendations
echo.
echo [36m================================================[0m
echo [32m   TROUBLESHOOTING COMPLETED[0m
echo [36m================================================[0m
echo.

if %TROUBLESHOOT_RESULT% equ 0 (
    echo [32m✅ Troubleshooter ran successfully![0m
    echo.
    echo [33mNext Steps:[0m
    echo 1. Test photo upload di aplikasi absensi
    echo 2. Check detail absensi - foto harus muncul TANPA reload
    echo 3. Monitor real-time dengan: monitor-autorefresh.bat continuous
    echo.
    
    :: Offer to run monitoring
    echo [36m[INFO][0m Run real-time monitoring sekarang? (y/n)
    set /p monitor_choice="Choice: "
    
    if /i "!monitor_choice!"=="y" (
        echo.
        echo [35m[ACTION][0m Starting real-time monitoring...
        call "%~dp0monitor-autorefresh.bat" continuous
    )
) else (
    echo [33m⚠️ Troubleshooter menemukan issues[0m
    echo.
    echo Check output di atas untuk detail masalah dan perbaikan.
)

:show_manual_commands
echo.
echo [36m================================================[0m
echo [32m   MANUAL COMMANDS REFERENCE[0m
echo [36m================================================[0m
echo.
echo [33mSSH Connect:[0m
echo   ssh %VPS_USER%@%VPS_IP%
echo.
echo [33mService Commands:[0m
echo   systemctl status nginx
echo   systemctl status advanced-photo-monitor
echo   systemctl restart advanced-photo-monitor
echo   systemctl reload nginx
echo.
echo [33mLog Monitoring:[0m
echo   journalctl -u advanced-photo-monitor -f
echo   tail -f /var/log/advanced-photo-monitor.log
echo   tail -f /var/log/nginx/photo_access.log
echo.
echo [33mQuick Tests:[0m
echo   curl -I http://localhost/uploads/test.jpg
echo   ls -la /home/<USER>/htdocs/public/uploads/
echo.
echo [33mRe-run Advanced Fix:[0m
echo   /tmp/advanced-cloudpanel-autorefresh-fix.sh
echo.
echo [33mRe-run Troubleshooter:[0m
echo   %REMOTE_SCRIPT%
echo.

:: Option to connect to SSH
echo [36m[INFO][0m Connect ke SSH untuk manual troubleshooting? (y/n)
set /p ssh_choice="Choice: "

if /i "%ssh_choice%"=="y" (
    echo.
    echo [35m[ACTION][0m Opening SSH connection...
    ssh %VPS_USER%@%VPS_IP%
)

echo.
echo [32m[SUCCESS][0m Troubleshooting session completed
echo [36m[INFO][0m Test photo upload di aplikasi untuk verify fix
pause
