'use client';

import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix untuk icon Leaflet di Next.js
const icon = L.icon({
  iconUrl: '/marker-icon.png',
  shadowUrl: '/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Komponen untuk mendapatkan lokasi saat ini
const LocationMarker = ({ 
  position, 
  setPosition,
  setAddressInfo
}: { 
  position: L.LatLng | null; 
  setPosition: (pos: L.LatLng) => void;
  setAddressInfo: (info: string) => void;
}) => {
  const map = useMapEvents({
    click(e) {
      setPosition(e.latlng);
      // Reverse geocoding untuk mendapatkan alamat dari koordinat
      fetchAddress(e.latlng.lat, e.latlng.lng).then(address => {
        setAddressInfo(address);
      });
    },
  });

  useEffect(() => {
    if (position) {
      map.flyTo(position, map.getZoom());
    }
  }, [position, map]);

  return position === null ? null : (
    <Marker position={position} icon={icon} />
  );
};

// Fungsi untuk mendapatkan alamat dari koordinat menggunakan OpenStreetMap Nominatim
async function fetchAddress(lat: number, lng: number): Promise<string> {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
      { headers: { 'Accept-Language': 'id' } }
    );
    const data = await response.json();
    return data.display_name || 'Lokasi tidak dikenal';
  } catch (error) {
    console.error('Error fetching address:', error);
    return 'Tidak dapat mengambil alamat';
  }
}

interface MapPickerProps {
  onLocationSelect: (lat: number, lng: number, address: string) => void;
  initialLat?: number;
  initialLng?: number;
}

export default function MapPicker({ 
  onLocationSelect, 
  initialLat = -0.471852, // Default ke Samarinda, Kalimantan Timur
  initialLng = 117.157982
}: MapPickerProps) {
  const [position, setPosition] = useState<L.LatLng | null>(
    initialLat && initialLng ? L.latLng(initialLat, initialLng) : null
  );
  const [currentLocation, setCurrentLocation] = useState<L.LatLng | null>(null);
  const [addressInfo, setAddressInfo] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const previousPositionRef = useRef<string | null>(null);
  // Mendapatkan lokasi user saat komponen dimuat
  useEffect(() => {
    if ('geolocation' in navigator) {
      setIsLoading(true);
      navigator.geolocation.getCurrentPosition(
        (pos) => {
          const userLocation = L.latLng(pos.coords.latitude, pos.coords.longitude);
          setCurrentLocation(userLocation);
          setPosition(userLocation);
          
          // Mendapatkan alamat dari koordinat
          fetchAddress(userLocation.lat, userLocation.lng).then(address => {
            setAddressInfo(address);
          });
          
          setIsLoading(false);
        },        (error) => {
          let errorMessage = 'Tidak dapat mengakses lokasi';
          
          switch(error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Akses lokasi ditolak oleh pengguna';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Informasi lokasi tidak tersedia';
              break;
            case error.TIMEOUT:
              errorMessage = 'Waktu tunggu lokasi habis';
              break;
            default:
              errorMessage = 'Terjadi kesalahan saat mendapatkan lokasi';
              break;
          }
          
          console.warn('Geolocation error:', errorMessage, error);
          setLocationError(errorMessage);
          
          // Fallback ke lokasi default jika ada initialLat/initialLng
          if (initialLat && initialLng) {
            const defaultLocation = L.latLng(initialLat, initialLng);
            setPosition(defaultLocation);
            fetchAddress(defaultLocation.lat, defaultLocation.lng).then(address => {
              setAddressInfo(address);
            });
          }
          
          setIsLoading(false);
        },        { 
          enableHighAccuracy: true,
          timeout: 15000, // 15 seconds timeout
          maximumAge: 300000 // 5 minutes cache
        }
      );
    } else {
      setLocationError('Geolocation tidak didukung oleh browser ini');
      // Fallback ke lokasi default
      if (initialLat && initialLng) {
        const defaultLocation = L.latLng(initialLat, initialLng);
        setPosition(defaultLocation);
        fetchAddress(defaultLocation.lat, defaultLocation.lng).then(address => {
          setAddressInfo(address);
        });
      }
    }
  }, [initialLat, initialLng]); // Empty dependency array - run once on mount
  
  // Mengirim data lokasi ke parent component saat position berubah
  useEffect(() => {
    if (position && addressInfo) {
      // Using a ref to track if we need to call onLocationSelect to avoid infinite loops
      const positionString = `${position.lat},${position.lng}`;
      if (previousPositionRef.current !== positionString) {
        previousPositionRef.current = positionString;
        onLocationSelect(position.lat, position.lng, addressInfo);
      }
    }
  }, [position, addressInfo, onLocationSelect]);
  return (
    <div className="flex flex-col w-full">
      {locationError && (
        <div className="p-3 mb-2 text-sm text-yellow-800 bg-yellow-100 border border-yellow-200 rounded-md">
          <p className="font-semibold">Peringatan Lokasi:</p>
          <p>{locationError}</p>
          <p className="mt-1 text-xs">Anda masih dapat memilih lokasi dengan mengklik pada peta di bawah.</p>
        </div>
      )}
      
      <div className="h-[300px] rounded-md overflow-hidden relative">
        {isLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-75">
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
              <p className="mt-2 text-sm text-gray-600">Mendapatkan lokasi...</p>
            </div>
          </div>
        )}
        
        <MapContainer 
          center={position || [initialLat, initialLng]} 
          zoom={13} 
          style={{ height: '100%', width: '100%' }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          <LocationMarker 
            position={position} 
            setPosition={setPosition}
            setAddressInfo={setAddressInfo}
          />
        </MapContainer>
      </div>
      
      {position && (
        <div className="p-3 mt-2 text-xs rounded-md bg-gray-50">
          <p className="font-semibold text-gray-700">Lokasi terpilih:</p>
          <p className="mt-1 text-gray-600">{addressInfo}</p>
          <p className="mt-1 text-gray-500">
            Koordinat: {position.lat.toFixed(6)}, {position.lng.toFixed(6)}
          </p>          <button
            type="button"
            onClick={() => {
              if (currentLocation) {
                setPosition(currentLocation);
                fetchAddress(currentLocation.lat, currentLocation.lng).then(address => {
                  setAddressInfo(address);
                  // Don't call onLocationSelect here - the useEffect will handle it
                });
              }
            }}
            className="px-2 py-1 mt-2 text-xs text-white bg-blue-500 rounded hover:bg-blue-600"
            disabled={!currentLocation}
          >
            Kembali ke Lokasi Saat Ini
          </button>
        </div>
      )}
    </div>
  );
}
