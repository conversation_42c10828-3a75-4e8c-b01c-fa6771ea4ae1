import { cookies } from 'next/headers';
import { cache } from 'react';
import { decrypt, createSession, destroySession } from './session';
import { prisma } from './prisma';
import bcrypt from 'bcryptjs';

export const dynamic = 'force-dynamic';

export const getCurrentUser = cache(async () => {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get('session')?.value;
    
    if (!sessionCookie) {
      return null;
    }
    
    const session = await decrypt(sessionCookie);
    
    if (!session?.userId) {
      return null;
    }
    
    const user = await prisma.user.findUnique({
      where: { id: session.userId as string },
      select: {
        id: true,
        name: true,
        email: true,
        role: true
      }
    });
    
    return user;
  } catch (error) {
    console.error('Get current user error:', error);
    return null;
  }
});

export async function loginUser(email: string, password: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return { success: false, message: 'Email atau password salah' };
    }

    const passwordValid = await bcrypt.compare(password, user.password);
    if (!passwordValid) {
      return { success: false, message: 'Email atau password salah' };
    }

    await createSession(user.id, user.role);

    return {
      success: true,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'Terjadi kesalahan saat login' };
  }
}

export async function logoutUser() {
  try {
    await destroySession();
    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false, message: 'Terjadi kesalahan saat logout' };
  }
}
