'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Button from './Button';
import InputField from './InputField';
import { formatIndonesiaDate } from '@/utils/dateUtils';

interface PanitiaFormProps {
  pelatihanId: string;
  panitiaId?: string;
  isEdit?: boolean;
}

interface Pegawai {
  id: string;
  nama: string;
  nip: string;
  p_gol: string;
  jabatan: string;
}

interface Lokasi {
  id: string;
  kab_kota: string;
}

interface Pelatihan {
  id: string;
  nama: string;
  tgl_mulai: string;
  tgl_berakhir: string;
}

interface _Panitia {
  id: string;
  pelatihanId: string;
  pegawaiId: string;
  jabatan: string;
  lokasiId: string;
  no_surat: string | null;
  keterangan: string | null;
}

// Enum for committee positions
enum PanitiaJabatan {
  PENGARAH = 'PENGARAH',
  PENANGGUNG_JAWAB = 'PENANGGUNG_JAWAB',
  KETUA = 'KETUA',
  ANGGOTA = 'ANGGOTA',
  MODERATOR = 'MODERATOR',
  HOST = 'HOST'
}

export default function PanitiaForm({ pelatihanId, panitiaId = '', isEdit = false }: PanitiaFormProps) {
  const router = useRouter();
  const [allPegawaiList, setAllPegawaiList] = useState<Pegawai[]>([]);
  const [pegawaiList, setPegawaiList] = useState<Pegawai[]>([]);
  const [lokasiList, setLokasiList] = useState<Lokasi[]>([]);
  const [pelatihan, setPelatihan] = useState<Pelatihan | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [busyPegawaiIds, setBusyPegawaiIds] = useState<string[]>([]);
  const [currentPegawaiId, setCurrentPegawaiId] = useState<string>('');

  const [formData, setFormData] = useState<{
    pegawaiId: string;
    jabatan: PanitiaJabatan;
    lokasiId: string;
    no_surat: string;
    keterangan: string;
  }>({
    pegawaiId: '',
    jabatan: PanitiaJabatan.ANGGOTA,
    lokasiId: '',
    no_surat: '',
    keterangan: '',
  });

  // Format display name for jabatan enum
  const formatJabatan = (jabatan: string): string => {
    return jabatan.replace('_', ' ').replace(/(^\w|\s\w)/g, m => m.toUpperCase());
  };

  // Format date safely to prevent "Invalid Date" display
  const formatDateSafely = (dateString: string | null | undefined) => {
    return formatIndonesiaDate(dateString);
  };

  // Fungsi untuk mengecek pegawai yang sibuk pada tanggal pelatihan
  const fetchBusyPegawai = async (tglMulai: string, tglBerakhir: string, currentPanitiaId?: string) => {
    try {
      // Ambil semua panitia yang memiliki tugas pada rentang tanggal yang sama
      const searchParams = new URLSearchParams({
        tgl_mulai: tglMulai,
        tgl_berakhir: tglBerakhir
      });
      
      // Jika mode edit, kecualikan panitia yang sedang diedit
      if (currentPanitiaId) {
        searchParams.append('exclude', currentPanitiaId);
      }
      
      const busyPegawaiResponse = await fetch(`/api/panitia/check-availability?${searchParams.toString()}`);
      
      if (!busyPegawaiResponse.ok) {
        console.error('Gagal mengecek ketersediaan panitia');
        return [];
      }
      
      const busyData = await busyPegawaiResponse.json();
      return busyData.busyPegawaiIds || [];
    } catch (err) {
      console.error('Error checking busy pegawai:', err);
      return [];
    }
  };

  // Wrap filterAvailablePegawai in useCallback to memoize it
  const filterAvailablePegawai = useCallback(() => {
    if (!allPegawaiList.length || !pelatihan) return;

    const availablePegawai = allPegawaiList.filter(pegawai => {
      // Jika sedang edit dan ini adalah pegawai yang sama dengan sebelumnya, tetap tampilkan
      if (isEdit && pegawai.id === currentPegawaiId) {
        return true;
      }
      // Jika pegawai ada di daftar yang sibuk, jangan tampilkan
      return !busyPegawaiIds.includes(pegawai.id);
    });

    setPegawaiList(availablePegawai);
  }, [allPegawaiList, pelatihan, isEdit, currentPegawaiId, busyPegawaiIds]);

  // Fetch data pegawai, lokasi, and pelatihan
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch pegawai list
        const pegawaiResponse = await fetch('/api/pegawai');
        if (!pegawaiResponse.ok) {
          throw new Error('Gagal mengambil data pegawai');
        }
        const pegawaiData = await pegawaiResponse.json();
        setAllPegawaiList(pegawaiData);

        // Fetch lokasi list with dropdown parameter to bypass admin restriction
        const lokasiResponse = await fetch('/api/tugas-lokasi?dropdown=true');
        if (!lokasiResponse.ok) {
          throw new Error('Gagal mengambil data lokasi');
        }
        const lokasiData = await lokasiResponse.json();
        
        // Handle the response format
        setLokasiList(lokasiData);

        // Fetch pelatihan data to get dates
        const pelatihanResponse = await fetch(`/api/pelatihan/${pelatihanId}`);
        if (!pelatihanResponse.ok) {
          throw new Error('Gagal mengambil data pelatihan');
        }
        const pelatihanData = await pelatihanResponse.json();
        
        // Make sure we're accessing the correct data structure from the API
        let pelatihanResult;
        if (pelatihanData.data && pelatihanData.success) {
          // If API returns data in a nested structure with success flag
          pelatihanResult = pelatihanData.data;
        } else {
          // Direct object
          pelatihanResult = pelatihanData;
        }
        
        setPelatihan(pelatihanResult);

        // Jika data pelatihan tersedia, cek pegawai yang sibuk pada tanggal tersebut
        if (pelatihanResult && pelatihanResult.tgl_mulai && pelatihanResult.tgl_berakhir) {
          const busyIds = await fetchBusyPegawai(
            pelatihanResult.tgl_mulai, 
            pelatihanResult.tgl_berakhir,
            isEdit ? panitiaId : undefined
          );
          setBusyPegawaiIds(busyIds);
        }

        // If edit mode, fetch panitia data
        if (isEdit && panitiaId) {
          const panitiaResponse = await fetch(`/api/panitia/${panitiaId}`);
          if (!panitiaResponse.ok) {
            throw new Error('Gagal mengambil data panitia');
          }
          const panitiaData = await panitiaResponse.json();
          
          setFormData({
            pegawaiId: panitiaData.pegawaiId,
            jabatan: panitiaData.jabatan as PanitiaJabatan,
            lokasiId: panitiaData.lokasiId,
            no_surat: panitiaData.no_surat || '',
            keterangan: panitiaData.keterangan || '',
          });
          
          // Catat pegawai ID saat ini untuk mode edit
          setCurrentPegawaiId(panitiaData.pegawaiId);
        }
      } catch (err) {
        console.error('Error in fetchData:', err);
        setError(err instanceof Error ? err.message : 'Terjadi kesalahan');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [isEdit, panitiaId, pelatihanId]);

  // Filter pegawai yang tersedia setiap kali data pelatihan atau daftar pegawai yang sibuk berubah
  useEffect(() => {
    filterAvailablePegawai();
  }, [filterAvailablePegawai]); // Now includes filterAvailablePegawai in dependencies

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (!pelatihan) {
        throw new Error('Data pelatihan tidak ditemukan');
      }

      const apiUrl = isEdit 
        ? `/api/panitia/${panitiaId}` 
        : `/api/panitia`;
      
      const method = isEdit ? 'PUT' : 'POST';
      
      const payload = {
        ...formData,
        pelatihanId,
      };

      const response = await fetch(apiUrl, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menyimpan data panitia');
      }

      setSuccess(isEdit ? 'Panitia berhasil diperbarui' : 'Panitia berhasil ditambahkan');
      
      if (!isEdit) {
        setFormData({
          pegawaiId: '',
          jabatan: PanitiaJabatan.ANGGOTA,
          lokasiId: '',
          no_surat: '',
          keterangan: '',
        });
      }

      router.refresh();
      
      // Redirect after 2 seconds
      setTimeout(() => {
        router.push(`/dashboard/pelatihan/${pelatihanId}`);
      }, 2000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/dashboard/pelatihan/${pelatihanId}`);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="mb-6 text-xl font-semibold">
        {isEdit ? 'Edit Panitia Kegiatan' : 'Tambah Panitia Kegiatan'}
      </h2>

      {error && (
        <div className="p-4 mb-4 border-l-4 border-red-500 bg-red-50">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="p-4 mb-4 border-l-4 border-green-500 bg-green-50">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-4">
          <label htmlFor="pegawaiId" className="block mb-1 text-sm font-medium text-gray-700">
            Pegawai *
          </label>
          <select
            id="pegawaiId"
            name="pegawaiId"
            value={formData.pegawaiId}
            onChange={handleChange}
            className="w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          >
            <option value="">-- Pilih Pegawai --</option>
            {pegawaiList.map(pegawai => (
              <option key={pegawai.id} value={pegawai.id}>
                {pegawai.nama} ({pegawai.nip}) - {pegawai.jabatan}
              </option>
            ))}
          </select>
        </div>

        <div className="mb-4">
          <label htmlFor="jabatan" className="block mb-1 text-sm font-medium text-gray-700">
            Jabatan dalam Kepanitiaan *
          </label>
          <select
            id="jabatan"
            name="jabatan"
            value={formData.jabatan}
            onChange={handleChange}
            className="w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          >
            {Object.values(PanitiaJabatan).map(jabatan => (
              <option key={jabatan} value={jabatan}>
                {formatJabatan(jabatan)}
              </option>
            ))}
          </select>
        </div>

        <div className="mb-4">
          <label htmlFor="lokasiId" className="block mb-1 text-sm font-medium text-gray-700">
            Lokasi Tugas *
          </label>
          <select
            id="lokasiId"
            name="lokasiId"
            value={formData.lokasiId}
            onChange={handleChange}
            className="w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            required
          >
            <option value="">-- Pilih Lokasi Tugas --</option>
            {lokasiList.map(lokasi => (
              <option key={lokasi.id} value={lokasi.id}>
                {lokasi.kab_kota}
              </option>
            ))}
          </select>
        </div>

        {pelatihan && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium text-gray-700">
                Tanggal Mulai (otomatis dari kegiatan)
              </label>
              <input
                type="text"
                value={pelatihan.tgl_mulai ? formatDateSafely(pelatihan.tgl_mulai) : '-'}
                className="w-full px-3 py-2 text-gray-600 border border-gray-200 rounded-md bg-gray-50"
                disabled
              />
            </div>

            <div className="mb-4">
              <label className="block mb-1 text-sm font-medium text-gray-700">
                Tanggal Selesai (otomatis dari pelatihan)
              </label>
              <input
                type="text"
                value={pelatihan.tgl_berakhir ? formatDateSafely(pelatihan.tgl_berakhir) : '-'}
                className="w-full px-3 py-2 text-gray-600 border border-gray-200 rounded-md bg-gray-50"
                disabled
              />
            </div>
          </div>
        )}

        <div className="mb-4">
          <InputField
            label="Nomor Surat"
            id="no_surat"
            name="no_surat"
            type="text"
            value={formData.no_surat}
            onChange={handleChange}
            placeholder="Nomor surat tugas (opsional)"
          />
        </div>

        <div className="mb-4">
          <label htmlFor="keterangan" className="block mb-1 text-sm font-medium text-gray-700">
            Keterangan
          </label>
          <textarea
            id="keterangan"
            name="keterangan"
            value={formData.keterangan}
            onChange={handleChange}
            rows={3}
            className="w-full px-3 py-2 placeholder-gray-400 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            placeholder="Keterangan tambahan (opsional)"
          />
        </div>

        <div className="flex justify-end pt-4 space-x-3">
          <Button
            type="button"
            variant="secondary"
            onClick={handleCancel}
            disabled={isLoading}
          >
            Batal
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isLoading}
          >
            {isLoading ? 'Menyimpan...' : isEdit ? 'Perbarui' : 'Simpan'}
          </Button>
        </div>
      </form>
    </div>
  );
}
