'use client';

import React, { ReactNode, useEffect } from 'react';
import Layout from '../components/Layouts'; 
import { setupGlobalErrorLogging } from '../utils/logger';
import { setupConsoleInterceptor } from '../utils/consoleInterceptor';

// Definisikan tipe untuk props
interface ClientLayoutProps {
  children: ReactNode;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

// This is a client component wrapper
export default function ClientLayout({ children, user }: ClientLayoutProps) {
  // Setup global error logging for the entire app
  useEffect(() => {
    setupGlobalErrorLogging();
    setupConsoleInterceptor();
  }, []);

  return <Layout user={user}>{children}</Layout>;
}