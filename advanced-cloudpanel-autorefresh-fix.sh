#!/bin/bash

# CloudPanel Ubuntu 24.04 - Advanced Auto Refresh Fix untuk Photo Upload
# Solusi komprehensif untuk masalah nginx memerlukan reload manual

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
}

echo -e "${GREEN}"
echo "================================================="
echo "   ADVANCED AUTO-REFRESH FIX - UBUNTU 24.04"
echo "   Solusi Komprehensif Nginx + CloudPanel"
echo "================================================="
echo -e "${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    log_error "Script harus dijalankan sebagai root (gunakan sudo)"
    exit 1
fi

# STEP 1: Advanced Application Detection
log_info "Melakukan deteksi aplikasi advanced..."

# Deteksi CloudPanel sites
CLOUDPANEL_DIRS=()
while IFS= read -r -d '' dir; do
    if [ -d "$dir" ] && [ -n "$(ls -A "$dir" 2>/dev/null || true)" ]; then
        CLOUDPANEL_DIRS+=("$dir")
        log_success "CloudPanel site: $dir"
    fi
done < <(find /home -maxdepth 2 -name "htdocs" -type d -print0 2>/dev/null || true)

# Deteksi Next.js applications
NEXTJS_APPS=()
for search_root in "${CLOUDPANEL_DIRS[@]}" "/var/www" "/opt" "/usr/local"; do
    if [ ! -d "$search_root" ]; then continue; fi
    
    while IFS= read -r -d '' app_path; do
        app_dir=$(dirname "$app_path")
        
        # Validasi apakah benar Next.js app
        if [ -f "$app_dir/package.json" ] && (grep -q "next" "$app_dir/package.json" 2>/dev/null || [ -f "$app_dir/server.js" ]); then
            if [[ ! " ${NEXTJS_APPS[@]} " =~ " ${app_dir} " ]]; then
                NEXTJS_APPS+=("$app_dir")
                log_success "Next.js app: $app_dir"
            fi
        fi
    done < <(find "$search_root" -maxdepth 4 \( -name "server.js" -o -name "package.json" \) -print0 2>/dev/null || true)
done

# Tentukan aplikasi utama
APP_DIR=""
DOMAIN_USER=""
DOMAIN_GROUP=""

for dir in "${NEXTJS_APPS[@]}"; do
    if [ -d "$dir" ] && ([ -d "$dir/public" ] || [ -f "$dir/package.json" ]); then
        APP_DIR="$dir"
        
        # Deteksi CloudPanel domain user
        if [[ "$dir" =~ /home/<USER>/]+)/htdocs ]]; then
            DOMAIN_USER="${BASH_REMATCH[1]}"
            DOMAIN_GROUP="$DOMAIN_USER"
            log_success "CloudPanel app detected: $APP_DIR"
            log_info "Domain user: $DOMAIN_USER"
        else
            DOMAIN_USER="www-data"
            DOMAIN_GROUP="www-data"
            log_success "Standard app detected: $APP_DIR"
        fi
        break
    fi
done

if [ -z "$APP_DIR" ]; then
    log_error "Tidak dapat menemukan aplikasi Next.js yang valid"
    log_warning "Pastikan script dijalankan di server yang tepat"
    exit 1
fi

# STEP 2: Comprehensive Backup
BACKUP_DIR="/tmp/advanced-autorefresh-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
log_info "Backup directory: $BACKUP_DIR"

# Backup all relevant configs
for config_dir in "/etc/nginx/sites-available" "/etc/nginx/conf.d" "/etc/nginx"; do
    if [ -d "$config_dir" ]; then
        cp -r "$config_dir" "$BACKUP_DIR/" 2>/dev/null || true
    fi
done

# Backup system configs
cp /etc/sysctl.conf "$BACKUP_DIR/sysctl.conf.backup" 2>/dev/null || true

log_success "Backup selesai"

# STEP 3: Advanced Nginx Optimization
log_action "Mengaplikasikan optimasi nginx advanced..."

# Optimasi nginx.conf untuk real-time file serving
NGINX_MAIN_CONFIG="/etc/nginx/nginx.conf"

# Backup original
cp "$NGINX_MAIN_CONFIG" "$BACKUP_DIR/nginx.conf.original"

# Tambahkan optimasi jika belum ada
if ! grep -q "# ADVANCED AUTO-REFRESH OPTIMIZATION" "$NGINX_MAIN_CONFIG"; then
    sed -i '/worker_processes/a\
\
# ADVANCED AUTO-REFRESH OPTIMIZATION\
worker_rlimit_nofile 65535;\
worker_connections 4096;' "$NGINX_MAIN_CONFIG"

    sed -i '/sendfile on;/a\
\
# File serving optimization untuk real-time access\
sendfile_max_chunk 512k;\
tcp_nopush on;\
tcp_nodelay on;\
keepalive_timeout 30;\
keepalive_requests 100;\
\
# Disable server tokens\
server_tokens off;' "$NGINX_MAIN_CONFIG"

    log_success "Nginx main config optimized"
fi

# STEP 4: Advanced Site Configuration
log_action "Mengkonfigurasi site untuk real-time photo access..."

# Deteksi konfigurasi site
SITE_CONFIGS=()
DOMAIN="kegiatan.bpmpkaltim.id"

# Cari konfigurasi yang ada
for config_pattern in \
    "/etc/nginx/sites-available/$DOMAIN" \
    "/etc/nginx/sites-available/$DOMAIN.conf" \
    "/etc/nginx/sites-available/kegiatan-bpmpkaltim-id" \
    "/etc/nginx/sites-available/kegiatan-bpmpkaltim-id.conf" \
    "/etc/nginx/conf.d/$DOMAIN.conf" \
    "/etc/nginx/conf.d/kegiatan-bpmpkaltim-id.conf"; do
    
    if [ -f "$config_pattern" ]; then
        SITE_CONFIGS+=("$config_pattern")
    fi
done

# Cari berdasarkan content
if [ ${#SITE_CONFIGS[@]} -eq 0 ]; then
    while IFS= read -r -d '' config_file; do
        if grep -l "$DOMAIN\|kegiatan" "$config_file" >/dev/null 2>&1; then
            SITE_CONFIGS+=("$config_file")
        fi
    done < <(find /etc/nginx -name "*.conf" -print0 2>/dev/null || true)
fi

# Buat konfigurasi jika tidak ada
if [ ${#SITE_CONFIGS[@]} -eq 0 ]; then
    if [ -d "/etc/nginx/sites-available" ]; then
        MAIN_CONFIG="/etc/nginx/sites-available/$DOMAIN"
    else
        mkdir -p /etc/nginx/conf.d
        MAIN_CONFIG="/etc/nginx/conf.d/$DOMAIN.conf"
    fi
    SITE_CONFIGS+=("$MAIN_CONFIG")
    log_warning "Membuat konfigurasi baru: $MAIN_CONFIG"
else
    MAIN_CONFIG="${SITE_CONFIGS[0]}"
    log_info "Menggunakan konfigurasi: $MAIN_CONFIG"
fi

# Backup existing config
if [ -f "$MAIN_CONFIG" ]; then
    cp "$MAIN_CONFIG" "$BACKUP_DIR/$(basename "$MAIN_CONFIG").backup"
fi

# Buat konfigurasi advanced
cat > "$MAIN_CONFIG" << EOF
# Advanced CloudPanel Configuration untuk Real-time Photo Upload
# Generated by: Advanced Auto-Refresh Fix Script

upstream nextjs_backend {
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

# Rate limiting untuk uploads
limit_req_zone \$binary_remote_addr zone=upload_limit:10m rate=10r/m;

server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN *.bpmpkaltim.id;

    # Logging khusus untuk monitoring
    access_log /var/log/nginx/kegiatan_access.log combined;
    error_log /var/log/nginx/kegiatan_error.log warn;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Root directory
    root $APP_DIR/public;
    index index.html index.htm;

    # Advanced uploads configuration untuk real-time access
    location /uploads/ {
        alias $APP_DIR/public/uploads/;
        
        # Real-time file serving - NO CACHING
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate, proxy-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Last-Modified "" always;
        add_header ETag "" always;
        
        # CORS untuk cross-origin requests
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
        add_header Access-Control-Max-Age "86400" always;
        
        # Handle preflight requests
        if (\$request_method = 'OPTIONS') {
            return 204;
        }
        
        # Advanced file serving
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        
        # Disable any buffering
        proxy_buffering off;
        proxy_request_buffering off;
        
        # File existence check dengan immediate response
        try_files \$uri \$uri/ =404;
        
        # Specific handling untuk image files
        location ~* \.(jpg|jpeg|png|gif|webp|svg)$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate" always;
            add_header Pragma "no-cache" always;
            
            # Immediate file check
            try_files \$uri =404;
            
            # Logging untuk debugging
            access_log /var/log/nginx/photo_access.log combined;
            error_log /var/log/nginx/photo_error.log debug;
        }
        
        # Rate limiting untuk uploads
        limit_req zone=upload_limit burst=20 nodelay;
    }

    # Static assets dengan minimal caching
    location ~* \.(css|js|ico|manifest\.json)$ {
        expires 1h;
        add_header Cache-Control "public, max-age=3600";
        access_log off;
    }

    # API routes dengan no caching
    location /api/ {
        proxy_pass http://nextjs_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # No caching untuk API
        proxy_cache_bypass 1;
        proxy_no_cache 1;
        proxy_buffering off;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Main Next.js application
    location / {
        proxy_pass http://nextjs_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Caching bypass untuk dynamic content
        proxy_cache_bypass \$http_upgrade;
        proxy_buffering off;
        
        # Connection settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

log_success "Advanced site configuration created"

# STEP 5: Advanced Directory Setup
log_action "Setting up advanced directory structure..."

# Buat struktur directory lengkap
mkdir -p "$APP_DIR/public/uploads/absensi/photos"
mkdir -p "$APP_DIR/public/uploads/temp"
mkdir -p "$APP_DIR/public/uploads/backups"

# Advanced permissions dengan ACL support
if command -v setfacl >/dev/null 2>&1; then
    # Set ACL untuk flexible permissions
    setfacl -R -m u:$DOMAIN_USER:rwx "$APP_DIR/public/uploads" 2>/dev/null || true
    setfacl -R -m g:$DOMAIN_GROUP:rwx "$APP_DIR/public/uploads" 2>/dev/null || true
    setfacl -R -m o::r-x "$APP_DIR/public/uploads" 2>/dev/null || true
    setfacl -R -d -m u:$DOMAIN_USER:rwx "$APP_DIR/public/uploads" 2>/dev/null || true
    setfacl -R -d -m g:$DOMAIN_GROUP:rwx "$APP_DIR/public/uploads" 2>/dev/null || true
    log_success "ACL permissions set"
fi

# Standard permissions sebagai fallback
chown -R "$DOMAIN_USER:$DOMAIN_GROUP" "$APP_DIR/public/uploads"
chmod -R 755 "$APP_DIR/public/uploads"

# Set SGID untuk automatic group inheritance
find "$APP_DIR/public/uploads" -type d -exec chmod g+s {} \;

log_success "Advanced directory permissions set"

# STEP 6: Real-time File Monitoring System
log_action "Deploying advanced file monitoring system..."

# Advanced monitoring script
cat > "/usr/local/bin/advanced-photo-monitor.sh" << 'MONITOR_SCRIPT'
#!/bin/bash

# Advanced Photo Upload Monitoring System
# Monitors multiple upload directories dan auto-fix permissions real-time

set -e

# Configuration
UPLOAD_DIRS=("__UPLOAD_DIRS__")
DOMAIN_USER="__DOMAIN_USER__"
DOMAIN_GROUP="__DOMAIN_GROUP__"
LOG_FILE="/var/log/advanced-photo-monitor.log"
STATS_FILE="/var/log/photo-monitor-stats.log"

# Logging function
log_event() {
    local level="$1"
    local message="$2"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$level] $message" | tee -a "$LOG_FILE"
}

# Statistics tracking
update_stats() {
    local action="$1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') $action" >> "$STATS_FILE"
    
    # Keep only last 1000 entries
    tail -1000 "$STATS_FILE" > "${STATS_FILE}.tmp" && mv "${STATS_FILE}.tmp" "$STATS_FILE"
}

# Advanced permission fix
fix_permissions() {
    local file_path="$1"
    local retries=3
    
    for ((i=1; i<=retries; i++)); do
        if [ -f "$file_path" ]; then
            # Wait untuk file write completion
            sleep 0.1
            
            # Fix ownership
            if chown "$DOMAIN_USER:$DOMAIN_GROUP" "$file_path" 2>/dev/null; then
                chmod 644 "$file_path" 2>/dev/null || true
                
                # Set ACL jika tersedia
                if command -v setfacl >/dev/null 2>&1; then
                    setfacl -m u:$DOMAIN_USER:rw- "$file_path" 2>/dev/null || true
                    setfacl -m g:$DOMAIN_GROUP:rw- "$file_path" 2>/dev/null || true
                    setfacl -m o::r-- "$file_path" 2>/dev/null || true
                fi
                
                log_event "SUCCESS" "Fixed permissions for: $file_path (attempt $i)"
                update_stats "PERMISSION_FIXED:$file_path"
                return 0
            else
                log_event "WARNING" "Failed to fix permissions for: $file_path (attempt $i)"
                sleep 0.2
            fi
        else
            log_event "WARNING" "File not found: $file_path (attempt $i)"
            sleep 0.1
        fi
    done
    
    log_event "ERROR" "Failed to fix permissions after $retries attempts: $file_path"
    update_stats "PERMISSION_FAILED:$file_path"
    return 1
}

# Main monitoring function
start_monitoring() {
    log_event "INFO" "Starting advanced photo monitoring..."
    log_event "INFO" "Monitoring directories: ${UPLOAD_DIRS[*]}"
    log_event "INFO" "Target user:group = $DOMAIN_USER:$DOMAIN_GROUP"
    
    # Check if directories exist
    for dir in "${UPLOAD_DIRS[@]}"; do
        if [ ! -d "$dir" ]; then
            log_event "WARNING" "Directory does not exist: $dir"
            mkdir -p "$dir" 2>/dev/null || true
            chown "$DOMAIN_USER:$DOMAIN_GROUP" "$dir" 2>/dev/null || true
        fi
    done
    
    # Start inotify monitoring
    inotifywait -m -r -q \
        -e create,moved_to,close_write \
        --format '%w%f %e %T' \
        --timefmt '%Y-%m-%d %H:%M:%S' \
        "${UPLOAD_DIRS[@]}" 2>/dev/null | \
    while read file_path event timestamp; do
        # Process only image files
        if [[ "$file_path" =~ \.(jpg|jpeg|png|gif|webp|svg)$ ]]; then
            log_event "INFO" "New image detected: $file_path (event: $event)"
            update_stats "IMAGE_DETECTED:$file_path"
            
            # Fix permissions
            fix_permissions "$file_path" &
            
            # Optional: trigger nginx reload untuk extreme cases
            # systemctl reload nginx >/dev/null 2>&1 || true
        fi
    done
}

# Signal handlers
cleanup() {
    log_event "INFO" "Monitoring stopped"
    exit 0
}

trap cleanup SIGTERM SIGINT

# Health check function
health_check() {
    local pid_file="/var/run/advanced-photo-monitor.pid"
    echo $$ > "$pid_file"
    
    while true; do
        sleep 300  # 5 minutes
        log_event "INFO" "Health check - monitoring active"
        update_stats "HEALTH_CHECK"
    done &
}

# Start monitoring
main() {
    # Create log files
    touch "$LOG_FILE" "$STATS_FILE"
    chown "$DOMAIN_USER:$DOMAIN_GROUP" "$LOG_FILE" "$STATS_FILE" 2>/dev/null || true
    
    # Start health check
    health_check
    
    # Start main monitoring
    start_monitoring
}

# Run main function
main
MONITOR_SCRIPT

# Replace placeholders
UPLOAD_DIRS_STR=""
for dir in "$APP_DIR/public/uploads"; do
    UPLOAD_DIRS_STR="$UPLOAD_DIRS_STR \"$dir\""
done

sed -i "s|__UPLOAD_DIRS__|$UPLOAD_DIRS_STR|g" "/usr/local/bin/advanced-photo-monitor.sh"
sed -i "s|__DOMAIN_USER__|$DOMAIN_USER|g" "/usr/local/bin/advanced-photo-monitor.sh"
sed -i "s|__DOMAIN_GROUP__|$DOMAIN_GROUP|g" "/usr/local/bin/advanced-photo-monitor.sh"

chmod +x "/usr/local/bin/advanced-photo-monitor.sh"

# Advanced systemd service
cat > "/etc/systemd/system/advanced-photo-monitor.service" << EOF
[Unit]
Description=Advanced Photo Upload Monitor
Documentation=https://github.com/your-repo/advanced-photo-monitor
After=network.target nginx.service
Wants=nginx.service

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/local/bin/advanced-photo-monitor.sh
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
TimeoutStopSec=20

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768

# Security
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$APP_DIR/public/uploads /var/log /var/run

# Performance
Nice=-5
IOSchedulingClass=2
IOSchedulingPriority=4

[Install]
WantedBy=multi-user.target
EOF

# Deploy service
systemctl daemon-reload
systemctl enable advanced-photo-monitor.service

log_success "Advanced monitoring system deployed"

# STEP 7: System Optimization
log_action "Applying advanced system optimization..."

# Advanced inotify optimization
cat >> "/etc/sysctl.conf" << 'EOF'

# Advanced Auto-Refresh Optimization
fs.inotify.max_user_watches=1048576
fs.inotify.max_user_instances=1024
fs.inotify.max_queued_events=65536
fs.file-max=2097152
fs.nr_open=1048576

# Network optimization
net.core.somaxconn=65535
net.core.netdev_max_backlog=5000
net.ipv4.tcp_max_syn_backlog=65535
net.ipv4.tcp_keepalive_time=60
net.ipv4.tcp_keepalive_intvl=10
net.ipv4.tcp_keepalive_probes=6
EOF

# Apply sysctl changes
sysctl -p >/dev/null 2>&1

# Nginx worker optimization
NGINX_WORKERS=$(nproc)
sed -i "s/worker_processes auto;/worker_processes $NGINX_WORKERS;/" /etc/nginx/nginx.conf 2>/dev/null || true

log_success "System optimization applied"

# STEP 8: Testing & Validation
log_action "Running comprehensive tests..."

# Test nginx configuration
if ! nginx -t; then
    log_error "Nginx configuration error detected!"
    log_warning "Restoring backup..."
    cp "$BACKUP_DIR/nginx.conf" /etc/nginx/nginx.conf
    cp "$BACKUP_DIR/$(basename "$MAIN_CONFIG").backup" "$MAIN_CONFIG" 2>/dev/null || true
    systemctl reload nginx
    exit 1
fi

# Restart services
systemctl restart nginx
systemctl start advanced-photo-monitor.service

# Wait untuk services startup
sleep 3

# Service status check
NGINX_STATUS=$(systemctl is-active nginx)
MONITOR_STATUS=$(systemctl is-active advanced-photo-monitor.service)

if [ "$NGINX_STATUS" = "active" ] && [ "$MONITOR_STATUS" = "active" ]; then
    log_success "✅ All services running successfully"
else
    log_warning "⚠️ Service status - Nginx: $NGINX_STATUS, Monitor: $MONITOR_STATUS"
fi

# Real-world test
log_action "Performing real-world test..."

# Create test photo
TEST_DIR="$APP_DIR/public/uploads/absensi/photos/test-advanced-$(date +%s)"
mkdir -p "$TEST_DIR"

TEST_FILE="$TEST_DIR/test-photo.jpg"
# Create a small valid JPEG test file
printf '\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\x27 ($\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9' > "$TEST_FILE"

# Monitor file permission fix
sleep 1

# Check permissions
ACTUAL_OWNER=$(stat -c '%U:%G' "$TEST_FILE" 2>/dev/null || echo "unknown")
EXPECTED_OWNER="$DOMAIN_USER:$DOMAIN_GROUP"

if [ "$ACTUAL_OWNER" = "$EXPECTED_OWNER" ]; then
    log_success "✅ Permission auto-fix working! ($ACTUAL_OWNER)"
else
    log_warning "⚠️ Permission mismatch. Expected: $EXPECTED_OWNER, Got: $ACTUAL_OWNER"
fi

# Test HTTP access
TEST_URL="http://localhost/uploads/absensi/photos/$(basename "$TEST_DIR")/test-photo.jpg"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 10 2>/dev/null || echo "000")

if [ "$HTTP_STATUS" = "200" ]; then
    log_success "✅ HTTP access test passed! (Status: $HTTP_STATUS)"
else
    log_warning "⚠️ HTTP access test failed. Status: $HTTP_STATUS"
fi

# Check if monitoring logged the event
if grep -q "test-photo.jpg" "/var/log/advanced-photo-monitor.log" 2>/dev/null; then
    log_success "✅ Monitoring system detected the test file"
else
    log_warning "⚠️ Monitoring system may not have detected the test file"
fi

# Cleanup test files
rm -rf "$TEST_DIR"

# STEP 9: Final Summary & Instructions
echo ""
echo -e "${GREEN}"
echo "================================================="
echo "   ADVANCED AUTO-REFRESH FIX COMPLETED"
echo "================================================="
echo -e "${NC}"

log_success "🎉 INSTALLATION COMPLETE!"
echo ""

echo -e "${CYAN}📊 SYSTEM STATUS:${NC}"
echo "• Nginx: $NGINX_STATUS"
echo "• Advanced Monitor: $MONITOR_STATUS"
echo "• App Directory: $APP_DIR"
echo "• Upload Owner: $DOMAIN_USER:$DOMAIN_GROUP"
echo ""

echo -e "${CYAN}🔧 OPTIMIZATIONS APPLIED:${NC}"
echo "• ✅ Real-time nginx configuration (no caching)"
echo "• ✅ Advanced file monitoring system"
echo "• ✅ Auto-permission fixing"
echo "• ✅ System inotify optimization"
echo "• ✅ ACL-based flexible permissions"
echo "• ✅ Health monitoring & statistics"
echo ""

echo -e "${CYAN}📋 MONITORING COMMANDS:${NC}"
echo "• Service status: systemctl status advanced-photo-monitor"
echo "• Live logs: journalctl -u advanced-photo-monitor -f"
echo "• Photo logs: tail -f /var/log/advanced-photo-monitor.log"
echo "• Statistics: tail -f /var/log/photo-monitor-stats.log"
echo "• Nginx logs: tail -f /var/log/nginx/photo_access.log"
echo ""

echo -e "${CYAN}🧪 TESTING INSTRUCTIONS:${NC}"
echo "1. Upload foto baru di aplikasi absensi"
echo "2. Langsung buka detail absensi - foto harus muncul TANPA reload"
echo "3. Check logs: tail -f /var/log/advanced-photo-monitor.log"
echo "4. Monitor stats: tail -f /var/log/photo-monitor-stats.log"
echo ""

echo -e "${CYAN}🔍 TROUBLESHOOTING:${NC}"
echo "• Restart monitor: systemctl restart advanced-photo-monitor"
echo "• Reload nginx: systemctl reload nginx"
echo "• Check permissions: ls -la $APP_DIR/public/uploads/"
echo "• Manual fix: chown -R $DOMAIN_USER:$DOMAIN_GROUP $APP_DIR/public/uploads/"
echo ""

echo -e "${CYAN}⚠️ ROLLBACK (Emergency):${NC}"
echo "• Stop monitor: systemctl stop advanced-photo-monitor"
echo "• Disable monitor: systemctl disable advanced-photo-monitor"
echo "• Restore nginx: cp $BACKUP_DIR/nginx.conf /etc/nginx/nginx.conf"
echo "• Restart nginx: systemctl restart nginx"
echo ""

log_success "🚀 FOTO UPLOAD SEKARANG REAL-TIME TANPA RELOAD NGINX!"
log_info "🎯 Test sekarang dengan upload foto baru di aplikasi"

echo -e "${GREEN}=================================================${NC}"
