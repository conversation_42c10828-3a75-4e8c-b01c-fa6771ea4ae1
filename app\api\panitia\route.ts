import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

// GET: Mengambil daftar semua panitia (opsional dengan filter pelatihanId)
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const pelatihanId = searchParams.get('pelatihanId');
    
    // Filter berdasarkan pelatihanId jika ada
    let whereClause = {};
    if (pelatihanId) {
      whereClause = {
        pelatihanId: pelatihanId
      };
    }

    const panitia = await prisma.panitia.findMany({
      where: whereClause,
      include: {
        pegawai: true,
        lokasi: true,
        pelatihan: {
          select: {
            nama: true,
            tgl_mulai: true,
            tgl_berakhir: true
          }
        }
      },
      orderBy: [
        {
          jabatan: 'asc' // Order by enum priority (PENGARAH first)
        }
      ]
    });

    return NextResponse.json(panitia);
  } catch (error) {
    console.error('Error fetching panitia:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST: Membuat panitia baru
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    
    if (!user || !['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { 
      pelatihanId, 
      pegawaiId, 
      jabatan, 
      lokasiId,
      no_surat, 
      keterangan 
    } = body;

    // Validasi input
    if (!pelatihanId || !pegawaiId || !jabatan || !lokasiId) {
      return NextResponse.json({ error: 'Field yang wajib diisi belum lengkap' }, { status: 400 });
    }

    // Periksa apakah pelatihan ada dan ambil tanggal mulai/selesai
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { id: pelatihanId },
      select: {
        id: true,
        tgl_mulai: true,
        tgl_berakhir: true
      }
    });

    if (!pelatihan) {
      return NextResponse.json({ error: 'Pelatihan tidak ditemukan' }, { status: 404 });
    }

    // Periksa apakah pegawai ada
    const pegawai = await prisma.pegawai.findUnique({
      where: { id: pegawaiId },
    });

    if (!pegawai) {
      return NextResponse.json({ error: 'Pegawai tidak ditemukan' }, { status: 404 });
    }

    // Periksa apakah lokasi tugas ada
    const lokasi = await prisma.t_tugas.findUnique({
      where: { id: lokasiId },
    });

    if (!lokasi) {
      return NextResponse.json({ error: 'Lokasi tugas tidak ditemukan' }, { status: 404 });
    }

    // Validasi enum jabatan
    const validJabatan = ['PENGARAH', 'PENANGGUNG_JAWAB', 'KETUA', 'ANGGOTA', 'MODERATOR', 'HOST'];
    if (!validJabatan.includes(jabatan)) {
      return NextResponse.json(
        { error: 'Jabatan tidak valid. Pilih dari: Pengarah, Penanggung Jawab, Ketua, Anggota, Moderator, atau Host' }, 
        { status: 400 }
      );
    }

    // Buat panitia baru dengan menggunakan tanggal dari pelatihan
    const panitia = await prisma.panitia.create({
      data: {
        pelatihanId,
        pegawaiId,
        jabatan,
        lokasiId,
        no_surat,
        keterangan,
      },
      include: {
        pegawai: true,
        lokasi: true,
        pelatihan: {
          select: {
            tgl_mulai: true,
            tgl_berakhir: true
          }
        }
      }
    });

    return NextResponse.json(panitia, { status: 201 });
  } catch (error) {
    console.error('Error creating panitia:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}