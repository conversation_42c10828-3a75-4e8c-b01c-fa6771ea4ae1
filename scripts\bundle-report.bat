@echo off
echo.
echo 📊 Bundle Size Analysis Report
echo ================================
echo.

if exist ".next\static" (
    echo ✅ Production build found
    echo.
    echo 📦 JavaScript Bundle Sizes:
    echo ----------------------------
    for /r ".next\static\chunks" %%f in (*.js) do (
        echo %%~nxf - %%~zf bytes
    )
    echo.
    
    echo 🎨 CSS Bundle Sizes:
    echo --------------------
    for /r ".next\static\css" %%f in (*.css) do (
        echo %%~nxf - %%~zf bytes
    )
    echo.
    
    echo 📄 Page Bundles:
    echo ----------------
    for /r ".next\static\chunks\pages" %%f in (*.js) do (
        echo %%~nxf - %%~zf bytes
    )
    echo.
    
    echo 🔄 App Router Bundles:
    echo ---------------------
    for /r ".next\static\chunks\app" %%f in (*.js) do (
        echo %%~nxf - %%~zf bytes
    )
    
) else (
    echo ❌ No production build found
    echo Run: npm run build first
)

echo.
echo 💡 Optimization Tips:
echo - Files over 100KB should be code-split
echo - Use dynamic imports for heavy components
echo - Check for duplicate dependencies
echo.

pause
