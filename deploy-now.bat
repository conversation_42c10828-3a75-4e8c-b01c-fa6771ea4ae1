@echo off
echo ============================================
echo   Uploading Photo Fix to VPS
echo ============================================

REM Upload main deployment archive
echo Uploading deployment archive...
scp .next\deployment.tar.gz <EMAIL>:/home/<USER>/

REM Upload fix script
echo Uploading fix script...
scp fix-photo-upload-vps.sh <EMAIL>:/home/<USER>/

REM Upload nginx config
echo Uploading nginx config...
scp nginx-config.txt <EMAIL>:/home/<USER>/

echo.
echo ============================================
echo   Upload Complete! Now SSH to your VPS:
echo   ssh <EMAIL>
echo ============================================
pause
