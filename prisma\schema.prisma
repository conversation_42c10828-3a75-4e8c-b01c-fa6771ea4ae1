generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model absensi {
  id           String             @id
  pelatihanId  String
  nama         String
  nip_nik      String?
  jabatan      String?
  unit_kerja   String
  no_hp        String
  waktu        DateTime           @default(now())
  tanda_tangan String             @db.Text
  foto_path    String?            // Path ke file foto absensi (untuk absensi internal)
  foto_url     String?            @db.Text // URL publik untuk akses foto absensi
  latitude     Float?             // Geolokasi koordinat lintang
  longitude    Float?             // Geolokasi koordinat bujur
  alamat       String?            // Alamat dari koordinat (hasil reverse geocoding)
  createdAt    DateTime           @default(now())
  jenjang      pelatihan_jenjang?
  pelatihan    pelatihan          @relation("PelatihanAbsensi", fields: [pelatihanId], references: [id], onDelete: Cascade)

  @@index([pelatihanId], map: "Absensi_pelatihanId_idx")
}

model biodata {
  id                String             @id @default(uuid())
  nama              String
  tempat_lahir      String
  tanggal_lahir     DateTime
  pendidikan        String
  jenis_kelamin     String
  nip               String?            @unique(map: "Biodata_nip_key")
  pangkat_golongan  String?
  jabatan           String
  unit_kerja        String
  alamat_unit_kerja String
  npwp              String?
  email             String             @unique(map: "Biodata_email_key")
  no_hp             String
  kota              String             @default("Samarinda") // Kota untuk eksport PDF
  tanda_tangan      String             @db.Text
  foto_path         String?            // Path ke file foto yang diupload
  foto_url          String?            @db.Text // URL publik untuk akses foto (using TEXT for longer URLs)
  pelatihanId       String
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  jenjang           pelatihan_jenjang?
  pelatihan         pelatihan          @relation("PelatihanBiodata", fields: [pelatihanId], references: [id], onDelete: Cascade)

  @@index([pelatihanId], map: "Biodata_pelatihanId_idx")
}

model pelatihan {
  id                     String                     @id
  nama                   String
  tempat                 String
  tgl_mulai              DateTime
  tgl_berakhir           DateTime
  link_registrasi        String                     @unique(map: "Pelatihan_link_registrasi_key")
  link_absensi           String                     @unique(map: "Pelatihan_link_absensi_key")
  link_absensi_internal  String?                    @unique(map: "Pelatihan_link_absensi_internal_key")
  link_absensi_eksternal String?                    @unique(map: "Pelatihan_link_absensi_eksternal_key")
  peserta_kegiatan       peserta_kegiatan_enum      @default(INTERNAL)
  createdAt              DateTime                   @default(now())
  updatedAt              DateTime
  jenjang                pelatihan_jenjang
  target_peserta         Int
  userId                 String
  absensi                absensi[]                  @relation("PelatihanAbsensi")
  biodata                biodata[]                  @relation("PelatihanBiodata")
  materiPelatihan        materi_pelatihan[]
  panitia                panitia[]                  @relation("PelatihanPanitia")
  user                   user                       @relation(fields: [userId], references: [id], map: "Pelatihan_userId_fkey")
  jenjangTargets         pelatihan_jenjang_target[]
  forms                  form[] // Relasi balik untuk form
  is_internal            Boolean                    @default(false) // True = internal, False = eksternal
  venue_id               String?                    // Relasi ke training_venue
  venue                  training_venue?            @relation(fields: [venue_id], references: [id])

  @@index([userId], map: "Pelatihan_userId_idx")
}

model pelatihan_jenjang_target {
  id             String            @id @default(uuid())
  pelatihanId    String
  jenjang        pelatihan_jenjang
  target_peserta Int
  createdAt      DateTime          @default(now())
  pelatihan      pelatihan         @relation(fields: [pelatihanId], references: [id], onDelete: Cascade)

  @@index([pelatihanId])
}

model user {
  id            String           @id
  name          String
  email         String           @unique(map: "User_email_key")
  password      String
  role          user_role        @default(USER)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime
  pelatihan     pelatihan[]
  forms         form[] // Relasi balik untuk form
  formResponses formSubmission[] @relation("formResponses") // Relasi balik untuk formSubmission

  @@map("user")
}

model error_log {
  id        String    @id @default(uuid())
  level     log_level
  message   String    @db.Text
  stack     String?   @db.Text
  path      String?
  method    String?
  userId    String?
  userAgent String?   @db.Text
  ip        String?
  createdAt DateTime  @default(now())
  resolved  Boolean   @default(false)

  @@index([level])
  @@index([userId])
  @@index([createdAt])
}

model materi_pelatihan {
  id          String    @id @default(uuid())
  pelatihanId String
  nama_file   String
  path_file   String
  size        Int
  mime_type   String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  pelatihan   pelatihan @relation(fields: [pelatihanId], references: [id], onDelete: Cascade)

  @@index([pelatihanId])
}

model pegawai {
  id        String    @id @default(uuid())
  nama      String
  nip       String    @unique
  p_gol     String
  jabatan   String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  panitia   panitia[]

  @@index([nip])
}

model t_tugas {
  id        String    @id @default(uuid())
  kab_kota  String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  panitia   panitia[]
}

model panitia {
  id          String          @id @default(uuid())
  pelatihanId String
  pegawaiId   String
  jabatan     panitia_jabatan
  lokasiId    String
  no_surat    String?
  keterangan  String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  lokasi      t_tugas         @relation(fields: [lokasiId], references: [id])
  pegawai     pegawai         @relation(fields: [pegawaiId], references: [id], onDelete: Cascade)
  pelatihan   pelatihan       @relation("PelatihanPanitia", fields: [pelatihanId], references: [id], onDelete: Cascade)

  @@index([pelatihanId])
  @@index([pegawaiId])
  @@index([lokasiId])
}

model training_venue {
  id          String    @id @default(uuid())
  nama        String
  alamat      String
  latitude    Float     // Koordinat lintang pusat venue
  longitude   Float     // Koordinat bujur pusat venue
  radius      Int       // Radius geofence dalam meter
  is_active   Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relasi dengan pelatihan
  pelatihan   pelatihan[]

  @@index([is_active])
}

enum user_role {
  ADMIN
  USER
  GM1
  GM2
  GM3
  GM4
  GM5
  KEPALA
  KASUBAG
}

enum pelatihan_jenjang {
  PAUD
  SD
  SMP
  SMA
  UMUM
  DINAS
  BPMP
  LAINNYA
}

enum log_level {
  INFO
  WARNING
  ERROR
  CRITICAL
}

enum panitia_jabatan {
  PENGARAH
  PENANGGUNG_JAWAB
  KETUA
  ANGGOTA
  MODERATOR
  HOST
}

enum peserta_kegiatan_enum {
  INTERNAL
  EKSTERNAL
  KEDUANYA
}

// Enum untuk tipe pertanyaan
enum question_type {
  SHORT_TEXT
  LONG_TEXT
  SINGLE_CHOICE
  MULTIPLE_CHOICE
  DROPDOWN
  DATE
  TIME
  FILE_UPLOAD
  SCALE
  EMAIL
  PHONE
  NUMBER
}

// Model untuk form
model form {
  id          String   @id @default(cuid())
  title       String
  description String?  @db.Text
  userId      String // ID pembuat form
  pelatihanId String? // ID pelatihan terkait
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  isPublished Boolean  @default(false)
  isDeleted   Boolean  @default(false)

  // Relasi
  user        user             @relation(fields: [userId], references: [id])
  pelatihan   pelatihan?       @relation(fields: [pelatihanId], references: [id])
  questions   question[]
  submissions formSubmission[]

  @@index([userId])
  @@index([pelatihanId])
}

// Model untuk pertanyaan dalam form
model question {
  id              String        @id @default(cuid())
  formId          String
  questionText    String        @db.Text
  questionType    question_type
  isRequired      Boolean       @default(false)
  order           Int
  options         Json? // Untuk pilihan pada multiple choice, dropdown, dll
  validationRules Json? // Aturan validasi tambahan
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relasi
  form    form     @relation(fields: [formId], references: [id], onDelete: Cascade)
  answers answer[]

  @@index([formId])
}

// Model untuk submission form
model formSubmission {
  id             String   @id @default(cuid())
  formId         String
  respondentId   String? // Opsional, jika responden adalah user terdaftar
  respondentInfo Json? // Informasi responden jika tidak terdaftar
  submittedAt    DateTime @default(now())

  // Relasi
  form    form     @relation(fields: [formId], references: [id], onDelete: Cascade)
  user    user?    @relation("formResponses", fields: [respondentId], references: [id])
  answers answer[]

  @@index([formId])
  @@index([respondentId])
}

// Model untuk jawaban
model answer {
  id           String  @id @default(cuid())
  submissionId String
  questionId   String
  answerValue  String? @db.Text
  answerJson   Json? // Untuk jawaban kompleks
  fileUrl      String? // Untuk jawaban berupa file

  // Relasi
  submission formSubmission @relation(fields: [submissionId], references: [id], onDelete: Cascade)
  question   question       @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@index([submissionId])
  @@index([questionId])
}
