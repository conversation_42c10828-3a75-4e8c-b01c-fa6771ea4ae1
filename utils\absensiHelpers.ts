import { PrismaClient } from '@prisma/client';
import { getIndonesiaDayRange } from './dateUtils';

/**
 * Membuat whereClause untuk query absensi berdasarkan filter
 */
export function buildAbsensiWhereClause(
  userPelatihanIds: string[],
  pelatihanId?: string | null,
  date?: string | null
) {
  // Build where clause for filtering
  const whereClause: {
    pelatihanId: { in: string[] } | string;
    waktu?: { gte: Date; lte: Date };
  } = {
    pelatihanId: {
      in: userPelatihanIds,
    },
  };
  
  // Add pelatihan filter if provided
  if (pelatihanId) {
    whereClause.pelatihanId = pelatihanId;
  }
    // Add date filter if provided
  if (date) {
    const { start, end } = getIndonesiaDayRange(date);
    
    whereClause.waktu = {
      gte: start,
      lte: end,
    };
  }

  return whereClause;
}

/**
 * Fungsi untuk mengambil data absensi dengan filter dan paginasi
 */
export async function getFilteredAbsensi(
  prisma: PrismaClient,
  userId: string,
  page: number,
  limit: number,
  pelatihanId?: string | null,
  date?: string | null
) {
  // Get pagination parameters
  const skip = (page - 1) * limit;
  
  // First, get all pelatihan IDs created by the current user
  const userPelatihan = await prisma.pelatihan.findMany({
    where: {
      userId: userId,
    },
    select: {
      id: true,
    },
  });

  // Extract just the IDs into an array
  const userPelatihanIds = userPelatihan.map((item) => item.id);
  
  // Build where clause
  const whereClause = buildAbsensiWhereClause(userPelatihanIds, pelatihanId, date);

  try {
    // Use Prisma transaction for better performance
    const [totalAbsensi, absensi, pelatihan] = await prisma.$transaction([
      // Count total absensi for pagination
      prisma.absensi.count({
        where: whereClause,
      }),
      
      // Fetch absensi with pagination and filters
      prisma.absensi.findMany({
        where: whereClause,
        orderBy: {
          waktu: 'desc',
        },
        include: {
          pelatihan: {
            select: {
              id: true,
              nama: true,
              tempat: true,
              tgl_mulai: true,
              tgl_berakhir: true
            },
          },
        },
        skip,
        take: limit,
      }),
      
      // Fetch daftar pelatihan for filter
      prisma.pelatihan.findMany({
        where: {
          userId: userId,
        },
        select: {
          id: true,
          nama: true,
        },
        orderBy: {
          nama: 'asc',
        },
      })
    ]);
    
    return {
      totalAbsensi,
      absensi,      pelatihan,
      totalPages: Math.ceil(totalAbsensi / limit)
    };
  } catch (error) {
    throw error;
  }
}
