#!/bin/bash

# Ubuntu 24.04 - Nginx Config Fix untuk Photo Upload 404
# Script khusus untuk Ubuntu 24.04 dengan nginx config di /etc/nginx/nginx.conf

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

echo -e "${GREEN}"
echo "============================================"
echo "   UBUNTU 24.04 NGINX FIX - PHOTO UPLOAD"
echo "============================================"
echo -e "${NC}"

# STEP 1: Auto-detect aplikasi Next.js
log_info "Mencari aplikasi Next.js di Ubuntu 24.04..."

POSSIBLE_APPS=()

# Cari berdasarkan server.js (Next.js standalone)
while IFS= read -r -d '' app_path; do
    app_dir=$(dirname "$app_path")
    POSSIBLE_APPS+=("$app_dir")
    log_success "Ditemukan Next.js app: $app_dir"
done < <(find /home /var/www /opt /usr/local -name "server.js" -path "*/standalone/*" -print0 2>/dev/null || true)

# Cari berdasarkan package.json dengan next
while IFS= read -r -d '' package_path; do
    if grep -q '"next"' "$package_path" 2>/dev/null; then
        app_dir=$(dirname "$package_path")
        if [[ ! " ${POSSIBLE_APPS[@]} " =~ " ${app_dir} " ]]; then
            POSSIBLE_APPS+=("$app_dir")
            log_success "Ditemukan Next.js project: $app_dir"
        fi
    fi
done < <(find /home /var/www /opt /usr/local -name "package.json" -print0 2>/dev/null || true)

# Jika tidak ada yang ditemukan, gunakan default locations untuk Ubuntu
if [ ${#POSSIBLE_APPS[@]} -eq 0 ]; then
    log_warning "Tidak ditemukan aplikasi Next.js otomatis, menggunakan default Ubuntu paths..."
    POSSIBLE_APPS=(
        "/var/www/html"
        "/home/<USER>/app"
        "/opt/app"
        "/usr/local/app"
        "/home/<USER>/app"
    )
fi

# Pilih app directory yang valid
APP_DIR=""
for dir in "${POSSIBLE_APPS[@]}"; do
    if [ -d "$dir" ]; then
        APP_DIR="$dir"
        log_success "Menggunakan app directory: $APP_DIR"
        break
    fi
done

if [ -z "$APP_DIR" ]; then
    log_error "Tidak dapat menemukan aplikasi directory yang valid"
    log_warning "Menggunakan default: /var/www/html"
    APP_DIR="/var/www/html"
    mkdir -p "$APP_DIR"
fi

# STEP 2: Buat backup
BACKUP_DIR="/tmp/nginx-photo-fix-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
log_info "Backup directory: $BACKUP_DIR"

# Backup nginx.conf
cp /etc/nginx/nginx.conf "$BACKUP_DIR/nginx.conf.backup"
log_success "Nginx config di-backup"

# STEP 3: Buat struktur folder uploads
log_action "Membuat struktur folder uploads..."

mkdir -p "$APP_DIR/public/uploads/absensi/photos"
chown -R www-data:www-data "$APP_DIR/public/uploads" 2>/dev/null || true
chmod -R 755 "$APP_DIR/public/uploads"

log_success "Folder uploads dibuat: $APP_DIR/public/uploads/absensi/photos/"

# STEP 4: Perbaiki duplikasi folder jika ada
if [ -d "$APP_DIR/public/public" ]; then
    log_warning "Ditemukan duplikasi folder public/public"
    
    # Backup duplikasi
    cp -r "$APP_DIR/public/public" "$BACKUP_DIR/" 2>/dev/null || true
    
    # Pindahkan konten
    if [ -d "$APP_DIR/public/public/uploads" ]; then
        cp -r "$APP_DIR/public/public/uploads/"* "$APP_DIR/public/uploads/" 2>/dev/null || true
        log_success "File dipindahkan dari duplikasi"
    fi
    
    # Hapus duplikasi
    rm -rf "$APP_DIR/public/public"
    log_success "Duplikasi folder dihapus"
fi

# STEP 5: Analisis nginx.conf structure
log_action "Menganalisis struktur nginx.conf..."

# Cek apakah nginx.conf menggunakan include untuk sites
if grep -q "include.*sites-enabled" /etc/nginx/nginx.conf; then
    log_info "Nginx menggunakan sites-enabled structure"
    USE_SITES_ENABLED=true
    
    # Pastikan folder sites-available dan sites-enabled ada
    mkdir -p /etc/nginx/sites-available
    mkdir -p /etc/nginx/sites-enabled
    
    SITE_CONFIG="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
    
elif grep -q "include.*conf.d" /etc/nginx/nginx.conf; then
    log_info "Nginx menggunakan conf.d structure"
    USE_SITES_ENABLED=false
    
    mkdir -p /etc/nginx/conf.d
    SITE_CONFIG="/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
    
else
    log_warning "Nginx structure tidak standar, membuat konfigurasi di conf.d"
    USE_SITES_ENABLED=false
    
    # Tambahkan include conf.d jika belum ada
    if ! grep -q "include.*conf.d" /etc/nginx/nginx.conf; then
        sed -i '/http {/a\    include /etc/nginx/conf.d/*.conf;' /etc/nginx/nginx.conf
    fi
    
    mkdir -p /etc/nginx/conf.d
    SITE_CONFIG="/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
fi

# STEP 6: Buat/Update konfigurasi site
log_action "Membuat konfigurasi untuk kegiatan.bpmpkaltim.id..."

# Backup existing config jika ada
if [ -f "$SITE_CONFIG" ]; then
    cp "$SITE_CONFIG" "$BACKUP_DIR/$(basename $SITE_CONFIG).backup"
fi

# Buat konfigurasi site
cat > "$SITE_CONFIG" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name kegiatan.bpmpkaltim.id;

    # Static files untuk uploads
    location /uploads/ {
        root $APP_DIR/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        
        # Logging untuk debug
        access_log /var/log/nginx/uploads_access.log;
        error_log /var/log/nginx/uploads_error.log;
        
        # Try files dengan fallback
        try_files \$uri \$uri/ =404;
    }

    # Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

log_success "Site config dibuat: $SITE_CONFIG"

# STEP 7: Aktifkan site jika menggunakan sites-enabled
if [ "$USE_SITES_ENABLED" = true ]; then
    ln -sf "$SITE_CONFIG" "/etc/nginx/sites-enabled/"
    log_success "Site diaktifkan di sites-enabled"
    
    # Nonaktifkan default site jika ada dan tidak perlu
    if [ -f "/etc/nginx/sites-enabled/default" ]; then
        if ! grep -q "kegiatan.bpmpkaltim.id" /etc/nginx/sites-enabled/default; then
            rm -f /etc/nginx/sites-enabled/default
            log_info "Default site dinonaktifkan"
        fi
    fi
fi

# STEP 8: Test dan reload nginx
log_action "Testing dan reload nginx..."

if nginx -t; then
    systemctl reload nginx
    log_success "Nginx configuration OK dan direload"
else
    log_error "Nginx configuration error!"
    log_warning "Restoring backup..."
    cp "$BACKUP_DIR/nginx.conf.backup" /etc/nginx/nginx.conf
    systemctl reload nginx || true
    exit 1
fi

# STEP 9: Test akses
log_action "Testing akses..."

# Buat file test
TEST_FILE="$APP_DIR/public/uploads/test-access.txt"
echo "Test file untuk verifikasi akses" > "$TEST_FILE"
chown www-data:www-data "$TEST_FILE" 2>/dev/null || true
chmod 644 "$TEST_FILE"

# Test dengan curl
TEST_URL="https://kegiatan.bpmpkaltim.id/uploads/test-access.txt"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 10 2>/dev/null || echo "000")

case $HTTP_STATUS in
    200)
        log_success "✅ Test akses berhasil! Status: $HTTP_STATUS"
        ;;
    404)
        log_warning "⚠️ File tidak ditemukan (404) - mungkin DNS atau cache issue"
        ;;
    000)
        log_error "❌ Connection failed - cek network/DNS"
        ;;
    *)
        log_info "ℹ️ Status: $HTTP_STATUS"
        ;;
esac

# Test lokal
LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost/uploads/test-access.txt" --max-time 5 2>/dev/null || echo "000")
if [ "$LOCAL_STATUS" = "200" ]; then
    log_success "✅ Test lokal berhasil! Nginx serve static files OK"
else
    log_warning "⚠️ Test lokal gagal - Status: $LOCAL_STATUS"
fi

# STEP 10: Hitung foto yang ada
PHOTO_COUNT=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l)
log_info "📸 Total foto ditemukan: $PHOTO_COUNT"

# STEP 11: Summary
echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   UBUNTU 24.04 NGINX FIX SELESAI"
echo "============================================"
echo -e "${NC}"

log_success "✅ Struktur folder: $APP_DIR/public/uploads/absensi/photos/"
log_success "✅ Nginx config: $SITE_CONFIG"
log_success "✅ Permissions: www-data:www-data 755"
log_success "✅ Backup: $BACKUP_DIR"

echo ""
log_info "🔍 VERIFIKASI:"
echo "1. Test upload foto baru di admin panel"
echo "2. Cek URL: https://kegiatan.bpmpkaltim.id/uploads/"
echo "3. Monitor: tail -f /var/log/nginx/uploads_error.log"

echo ""
log_info "📋 QUICK COMMANDS:"
echo "• Cek status: systemctl status nginx"
echo "• Test config: nginx -t"
echo "• Reload: systemctl reload nginx"
echo "• Logs: tail -f /var/log/nginx/uploads_access.log"

# Cleanup test file
rm -f "$TEST_FILE"

echo ""
log_warning "⚠️ Jika masih ada masalah, restore backup dengan:"
echo "sudo cp $BACKUP_DIR/nginx.conf.backup /etc/nginx/nginx.conf"
echo "sudo systemctl reload nginx"
