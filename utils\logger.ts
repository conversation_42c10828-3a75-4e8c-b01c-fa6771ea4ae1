/**
 * Logger utility untuk mencatat error di seluruh aplikasi
 */

type LogLevel = 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';

interface LogPayload {
  level: LogLevel;
  message: string;
  stack?: string;
  path?: string;
  method?: string;
  userId?: string;
  userAgent?: string;
  ip?: string;
  // Tambahkan properti tambahan yang mungkin dibutuhkan
  [key: string]: any; // Ini memungkinkan properti tambahan
}

// Renamed to _isRunningInMiddleware or removed since it's not used
import { isRunningInMiddleware as _isRunningInMiddleware } from './isMiddleware';

/**
 * Fungsi untuk mencatat log ke API
 * Dinonaktifkan untuk menghindari error logging yang tidak diinginkan
 */
export async function logError(_error: Error | LogPayload, _context?: any) {
  // Fungsi dinonaktifkan - tidak melakukan logging ke API
  // Tetap bisa logging ke console jika diperlukan untuk debugging
  
  // Uncomment baris berikut jika perlu melihat error di console
  // console.error(`[Logger Silent Mode]`, 'level' in error ? error.level : 'ERROR', 'message' in error ? error.message : (error as Error).message || 'Unknown error');
  
  // Tidak melakukan POST ke API
  return;
}

/**
 * Fungsi shorthand untuk berbagai level logging - dinonaktifkan
 */
export const logger = {
  info: (message: string, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
    
  warning: (message: string, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
  
  warn: (message: string, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
    
  error: (message: string, error?: Error | unknown, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
    
  critical: (message: string, error?: Error | unknown, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
};

/**
 * Logger khusus untuk middleware - dinonaktifkan
 */
export const middlewareLogger = {
  info: (message: string, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
    
  warning: (message: string, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
  
  warn: (message: string, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
    
  error: (message: string, error?: Error | unknown, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
    
  critical: (message: string, error?: Error | unknown, _options: Record<string, any> = {}) => {
    // Silent mode - no logging
  },
};

/**
 * Fungsi global error handler untuk client-side
 */
export function setupGlobalErrorLogging(): void {
  // Fungsi dinonaktifkan untuk menghindari error logging
  return;
}
