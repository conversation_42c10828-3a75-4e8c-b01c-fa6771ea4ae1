import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { addPelatihan } from '../actions';
import { fetchCsrfToken } from '@/utils/csrfClient';
import { logger } from '@/utils/logger';

// Validasi data form dengan logger
const logValidation = (data: any, formState: any) => {
  logger.info("Form validation running", { 
    hasErrors: Object.keys(formState.errors).length > 0,
    jenjangTargetsCount: data.jenjangTargets?.length 
  });
  
  if (Object.keys(formState.errors).length > 0) {
    logger.warning("Form validation errors", { errors: formState.errors });
  }
};

// Schema validasi dengan zod yang diperbaiki - validasi tanggal yang lebih fleksibel
const pelatihanSchema = z.object({
  nama: z.string().min(1, 'Nama pelatihan wajib diisi'),
  tempat: z.string().min(1, 'Tempat pelatihan wajib diisi'),
  tgl_mulai: z.string().min(1, 'Tanggal mulai wajib diisi'),
  tgl_berakhir: z.string().min(1, 'Tanggal berakhir wajib diisi'),
  peserta_kegiatan: z.enum(['internal', 'eksternal', 'keduanya'], {
    required_error: 'Peserta kegiatan wajib dipilih',
    invalid_type_error: 'Peserta kegiatan wajib dipilih',
  }),
  jenjangTargets: z.array(
    z.object({
      id: z.string(),
      jenjang: z.string().min(1, 'Jenjang wajib dipilih'),
      target_peserta: z.number().min(1, 'Target peserta minimal 1')
    })
  ).min(1, 'Minimal satu jenjang harus dipilih')
})
// Hapus validasi refine untuk tanggal agar validasi manual bisa lebih fleksibel

export type PelatihanFormData = z.infer<typeof pelatihanSchema>;

// Función para actualizar pelatihan
const updatePelatihanData = async (id: string, data: PelatihanFormData, csrfToken?: string) => {
  try {
    // Ensure the ID is properly encoded in the URL
    const encodedId = encodeURIComponent(id);
    const response = await fetch(`/api/pelatihan/${encodedId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(csrfToken ? { 'X-CSRF-Token': csrfToken } : {})
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json();
      logger.error("API error response", { statusCode: response.status, errorData });
      return { 
        success: false, 
        message: errorData.message || `Error: ${response.status} ${response.statusText}` 
      };
    }

    return await response.json();
  } catch (error) {
    logger.error("Fetch error", error instanceof Error ? error : new Error(String(error)));
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
};

// Ubah menjadi ekspor bernama (named export)
export const usePelatihanForm = (props?: { pelatihan?: any }) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [clientReady, setClientReady] = useState(false);
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
    // Default values dari props jika dalam mode edit
  const defaultValues = props?.pelatihan ? {
    nama: props.pelatihan.nama || '',
    tempat: props.pelatihan.tempat || '',
    tgl_mulai: props.pelatihan.tgl_mulai ? new Date(props.pelatihan.tgl_mulai).toISOString().split('T')[0] : '',
    tgl_berakhir: props.pelatihan.tgl_berakhir ? new Date(props.pelatihan.tgl_berakhir).toISOString().split('T')[0] : '',
    peserta_kegiatan: props.pelatihan.peserta_kegiatan ? props.pelatihan.peserta_kegiatan.toLowerCase() as 'internal' | 'eksternal' | 'keduanya' : 'internal',
    jenjangTargets: props.pelatihan.jenjangTargets?.length > 0 
      ? props.pelatihan.jenjangTargets.map((jt: any) => ({
          id: jt.id || uuidv4(),
                    jenjang: jt.jenjang || 'UMUM', // Default ke UMUM jika kosong
          target_peserta: jt.target_peserta || 1 // Default ke 1 jika 0
        }))
      : [{ id: uuidv4(), jenjang: 'UMUM', target_peserta: 1 }] // Nilai awal yang valid
  } : {
    nama: '',
    tempat: '',
    tgl_mulai: '',
    tgl_berakhir: '',
    peserta_kegiatan: 'internal' as 'internal' | 'eksternal' | 'keduanya',
    jenjangTargets: [{ id: uuidv4(), jenjang: 'UMUM', target_peserta: 1 }] // Nilai awal yang valid
  };
  
  const form = useForm<PelatihanFormData>({
    resolver: zodResolver(pelatihanSchema),
    defaultValues
  });
  
  // Destructuring hanya yang digunakan
  const {
    handleSubmit,
    setValue,
    watch,
    formState,
  } = form;

  // Set clientReady setelah hydration
  useEffect(() => {
    setClientReady(true);
  }, []);

  // Ambil CSRF token saat komponen di-mount
  useEffect(() => {
    const fetchToken = async () => {
      const token = await fetchCsrfToken();
      setCsrfToken(token);
    };
    fetchToken();
  }, []);

  // Hapus formData karena tidak digunakan
  const jenjangTargets = watch('jenjangTargets');
  const errors = formState.errors;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setValue(name as any, value, { shouldDirty: true, shouldTouch: true, shouldValidate: true });
  };

  // Fungsi untuk menambah jenjang target
  const addJenjangTarget = () => {
    const currentJenjangTargets = watch('jenjangTargets');
    setValue('jenjangTargets', [
      ...currentJenjangTargets,
      { id: uuidv4(), jenjang: 'UMUM', target_peserta: 1 } // Nilai default yang valid
    ]);
  };

  // Fungsi untuk menghapus jenjang target
  const removeJenjangTarget = (id: string) => {
    const currentJenjangTargets = watch('jenjangTargets');
    if (currentJenjangTargets.length <= 1) return;
    
    setValue(
      'jenjangTargets',
      currentJenjangTargets.filter(item => item.id !== id)
    );
  };

  // Fungsi untuk update nilai jenjang target
  const updateJenjangTarget = (id: string, field: string, value: any) => {
    const currentJenjangTargets = watch('jenjangTargets');
    const updatedTargets = currentJenjangTargets.map(item => {
      if (item.id === id) {
        return {
          ...item,
          [field]: field === 'target_peserta' ? Number(value) : value
        };
      }
      return item;
    });
    
    setValue('jenjangTargets', updatedTargets);
  };

  // Fungsi untuk handle perubahan pada jenjang target
  const handleJenjangTargetChange = (id: string, field: string, value: any) => {
    updateJenjangTarget(id, field, value);
  };

  // Modify the onSubmit function to ensure it's properly handling the submission
  const onSubmit = async (data: PelatihanFormData) => {
    logger.info("[usePelatihanForm] onSubmit dipanggil", { data });
    logValidation(data, formState);
    try {
      setFormError(null);
      setIsSubmitting(true);
      
      // Log state saat ini
      logger.info("[usePelatihanForm] State saat ini", {
        isSubmitting,
        hasErrors: Object.keys(errors).length > 0,
        csrfToken: csrfToken ? "Ada" : "Tidak ada"
      });
      
      // Validasi tanggal lebih eksplisit di sini sebagai double-check
      const tglMulai = new Date(data.tgl_mulai);
      const tglBerakhir = new Date(data.tgl_berakhir);
      
      if (tglMulai > tglBerakhir) {
        logger.error("[usePelatihanForm] Validasi tanggal gagal", {
          tglMulai: data.tgl_mulai,
          tglBerakhir: data.tgl_berakhir
        });
        setFormError("Tanggal mulai harus sebelum tanggal berakhir");
        setIsSubmitting(false);
        return;
      }
      
      // Cek validasi jenjangTargets
      const hasEmptyJenjang = data.jenjangTargets.some(jt => !jt.jenjang);
      const hasInvalidTarget = data.jenjangTargets.some(jt => !jt.target_peserta || jt.target_peserta < 1);
      
      if (hasEmptyJenjang) {
        logger.warning("[usePelatihanForm] Validasi gagal: Ada jenjang yang belum dipilih");
        setFormError("Semua jenjang harus dipilih");
        setIsSubmitting(false);
        return;
      }
      
      if (hasInvalidTarget) {
        logger.warning("[usePelatihanForm] Validasi gagal: Ada target peserta yang tidak valid");
        setFormError("Target peserta minimal 1 untuk setiap jenjang");
        setIsSubmitting(false);
        return;
      }

      // Log detail validasi berhasil
      logger.info("[usePelatihanForm] Semua validasi berhasil, melanjutkan proses");
      
      // Tambahkan sleep singkat untuk memastikan state isSubmitting terupdate
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Pastikan CSRF token berhasil diambil terlebih dahulu
      let token = csrfToken;
      if (!token) {
        logger.info("[usePelatihanForm] CSRF token tidak ditemukan, mencoba fetch ulang...");
        try {
          // Coba ambil token dengan langsung mengeksekusi fetch tanpa async/await
          const fetchTokenPromise = fetchCsrfToken();
          
          // Log sebelum await
          logger.info("[usePelatihanForm] Menunggu CSRF token...");
          
          token = await fetchTokenPromise;
          setCsrfToken(token);
          
          // Log hasil fetch token
          logger.info("[usePelatihanForm] CSRF token berhasil diambil", { tokenAvailable: !!token });
        } catch (csrfError) {
          logger.error("[usePelatihanForm] Gagal mengambil CSRF token", csrfError);
          // Tetap lanjutkan meskipun tanpa token (akan ditangani oleh back-end)
          logger.warning("[usePelatihanForm] Melanjutkan tanpa CSRF token");
        }
      } else {
        logger.info("[usePelatihanForm] CSRF token tersedia", { tokenAvailable: !!token });
      }

      // Jika edit mode
      if (props?.pelatihan?.id) {
        // Implementasi update pelatihan
        logger.info("[usePelatihanForm] Memperbarui pelatihan dengan ID", { id: props.pelatihan.id });
        
        // Ensure we're using the correct API endpoint format
        const apiUrl = `/api/pelatihan/${encodeURIComponent(props.pelatihan.id)}`;
        logger.info("[usePelatihanForm] Menggunakan API URL", { apiUrl });
        
        // Convertir csrfToken de string | null a string | undefined
        const csrfTokenValue = token === null ? undefined : token;
        
        logger.info("[usePelatihanForm] Mengirim request ke API dengan metode PUT");
        const result = await updatePelatihanData(props.pelatihan.id, data, csrfTokenValue);
        
        logger.info("[usePelatihanForm] Hasil update", { result });
        if (result.success) {
          toast.success('Pelatihan berhasil diperbarui');
          router.push('/dashboard/pelatihan');
          router.refresh();
        } else {
          setFormError(result.message || 'Gagal memperbarui pelatihan');
          logger.error("[usePelatihanForm] Gagal memperbarui pelatihan", { message: result.message });
        }
      } else {
        // Tambah pelatihan baru
        logger.info("[usePelatihanForm] Menambahkan pelatihan baru");
        
        // Ensure csrfToken is properly handled
        const csrfTokenValue = token === null ? undefined : token;
        
        // Log request yang akan dikirim
        logger.info("[usePelatihanForm] Request data", {
          dataPreview: JSON.stringify(data).substring(0, 100) + "...",
          withCsrfToken: !!csrfTokenValue
        });
        
        try {
          logger.info("[usePelatihanForm] Memanggil fungsi addPelatihan (server action)");
          const result = await addPelatihan(data, csrfTokenValue);
          
          logger.info("[usePelatihanForm] Hasil addPelatihan", { result });
          if (result && result.success) {
            logger.info("[usePelatihanForm] Berhasil! Menampilkan toast dan navigasi...");
            toast.success('Pelatihan berhasil ditambahkan');
            router.push('/dashboard/pelatihan');
            router.refresh();
          } else {
            logger.error("[usePelatihanForm] Gagal menambahkan pelatihan", { message: result?.message || "Unknown error" });
            setFormError(result?.message || 'Gagal menambahkan pelatihan');
          }
        } catch (apiError) {
          logger.error("[usePelatihanForm] Error saat memanggil fungsi addPelatihan", apiError);
          setFormError(apiError instanceof Error ? apiError.message : 'Terjadi kesalahan saat menambahkan pelatihan');
        }
      }
    } catch (error) {
      logger.error('[usePelatihanForm] Error submitting form', error instanceof Error ? error : new Error(String(error)));
      setFormError(error instanceof Error ? error.message : 'Terjadi kesalahan saat mengirim data');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Buat wrapper untuk handleSubmit
  const submitHandler = handleSubmit(onSubmit);

  // Tambahkan fungsi submission manual yang bersih tanpa console log
  const submitFormManually = async () => {
    try {
      const data = form.getValues();
      
      // Validasi form secara manual
      const isValid = await form.trigger();
      
      if (!isValid) {
        return;
      }
      
      // Set loading state
      setIsSubmitting(true);
      
      try {
        // Check if we're in edit mode
        if (props?.pelatihan?.id) {
          // Update existing pelatihan
          logger.info("[usePelatihanForm] Memperbarui pelatihan dengan ID", { id: props.pelatihan.id });
          const csrfTokenValue = csrfToken === null ? undefined : csrfToken;
          const result = await updatePelatihanData(props.pelatihan.id, data, csrfTokenValue);
          
          if (result.success) {
            toast.success('Pelatihan berhasil diperbarui');
            router.push('/dashboard/pelatihan');
            router.refresh();
          } else {
            setFormError(result.message || 'Gagal memperbarui pelatihan');
          }
        } else {
          // Add new pelatihan
          const result = await addPelatihan(data);
          
          if (result && result.success) {
            toast.success('Pelatihan berhasil ditambahkan');
            router.push('/dashboard/pelatihan');
            router.refresh();
          } else {
            setFormError(result?.message || 'Gagal menambahkan pelatihan');
          }
        }
      } catch (apiError) {
        setFormError(apiError instanceof Error ? apiError.message : 'Terjadi kesalahan saat menyimpan pelatihan');
      } finally {
        setIsSubmitting(false);
      }
    } catch (_error) {
      setFormError("Terjadi kesalahan saat mencoba menyimpan data");
      setIsSubmitting(false);
    }
  };

  return {
    form, // Ekspor seluruh form object
    handleSubmit: submitHandler,
    submitFormManually, // Ekspor fungsi submission manual
    errors,
    isSubmitting,
    jenjangTargets,
    addJenjangTarget,
    removeJenjangTarget,
    updateJenjangTarget,
    handleChange,
    handleJenjangTargetChange,
    formError,
    clientReady
  };
};

// Tetap pertahankan ekspor default untuk kompatibilitas
export default usePelatihanForm;
