"use client";

import { useState } from "react";
import InputField from "../../../../components/InputField";
import Button from "../../../../components/Button";

export default function AbsensiInternalForm() {
  const [formData, setFormData] = useState({
    nama: "",
    nip: "",
    jabatan: "",
    unit_kerja: "",
    no_hp: "",
    tanda_tangan: "",
    // tambahkan field lain sesuai kebutuhan internal
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    // Kirim data ke API absensi internal, sertakan is_internal: true
    const payload = { ...formData, is_internal: true };
    // Contoh penggunaan payload agar tidak warning (ganti dengan endpoint asli jika sudah ada)
    await fetch('/api/absensi/internal', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    setTimeout(() => {
      setIsSubmitting(false);
      setSuccess(true);
    }, 1000);
  };

  if (success) {
    return (
      <div className="p-8 text-center">
        <h2 className="mb-4 text-lg font-bold text-green-700">Absensi Berhasil!</h2>
        <p>Terima kasih telah melakukan absensi </p>
      </div>
    );
  }

  return (
    <div className="max-w-lg p-6 mx-auto mt-8 bg-white rounded shadow">
      <h1 className="mb-6 text-xl font-bold text-center">Form Absensi </h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <InputField label="Nama" name="nama" value={formData.nama} onChange={handleChange} required />
        <InputField label="NIP" name="nip" value={formData.nip} onChange={handleChange} required />
        <InputField label="Jabatan" name="jabatan" value={formData.jabatan} onChange={handleChange} required />
        <InputField label="Unit Kerja" name="unit_kerja" value={formData.unit_kerja} onChange={handleChange} required />
        <InputField label="No HP" name="no_hp" value={formData.no_hp} onChange={handleChange} required />
        {/* Tambahkan komponen tanda tangan jika diperlukan */}
        <Button type="submit" isLoading={isSubmitting} className="w-full text-white bg-green-600 hover:bg-green-700">Kirim Absensi</Button>
      </form>
    </div>
  );
}
