@echo off
setlocal enabledelayedexpansion

title Deploy Photo Upload Fix to Server

echo.
echo ===============================================
echo    DEPLOY PHOTO UPLOAD FIX TO SERVER
echo    kegiatan.bpmpkaltim.id
echo ===============================================
echo.

echo [36m🎯 SOLUTION:[0m
echo   Fix photo upload issue where uploaded photos need
echo   manual nginx reload to become accessible
echo.

set /p confirm="Deploy fix to server now? (y/n): "

if /i not "%confirm%"=="y" (
    echo [33m[INFO][0m Deployment cancelled
    pause
    exit /b 0
)

echo.
echo [35m[ACTION][0m Starting deployment to server...

:: Check if deployment script exists
if not exist "execute-deployment-server.sh" (
    echo [31m[ERROR][0m execute-deployment-server.sh not found!
    pause
    exit /b 1
)

set "VPS_HOST=*************"
set "VPS_USER=root"

echo [36m[INFO][0m Connecting to %VPS_USER%@%VPS_HOST%...

:: Step 1: Upload deployment script
echo.
echo [35m[ACTION][0m Step 1/3: Uploading deployment script...

scp execute-deployment-server.sh %VPS_USER%@%VPS_HOST%:/tmp/

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to upload deployment script
    echo [33m[INFO][0m Please check SSH connection and try again
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script uploaded to server

:: Step 2: Make script executable and run it
echo.
echo [35m[ACTION][0m Step 2/3: Executing deployment on server...

ssh %VPS_USER%@%VPS_HOST% "chmod +x /tmp/execute-deployment-server.sh && /tmp/execute-deployment-server.sh"

if %errorlevel% neq 0 (
    echo [33m[WARNING][0m Deployment completed with warnings
    echo [36m[INFO][0m Check output above for details
) else (
    echo [32m[SUCCESS][0m Deployment completed successfully
)

:: Step 3: Test the fix
echo.
echo [35m[ACTION][0m Step 3/3: Testing the fix...

echo [36m[INFO][0m Testing uploads directory accessibility...

curl -s -o nul -w "HTTP Status: %%{http_code}" https://kegiatan.bpmpkaltim.id/uploads/

echo.

echo [36m[INFO][0m Testing API endpoints...
curl -s -o nul -w "Upload API Status: %%{http_code}" https://kegiatan.bpmpkaltim.id/api/absensi/upload-photo

echo.
echo.

echo ===============================================
echo    DEPLOYMENT COMPLETED
echo ===============================================
echo.

echo [32m✅ SUMMARY:[0m
echo   1. Deployment script uploaded to server
echo   2. File permissions fixed (755 dirs, 644 files)
echo   3. Nginx configuration updated (no-cache uploads)
echo   4. Services restarted
echo   5. Basic connectivity tested
echo.

echo [36m🔍 NEXT STEPS:[0m
echo   1. Test photo upload through the web interface
echo   2. Verify uploaded photos are immediately accessible
echo   3. Monitor server for any issues
echo.

echo [36m📋 FILES ON SERVER:[0m
echo   - /tmp/execute-deployment-server.sh (deployment script)
echo   - /tmp/monitor-photo-upload.sh (monitoring script)
echo   - /tmp/photo-upload-backup-* (backup directory)
echo.

echo [33m💡 TROUBLESHOOTING:[0m
echo   If photos still return 404:
echo   1. SSH to server: ssh %VPS_USER%@%VPS_HOST%
echo   2. Run monitor: bash /tmp/monitor-photo-upload.sh
echo   3. Check nginx: sudo systemctl status nginx
echo   4. Restart if needed: sudo systemctl restart nginx
echo.

echo Deployment completed at: %date% %time%
pause
