@echo off
cls
color 0A
echo =========================================================
echo    CLOUDPANEL REALTIME PHOTO - REMOTE MONITORING
echo =========================================================
echo.

:: Set variables (ganti dengan detail VPS Anda)
set VPS_IP=YOUR_VPS_IP
set VPS_USER=root

echo [INFO] Remote Monitoring for CloudPanel Real-time Photo Fix
echo [INFO] Target VPS: %VPS_IP%
echo.

echo =========================================================
echo                    MONITORING OPTIONS
echo =========================================================
echo.
echo 1. Quick Status Check
echo 2. Continuous Monitoring (auto-refresh every 10s)
echo 3. Real-time Test (test upload functionality)
echo 4. View Recent Logs
echo 5. Service Management
echo 6. Exit
echo.

set /p CHOICE="Select option (1-6): "

if "%CHOICE%"=="1" goto quick_status
if "%CHOICE%"=="2" goto continuous_monitor
if "%CHOICE%"=="3" goto realtime_test
if "%CHOICE%"=="4" goto view_logs
if "%CHOICE%"=="5" goto service_management
if "%CHOICE%"=="6" goto exit
goto invalid_choice

:quick_status
echo.
echo [INFO] Running quick status check...
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "curl -sSL https://raw.githubusercontent.com/user/repo/main/monitor-realtime-photo-status.sh | bash -s -- --single"
goto menu_return

:continuous_monitor
echo.
echo [INFO] Starting continuous monitoring...
echo [INFO] Press Ctrl+C to stop
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "curl -sSL https://raw.githubusercontent.com/user/repo/main/monitor-realtime-photo-status.sh | bash -s -- --continuous"
goto menu_return

:realtime_test
echo.
echo [INFO] Running real-time functionality test...
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "curl -sSL https://raw.githubusercontent.com/user/repo/main/monitor-realtime-photo-status.sh | bash -s -- --test"
goto menu_return

:view_logs
echo.
echo [INFO] Viewing recent monitoring logs...
echo.
echo ========== Real-time Monitor Log ==========
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "tail -20 /var/log/realtime-photo-monitor.log 2>/dev/null || echo 'Log not found'"
echo.
echo ========== Nginx Photo Access Log ==========
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "tail -10 /var/log/nginx/photo_realtime.log 2>/dev/null || echo 'Log not found'"
goto menu_return

:service_management
echo.
echo =========================================================
echo                  SERVICE MANAGEMENT
echo =========================================================
echo.
echo 1. Check service status
echo 2. Restart real-time monitor
echo 3. Restart nginx
echo 4. Check nginx configuration
echo 5. View service logs
echo 6. Back to main menu
echo.

set /p SERVICE_CHOICE="Select service option (1-6): "

if "%SERVICE_CHOICE%"=="1" (
    echo.
    echo [INFO] Checking service status...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "systemctl status realtime-photo-monitor nginx --no-pager"
    goto service_management
)

if "%SERVICE_CHOICE%"=="2" (
    echo.
    echo [INFO] Restarting real-time monitor...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "systemctl restart realtime-photo-monitor && echo 'Service restarted successfully'"
    goto service_management
)

if "%SERVICE_CHOICE%"=="3" (
    echo.
    echo [INFO] Restarting nginx...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "systemctl restart nginx && echo 'Nginx restarted successfully'"
    goto service_management
)

if "%SERVICE_CHOICE%"=="4" (
    echo.
    echo [INFO] Checking nginx configuration...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "nginx -t"
    goto service_management
)

if "%SERVICE_CHOICE%"=="5" (
    echo.
    echo [INFO] Viewing service logs...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "journalctl -u realtime-photo-monitor -n 20 --no-pager"
    goto service_management
)

if "%SERVICE_CHOICE%"=="6" goto main_menu

goto service_management

:invalid_choice
echo.
echo [ERROR] Invalid choice. Please select 1-6.
timeout /t 2 >nul
goto main_menu

:menu_return
echo.
echo =========================================================
set /p CONTINUE="Return to main menu? (Y/N): "
if /i "%CONTINUE%"=="Y" goto main_menu
goto exit

:main_menu
cls
goto start

:exit
echo.
echo [INFO] Monitoring session ended.
echo.
echo Useful manual commands for your VPS:
echo.
echo # Check real-time monitor status
echo ssh %VPS_USER%@%VPS_IP% "systemctl status realtime-photo-monitor"
echo.
echo # View live monitoring logs
echo ssh %VPS_USER%@%VPS_IP% "tail -f /var/log/realtime-photo-monitor.log"
echo.
echo # Test photo access
echo ssh %VPS_USER%@%VPS_IP% "curl -I http://localhost/uploads/path/to/photo.jpg"
echo.
pause
exit /b 0

:start
goto main_menu
