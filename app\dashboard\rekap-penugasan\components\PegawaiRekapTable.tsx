'use client';

import Link from 'next/link';
import React, { useState } from 'react';
import { formatDate } from '../../../../utils/helpers';

interface Assignment {
  pelatihanId: string;
  namaPelatihan: string;
  tempat: string;
  tglMulai: Date;
  tglBerakhir: Date;
  jabatanPanitia: string;
  lokasiTugas: string;
  gmName: string;
  gmRole: string;
}

interface PegawaiData {
  id: string;
  nama: string;
  nip: string;
  jabatan: string;
  assignments: Assignment[];
}

interface PegawaiRekapTableProps {
  data: PegawaiData[];
}

const PegawaiRekapTable: React.FC<PegawaiRekapTableProps> = ({ data }) => {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  
  // Format jabatan panitia untuk tampilan yang lebih baik
  const formatJabatan = (jabatan: string): string => {
    return jabatan.replace('_', ' ').replace(/(^\w|\s\w)/g, m => m.toUpperCase());
  };
  
  // Format nama role GM
  const formatGMRole = (role: string): string => {
    if (role.startsWith('GM')) {
      return `Gugus Mutu ${role.slice(2)}`;
    }
    return role;
  };

  // Toggle expand/collapse row
  const toggleRow = (pegawaiId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [pegawaiId]: !prev[pegawaiId]
    }));
  };
  
  // Calculate total assignments per pegawai
  const calculateTotalAssignments = (assignments: Assignment[]) => {
    return assignments.length;
  };

  return (
    <div className="overflow-hidden bg-white rounded-lg shadow">
      {data.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Pegawai
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  NIP
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Jabatan
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Total Penugasan
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Detail
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map(pegawai => (
                <React.Fragment key={pegawai.id}>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {pegawai.nama}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {pegawai.nip}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {pegawai.jabatan}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-gray-900">
                        {calculateTotalAssignments(pegawai.assignments)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button 
                        onClick={() => toggleRow(pegawai.id)} 
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        {expandedRows[pegawai.id] ? 'Sembunyikan' : 'Lihat Detail'}
                      </button>
                    </td>
                  </tr>
                  {expandedRows[pegawai.id] && (
                    <tr className="bg-gray-50">
                      <td colSpan={5} className="px-6 py-4">
                        <div className="mb-3 text-sm font-semibold">
                          Detail Penugasan {pegawai.nama}
                        </div>
                        <div className="overflow-x-auto">
                          <table className="min-w-full border divide-y divide-gray-200">
                            <thead className="bg-gray-100">
                              <tr>
                                <th scope="col" className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500">
                                  Nama Kegiatan
                                </th>
                                <th scope="col" className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500">
                                  Tempat
                                </th>
                                <th scope="col" className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500">
                                  Tanggal
                                </th>
                                <th scope="col" className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500">
                                  Lokasi Tugas
                                </th>
                                <th scope="col" className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500">
                                  Jabatan Panitia
                                </th>
                                <th scope="col" className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500">
                                  Dibuat Oleh
                                </th>
                              </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                              {pegawai.assignments.map((assignment, idx) => (
                                <tr key={`${pegawai.id}-${idx}`} className="hover:bg-gray-100">
                                  <td className="px-4 py-3">
                                    <Link 
                                      href={`/dashboard/pelatihan/${assignment.pelatihanId}`} 
                                      className="text-sm font-medium text-blue-600 hover:underline"
                                    >
                                      {assignment.namaPelatihan}
                                    </Link>
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-500">
                                    {assignment.tempat}
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-500">
                                    {formatDate(assignment.tglMulai)} - {formatDate(assignment.tglBerakhir)}
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-500">
                                    {assignment.lokasiTugas}
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-500">
                                    {formatJabatan(assignment.jabatanPanitia)}
                                  </td>
                                  <td className="px-4 py-3 text-sm text-gray-500">
                                    {assignment.gmName} ({formatGMRole(assignment.gmRole)})
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="p-6 text-center text-gray-500">
          Tidak ada data penugasan pegawai yang ditemukan
        </div>
      )}
    </div>
  );
};

export default PegawaiRekapTable;