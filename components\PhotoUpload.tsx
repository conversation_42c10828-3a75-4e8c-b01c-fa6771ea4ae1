// components/PhotoUpload.tsx
'use client';

import React, { useState, useRef } from 'react';
import { Camera, Upload, X, Loader2 } from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';

interface PhotoUploadProps {
  onPhotoUploaded?: (photoData: {
    public_id: string;
    secure_url: string;
    url: string;
  }) => void;
  currentPhotoUrl?: string;
  folder?: string;
  publicId?: string;
  className?: string;
  acceptedFormats?: string[];
  maxSizeMB?: number;
  width?: number;
  height?: number;
  label?: string;
  required?: boolean;
}

export default function PhotoUpload({
  onPhotoUploaded,
  currentPhotoUrl,
  folder = 'pelatihan',
  publicId,
  className = '',
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  maxSizeMB = 5,
  width = 200,
  height = 200,
  label = 'Upload Foto',
  required = false
}: PhotoUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>(currentPhotoUrl || '');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validasi tipe file
    if (!acceptedFormats.includes(file.type)) {
      toast.error(`Tipe file tidak didukung. Gunakan: ${acceptedFormats.join(', ')}`);
      return;
    }

    // Validasi ukuran file
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      toast.error(`Ukuran file maksimal ${maxSizeMB}MB`);
      return;
    }

    // Preview image
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload to Cloudinary
    await uploadPhoto(file);
  };  const uploadPhoto = async (file: File) => {
    setUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', folder);
      if (publicId) {
        formData.append('publicId', publicId);
      }

      // Try ImageKit first (Primary)
      let response = await fetch('/api/upload-imagekit', {
        method: 'POST',
        body: formData,
      });

      let result = await response.json();

      // If ImageKit fails, try Cloudinary as backup
      if (!response.ok || !result.success) {
        console.log('ImageKit upload failed, trying Cloudinary backup...');
        toast.error('ImageKit gagal, mencoba Cloudinary...');
        
        response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        result = await response.json();
        
        // If Cloudinary also fails, try local upload as final backup
        if (!response.ok || !result.success) {
          console.log('Cloudinary upload failed, trying local backup...');
          toast.error('Cloud upload gagal, mencoba upload lokal...');
          
          response = await fetch('/api/upload-local', {
            method: 'POST',
            body: formData,
          });

          result = await response.json();
          
          if (!response.ok || !result.success) {
            throw new Error(result.message || 'Gagal mengupload foto');
          }
          
          toast.success('Foto berhasil diupload (backup lokal)');
        } else {
          toast.success('Foto berhasil diupload (Cloudinary backup)');
        }
      } else {
        toast.success('Foto berhasil diupload (ImageKit)');
      }
      
      // Callback dengan data foto
      if (onPhotoUploaded) {
        onPhotoUploaded({
          public_id: result.data.public_id,
          secure_url: result.data.secure_url,
          url: result.data.url,
        });
      }

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.message || 'Gagal mengupload foto');
      // Reset preview jika upload gagal
      setPreviewUrl(currentPhotoUrl || '');
    } finally {
      setUploading(false);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemovePhoto = () => {
    setPreviewUrl('');
    if (onPhotoUploaded) {
      onPhotoUploaded({
        public_id: '',
        secure_url: '',
        url: '',
      });
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <div className="flex flex-col items-center space-y-4">
        {/* Preview Area */}
        <div 
          className="relative border-2 border-dashed border-gray-300 rounded-lg overflow-hidden bg-gray-50 hover:bg-gray-100 transition-colors"
          style={{ width: width, height: height }}
        >
          {previewUrl ? (
            <>
              <Image
                src={previewUrl}
                alt="Preview"
                fill
                className="object-cover"
                sizes={`${width}px`}
              />
              {!uploading && (
                <button
                  onClick={handleRemovePhoto}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                  type="button"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
              {uploading && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-white animate-spin" />
                </div>
              )}
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <Camera className="w-8 h-8 mb-2" />
              <span className="text-sm">No Photo</span>
            </div>
          )}
        </div>

        {/* Upload Button */}
        <button
          onClick={handleButtonClick}
          disabled={uploading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          type="button"
        >
          {uploading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Uploading...</span>
            </>
          ) : (
            <>
              <Upload className="w-4 h-4" />
              <span>{previewUrl ? 'Ganti Foto' : 'Upload Foto'}</span>
            </>
          )}
        </button>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={uploading}
        />
      </div>
      
      {/* Info Text */}
      <p className="text-xs text-gray-500 text-center">
        Format: {acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')} | 
        Max: {maxSizeMB}MB
      </p>
    </div>
  );
}
