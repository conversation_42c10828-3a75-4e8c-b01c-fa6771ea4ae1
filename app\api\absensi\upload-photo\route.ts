'use server';

import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir, chmod } from 'fs/promises';
import { join } from 'path';
import { randomUUID } from 'crypto';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const pelatihanId = formData.get('pelatihanId') as string;
    const file = formData.get('file') as File;
    
    if (!file || !pelatihanId) {
      return NextResponse.json(
        { error: 'File atau pelatihanId tidak ditemukan' },
        { status: 400 }
      );
    }

    // Validasi tipe file
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'File harus berupa gambar' },
        { status: 400 }
      );
    }

    // Validasi ukuran file (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Ukuran file tidak boleh lebih dari 5MB' },
        { status: 400 }
      );
    }

    // Buat nama file unik dengan timestamp untuk absensi
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `absensi-${timestamp}-${randomUUID()}.jpg`;
    
    // Buat direktori untuk menyimpan file absensi
    const baseDir = join(process.cwd(), 'public', 'uploads', 'absensi');
    const photosDir = join(baseDir, 'photos');
    const dirPath = join(photosDir, pelatihanId);
    
    try {
      // Buat direktori secara bertahap untuk memastikan struktur folder lengkap
      await mkdir(baseDir, { recursive: true });
      await mkdir(photosDir, { recursive: true });
      await mkdir(dirPath, { recursive: true });
      
    } catch (_error) {
      return NextResponse.json(
        { error: 'Gagal membuat direktori penyimpanan' },
        { status: 500 }
      );
    }

    // Path lengkap file
    const filePath = join(dirPath, fileName);
    
    // Simpan file dengan optimasi gambar khusus untuk foto absensi
    try {
      // Convert File to Buffer
      const buffer = Buffer.from(await file.arrayBuffer());
      
      // Optimize image using sharp - ukuran lebih kecil untuk foto absensi
      const optimizedImageBuffer = await sharp(buffer)
        .resize({
          width: 400,
          height: 400,
          fit: 'inside', // Maintain aspect ratio
          withoutEnlargement: true // Don't enlarge if image is smaller
        })
        .jpeg({ quality: 75 }) // Convert to JPEG with 75% quality untuk menghemat space
        .toBuffer();      // Write optimized image to file
      await writeFile(filePath, optimizedImageBuffer);
      
      // Set proper file permissions (644 - rw-r--r--)
      await chmod(filePath, 0o644);
    } catch (_error) {
      return NextResponse.json(
        { error: 'Gagal mengoptimasi dan menyimpan foto absensi' },
        { status: 500 }
      );
    }
    
    // Path relatif untuk disimpan di database
    const relativePath = `/uploads/absensi/photos/${pelatihanId}/${fileName}`;

    return NextResponse.json({
      success: true,
      filePath: relativePath,
      fileUrl: relativePath
    });
  } catch (_error) {
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengunggah foto absensi' },
      { status: 500 }
    );
  }
}
