@echo off
cls
color 0B
echo =========================================================
echo    CLOUDPANEL REALTIME PHOTO FIX - WINDOWS DEPLOYER
echo =========================================================
echo.

:: Set variables (ganti dengan detail VPS Anda)
set VPS_IP=YOUR_VPS_IP
set VPS_USER=root
set VPS_PASSWORD=YOUR_VPS_PASSWORD
set LOCAL_SCRIPT=cloudpanel-realtime-photo-fix.sh

:: Check if script exists
if not exist "%LOCAL_SCRIPT%" (
    echo [ERROR] Script %LOCAL_SCRIPT% not found!
    echo Make sure the script is in the same directory as this batch file.
    pause
    exit /b 1
)

echo [INFO] CloudPanel Real-time Photo Fix Deployer
echo [INFO] Target VPS: %VPS_IP%
echo [INFO] Script: %LOCAL_SCRIPT%
echo.

set /p CONFIRM="Continue with deployment? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo =========================================================
echo                    DEPLOYMENT STEPS
echo =========================================================
echo.

:: Step 1: Upload script
echo [STEP 1] Uploading script to VPS...
scp -o StrictHostKeyChecking=no "%LOCAL_SCRIPT%" %VPS_USER%@%VPS_IP%:/tmp/
if errorlevel 1 (
    echo [ERROR] Failed to upload script
    echo.
    echo Manual upload method:
    echo 1. Open WinSCP or FileZilla
    echo 2. Connect to %VPS_IP%
    echo 3. Upload %LOCAL_SCRIPT% to /tmp/
    echo 4. Then run: chmod +x /tmp/%LOCAL_SCRIPT% ^&^& /tmp/%LOCAL_SCRIPT%
    pause
    exit /b 1
)
echo [SUCCESS] Script uploaded successfully

:: Step 2: Make executable and run
echo.
echo [STEP 2] Making script executable and running...
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "chmod +x /tmp/%LOCAL_SCRIPT% && /tmp/%LOCAL_SCRIPT%"
if errorlevel 1 (
    echo [ERROR] Failed to execute script
    echo.
    echo Manual execution method:
    echo 1. SSH to your VPS: ssh %VPS_USER%@%VPS_IP%
    echo 2. Run: chmod +x /tmp/%LOCAL_SCRIPT%
    echo 3. Run: /tmp/%LOCAL_SCRIPT%
    pause
    exit /b 1
)

echo.
echo =========================================================
echo                  DEPLOYMENT COMPLETED!
echo =========================================================
echo.
echo [SUCCESS] Real-time photo fix has been deployed
echo [SUCCESS] Photos should now display immediately without nginx reload
echo.
echo Next steps:
echo 1. Test photo upload functionality
echo 2. Monitor real-time service: systemctl status realtime-photo-monitor
echo 3. Check logs: tail -f /var/log/realtime-photo-monitor.log
echo.

:: Option to monitor immediately
set /p MONITOR="Open monitoring dashboard? (Y/N): "
if /i "%MONITOR%"=="Y" (
    echo.
    echo Opening monitoring dashboard...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "bash -c 'curl -sSL https://raw.githubusercontent.com/your-repo/monitor-script.sh | bash'"
)

echo.
echo =========================================================
echo                    IMPORTANT NOTES
echo =========================================================
echo.
echo * Real-time monitoring service is now active
echo * No more manual nginx reloads needed
echo * Photo uploads are processed automatically
echo * System optimized for CloudPanel Ubuntu 24.04
echo.
echo For troubleshooting, run on VPS:
echo   systemctl status realtime-photo-monitor
echo   tail -f /var/log/realtime-photo-monitor.log
echo   tail -f /var/log/nginx/photo_realtime.log
echo.
pause
