'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { submitAbsensi } from '../../action';
import SignatureCanvas from '../../../../../components/SignatureCanvas';
import PhotoCapture from '../../../../../components/PhotoCapture';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';

// Dynamic import untuk Simple GPS Picker (tanpa geofencing)
const InternalGpsComponent = dynamic(
  () => import('../../../../../components/SimpleMapPicker'),
  { 
    ssr: false,
    loading: () => <div className="h-[300px] bg-gray-100 animate-pulse flex items-center justify-center">Memuat peta...</div>
  }
);

// Import shadcn/ui components
import { Input } from '../../../../../components/ui/input';
import { Label } from '../../../../../components/ui/label';
import { Button } from '../../../../../components/ui/button';
import { formatIndonesiaDate } from '../../../../../utils/dateUtils';

// Definisikan interface untuk data pelatihan
interface PelatihanData {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: string | Date;
  tgl_berakhir: string | Date;
  link_absensi_internal: string;
}

export default function AbsensiInternalPage() {
  const params = useParams<{ link: string }>();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [pelatihan, setPelatihan] = useState<PelatihanData | null>(null);
  const [error, setError] = useState<string | null>(null);  const [formData, setFormData] = useState({
    nama: '',
    nip_nik: '',
    jabatan: '',
    unit_kerja: '',
    no_hp: '',
    tanda_tangan: '',
    latitude: '',
    longitude: '',
    alamat: '',
    foto_cloudinary_id: '',
    foto_url: ''
  });
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);  const [gpsValidation, setGpsValidation] = useState<{
    isValid: boolean;
    riskScore: number;
    warnings: string[];
    venueMatch?: string;
    analysis?: any; // Detailed analysis from InternalGpsPicker
    serverValidation?: any; // Server-side validation results
  } | null>(null);

  useEffect(() => {
    async function verifyLink() {
      try {
        const response = await fetch(`/api/verify-absensi-internal?link=${params.link}`);
        const data = await response.json();

        if (data.valid) {
          setPelatihan(data.pelatihan);
          setIsLoading(false);
        } else {
          setError(data.message);        setIsLoading(false);
        }
      } catch (_err) {
        setError('Terjadi kesalahan saat memverifikasi link');
        setIsLoading(false);
      }
    }

    if (params.link) {
      verifyLink();
    }
  }, [params.link]);

  const validatePhoneNumber = (phone: string): boolean => {
    const pattern = /^(\+62|62|0)8[1-9][0-9]{6,10}$/;
    return pattern.test(phone);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    // Real-time validation
    if (name === 'no_hp' && value) {
      if (!validatePhoneNumber(value)) {
        setFieldErrors(prev => ({ 
          ...prev, 
          [name]: 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx'
        }));
      } else {
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
  };

  const handleSignatureChange = (dataUrl: string) => {
    setFormData((prev) => ({ ...prev, tanda_tangan: dataUrl }));
    
    // Clear signature error when signature is provided
    if (dataUrl) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.tanda_tangan;
        return newErrors;
      });
    }
  };  const handleLocationSelect = (lat: number, lng: number, address: string, validation: any) => {
    setFormData((prev) => ({
      ...prev,
      latitude: lat.toString(),
      longitude: lng.toString(),
      alamat: address
    }));
    
    // Update GPS validation state
    if (validation) {
      setGpsValidation({
        isValid: validation.isValid,
        riskScore: validation.riskScore,
        warnings: validation.warnings,
        venueMatch: undefined // No geofencing, so no venue matching
      });
    }
    
    // Clear location error when location is provided
    if (lat && lng) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.location;
        return newErrors;
      });
    }
  };  const handlePhotoCapture = async (photoDataUrl: string) => {
    if (!photoDataUrl) {
      setFormData(prev => ({
        ...prev,
        foto_cloudinary_id: '',
        foto_url: ''
      }));
      return;
    }

    try {
      // Convert dataURL to blob
      const response = await fetch(photoDataUrl);
      const blob = await response.blob();
      
      // Create file from blob
      const file = new File([blob], `absensi-${Date.now()}.jpg`, { type: 'image/jpeg' });
      
      // Upload to Cloudinary using the same system as PhotoUpload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'absensi');

      // Try ImageKit first (Primary)
      let uploadResponse = await fetch('/api/upload-imagekit', {
        method: 'POST',
        body: formData,
      });

      let result = await uploadResponse.json();

      // If ImageKit fails, try Cloudinary as backup
      if (!uploadResponse.ok || !result.success) {
        console.log('ImageKit upload failed, trying Cloudinary backup...');
        toast.error('ImageKit gagal, mencoba Cloudinary...');
        
        uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        result = await uploadResponse.json();
        
        // If Cloudinary also fails, try local upload as final backup
        if (!uploadResponse.ok || !result.success) {
          console.log('Cloudinary upload failed, trying local backup...');
          toast.error('Cloud upload gagal, mencoba upload lokal...');
          
          uploadResponse = await fetch('/api/upload-local', {
            method: 'POST',
            body: formData,
          });

          result = await uploadResponse.json();
          
          if (!uploadResponse.ok || !result.success) {
            throw new Error(result.message || 'Gagal mengupload foto');
          }
          
          toast.success('Foto berhasil diupload (backup lokal)');
        } else {
          toast.success('Foto berhasil diupload (Cloudinary backup)');
        }
      } else {
        toast.success('Foto berhasil diupload (ImageKit)');
      }

      // Update form data dengan hasil upload
      setFormData(prev => ({
        ...prev,
        foto_cloudinary_id: result.data.public_id,
        foto_url: result.data.secure_url
      }));
      
      // Clear photo error when photo is uploaded
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.foto;
        return newErrors;
      });
      
    } catch (error) {
      console.error('Error uploading photo:', error);
      toast.error('Gagal mengupload foto ke cloud');
      
      // Fallback: simpan sebagai base64 untuk kompatibilitas
      setFormData(prev => ({
        ...prev,
        foto_cloudinary_id: '',
        foto_url: photoDataUrl // Simpan base64 sebagai fallback
      }));
    }
  };const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.nama) {
      errors.nama = 'Nama lengkap harus diisi';
    }
    
    if (!formData.unit_kerja) {
      errors.unit_kerja = 'Unit kerja harus diisi';
    }
    
    if (!formData.no_hp) {
      errors.no_hp = 'Nomor HP harus diisi';
    } else if (!validatePhoneNumber(formData.no_hp)) {
      errors.no_hp = 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx';
    }
    
    if (!formData.tanda_tangan) {
      errors.tanda_tangan = 'Tanda tangan harus diisi';
    }
    
    if (!formData.latitude || !formData.longitude) {
      errors.location = 'Lokasi harus dipilih';
    }
      // Validasi foto untuk peserta internal
    if (!formData.foto_cloudinary_id || !formData.foto_url) {
      errors.foto = 'Foto harus diambil untuk absensi';
    }
      // Validasi GPS Security (disederhanakan tanpa geofencing)
    if (gpsValidation && gpsValidation.riskScore > 80) {
      errors.location = 'Kualitas sinyal GPS terlalu rendah. Coba ambil lokasi lagi.';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      if (!pelatihan) return;      // Simplified GPS validation for any location
      if (formData.latitude && formData.longitude && gpsValidation) {
        // Basic validation - just check if coordinates are valid
        const lat = parseFloat(formData.latitude);
        const lng = parseFloat(formData.longitude);
        
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          toast.error('Koordinat GPS tidak valid. Silakan pilih lokasi lagi.');
          setIsSubmitting(false);
          return;
        }
        
        // Update gps_validation with simple validation
        setGpsValidation(prev => prev ? {
          ...prev,
          serverValidation: { isValid: true, riskScore: prev.riskScore, warnings: prev.warnings }
        } : {
          isValid: true,
          riskScore: 10,
          warnings: [],
          serverValidation: { isValid: true, riskScore: 10, warnings: [] }
        });
      }

      const submitData = {
        ...formData,
        pelatihanId: pelatihan.id,
        is_internal: true, // Flag untuk peserta internal
        gps_validation: gpsValidation // Include GPS validation data
      };
      
      const result = await submitAbsensi(submitData);
      
      if (result.success) {
        toast.success(result.message);
        router.push(`/public-pages/absensi/${params.link}/success`);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Submit error:', error);
      toast.error('Terjadi kesalahan saat menyimpan data');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 border-b-2 border-indigo-600 rounded-full animate-spin"></div>
          <p className="text-gray-600">Memuat halaman absensi...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-pink-100">
        <div className="max-w-md p-6 mx-auto text-center">
          <div className="mb-4 text-6xl text-red-500">⚠️</div>
          <h1 className="mb-2 text-2xl font-bold text-red-800">Link Tidak Valid</h1>
          <p className="mb-4 text-red-600">{error}</p>
          <Button 
            onClick={() => router.push('/')}
            className="bg-red-600 hover:bg-red-700"
          >
            Kembali ke Beranda
          </Button>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen px-3 py-4 bg-gradient-to-br from-blue-50 to-indigo-100 sm:py-8 sm:px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="p-4 mb-4 bg-white rounded-lg shadow-lg sm:p-6 sm:mb-6">
          <div className="text-center">
            <h1 className="mb-2 text-xl font-bold text-indigo-800 sm:text-2xl lg:text-3xl">
              Absensi Kegiatan
            </h1>
            <div className="p-3 mt-3 rounded-lg bg-indigo-50 sm:p-4 sm:mt-4">
              <h2 className="mb-2 text-base font-semibold text-indigo-700 sm:text-lg lg:text-xl">
                {pelatihan?.nama}
              </h2>
              <div className="space-y-1 text-xs text-indigo-600 sm:text-sm">
                <p><span className="font-medium">Tempat:</span> {pelatihan?.tempat}</p>
                <p><span className="font-medium">Tanggal:</span> {formatIndonesiaDate(pelatihan?.tgl_mulai)} - {formatIndonesiaDate(pelatihan?.tgl_berakhir)}</p>
                <div className="inline-block px-2 py-1 mt-2 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                  Peserta 
                </div>
              </div>
            </div>
          </div>
        </div>        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 bg-white rounded-lg shadow-lg sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
            {/* Nama Lengkap */}
            <div className="space-y-2">
              <Label htmlFor="nama" className="text-sm font-medium text-gray-700">
                Nama Lengkap *
              </Label>
              <Input
                id="nama"
                name="nama"
                value={formData.nama}
                onChange={handleChange}
                placeholder="Masukkan nama lengkap"
                className={`h-10 sm:h-auto ${fieldErrors.nama ? 'border-red-500' : ''}`}
              />
              {fieldErrors.nama && (
                <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.nama}</p>
              )}
            </div>

            {/* NIP/NIK */}
            <div className="space-y-2">
              <Label htmlFor="nip_nik" className="text-sm font-medium text-gray-700">
                NIP/NIK
              </Label>
              <Input
                id="nip_nik"
                name="nip_nik"
                value={formData.nip_nik}
                onChange={handleChange}
                placeholder="Masukkan NIP/NIK (opsional)"
                className="h-10 sm:h-auto"
              />
            </div>

            {/* Jabatan */}
            <div className="space-y-2">
              <Label htmlFor="jabatan" className="text-sm font-medium text-gray-700">
                Jabatan
              </Label>
              <Input
                id="jabatan"
                name="jabatan"
                value={formData.jabatan}
                onChange={handleChange}
                placeholder="Masukkan jabatan (opsional)"
                className="h-10 sm:h-auto"
              />
            </div>

            {/* Unit Kerja */}
            <div className="space-y-2">
              <Label htmlFor="unit_kerja" className="text-sm font-medium text-gray-700">
                Unit Kerja *
              </Label>
              <Input
                id="unit_kerja"
                name="unit_kerja"
                value={formData.unit_kerja}
                onChange={handleChange}
                placeholder="Masukkan unit kerja"
                className={`h-10 sm:h-auto ${fieldErrors.unit_kerja ? 'border-red-500' : ''}`}
              />
              {fieldErrors.unit_kerja && (
                <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.unit_kerja}</p>
              )}
            </div>

            {/* Nomor HP */}
            <div className="space-y-2 sm:col-span-2">
              <Label htmlFor="no_hp" className="text-sm font-medium text-gray-700">
                Nomor HP *
              </Label>
              <Input
                id="no_hp"
                name="no_hp"
                value={formData.no_hp}
                onChange={handleChange}
                placeholder="Contoh: 08123456789"
                className={`h-10 sm:h-auto ${fieldErrors.no_hp ? 'border-red-500' : ''}`}
              />
              {fieldErrors.no_hp && (
                <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.no_hp}</p>
              )}
            </div>
          </div>          {/* Tanda Tangan */}
          <div className="mt-4 space-y-2 sm:mt-6">
            <Label className="text-sm font-medium text-gray-700">
              Tanda Tangan *
            </Label>
            <div className="p-2 border rounded-lg sm:p-4">
              <SignatureCanvas onSignatureChange={handleSignatureChange} />
            </div>
            {fieldErrors.tanda_tangan && (
              <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.tanda_tangan}</p>
            )}
          </div>

          {/* Foto Kehadiran */}
          <div className="mt-4 space-y-2 sm:mt-6">
            <Label className="text-sm font-medium text-gray-700">
              Foto Kehadiran *
            </Label>
            <div className="p-3 border rounded-lg sm:p-4">
              <div className="mb-3 text-center sm:mb-4">                <p className="mb-2 text-xs text-gray-600 sm:text-sm">
                  Ambil foto langsung dengan kamera perangkat sebagai bukti kehadiran
                </p>
                <div className="inline-block px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
                  📷 Kamera Real-time + Lokasi GPS
                </div>
              </div>              <PhotoCapture 
                onPhotoCapture={handlePhotoCapture}
                isRequired={true}
              />{formData.foto_cloudinary_id && (
                <div className="p-3 mt-3 border border-green-200 rounded-lg bg-green-50">
                  <p className="text-xs text-green-700 sm:text-sm">
                    ✓ Foto berhasil diupload dan disimpan
                  </p>
                </div>
              )}
            </div>
            {fieldErrors.foto && (
              <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.foto}</p>
            )}
          </div>

          {/* Lokasi */}          <div className="mt-4 space-y-2 sm:mt-6">
            <Label className="text-sm font-medium text-gray-700">
              Pilih Lokasi Kehadiran *
            </Label>
            <div className="p-3 mb-3 border border-blue-200 rounded-lg bg-blue-50">
              <div className="flex items-center space-x-2">
                <div className="inline-block w-3 h-3 bg-blue-500 rounded-full"></div>
                <p className="text-xs font-medium text-blue-800 sm:text-sm">
                  Wajib menggunakan GPS asli perangkat
                </p>
              </div>
              <p className="mt-1 text-xs text-blue-700">
                Lokasi akan dideteksi secara otomatis menggunakan GPS perangkat Anda. Tidak bisa dipilih secara manual.
              </p>
            </div><div className="overflow-hidden border rounded-lg">
              <InternalGpsComponent 
                onLocationSelect={handleLocationSelect}
              />
            </div>{fieldErrors.location && (
              <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.location}</p>
            )}
              {/* GPS Status */}
            {gpsValidation && (
              <div className="p-3 mt-2 border border-green-200 rounded-lg bg-green-50">
                <div className="flex items-center mb-2 space-x-2">
                  <span className="inline-block w-3 h-3 bg-green-500 rounded-full"></span>
                  <p className="text-xs font-medium text-green-700 sm:text-sm">
                    Lokasi GPS Tersimpan
                  </p>
                </div>
                {gpsValidation.warnings.length > 0 && (
                  <ul className="space-y-1 text-xs text-green-600 list-disc list-inside">
                    {gpsValidation.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                )}
              </div>
            )}
            
            {formData.alamat && (
              <div className="p-3 mt-2 border border-green-200 rounded-lg bg-green-50">
                <p className="text-xs text-green-700 sm:text-sm">
                  <span className="font-medium">Lokasi terpilih:</span> {formData.alamat}
                </p>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-center mt-6 sm:mt-8">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full px-6 py-2 text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 sm:px-8 sm:py-3 sm:text-lg disabled:opacity-50 sm:w-auto"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-b-2 border-white rounded-full animate-spin"></div>
                  <span>Menyimpan...</span>
                </div>
              ) : (
                'Kirim Absensi'
              )}
            </Button>          </div>
        </form>
      </div>
    </div>
  );
}
