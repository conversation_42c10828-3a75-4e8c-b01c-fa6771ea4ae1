import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';

// Gunakan environment variable untuk secret key
const SECRET_KEY = process.env.JWT_SECRET;

// Validasi secret key
if (!SECRET_KEY || SECRET_KEY.length < 32) {
  console.error('JWT_SECRET tidak dikonfigurasi dengan benar. Harus minimal 32 karakter.');
  // Gunakan fallback key hanya untuk development
  if (process.env.NODE_ENV !== 'production') {
    console.warn('Menggunakan fallback secret key untuk development. JANGAN GUNAKAN DI PRODUCTION!');
  }
}

// Convert secret key ke Uint8Array
const secretKey = new TextEncoder().encode(
  SECRET_KEY || 'fallback_secret_key_minimum_32_characters_long_for_dev_only'
);

// Definisikan tipe untuk session data
export interface SessionData {
  userId: string;
  role?: string;
  [key: string]: any;
}

export async function encrypt(payload: SessionData) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h') // Token berlaku selama 24 jam
    .sign(secretKey);
}

export async function decrypt(token: string): Promise<SessionData | null> {
  try {
    const { payload } = await jwtVerify(token, secretKey);
    
    // Validasi payload
    if (typeof payload !== 'object' || payload === null) {
      return null;
    }
    
    // Pastikan userId ada dan bertipe string
    if (!payload.userId || typeof payload.userId !== 'string') {
      return null;
    }
    
    return payload as unknown as SessionData;
  } catch (error) {
    console.error('Failed to verify JWT:', error);
    return null;
  }
}

export async function createSession(userId: string, role: string) {
  // Buat session token
  const sessionToken = await encrypt({ userId, role });
  
  // Simpan di cookie
  const cookieStore = await cookies();
  cookieStore.set({
    name: 'session',
    value: sessionToken,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24 // 24 jam
  });
  
  return sessionToken;
}

export async function destroySession() {
  const cookieStore = await cookies();
  cookieStore.set({
    name: 'session',
    value: '',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: 0 // Hapus cookie
  });
}

// Tambahkan fungsi untuk menghapus session
export async function clearSession() {
  const cookieStore = await cookies();
  cookieStore.delete('session');
}

// Fungsi untuk memvalidasi token tanpa throw error
export async function validateToken(token: string): Promise<boolean> {
  try {
    const result = await decrypt(token);
    return !!result;
  } catch (_error) {
    return false;
  }
}

// Fungsi untuk me-refresh token yang hampir kadaluarsa
export async function refreshSessionIfNeeded(token: string): Promise<string | null> {
  try {
    // Decode token tanpa verifikasi untuk memeriksa waktu kadaluarsa
    const decoded = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    
    // Jika token akan kadaluarsa dalam 1 jam, refresh
    const expiryTime = decoded.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    if (expiryTime - currentTime < oneHour) {
      // Token akan kadaluarsa dalam 1 jam, refresh
      console.log('Token akan kadaluarsa, melakukan refresh');
      
      // Verifikasi token dulu untuk memastikan valid
      const session = await decrypt(token);
      if (!session) {
        return null;
      }
      
      // Buat token baru
      const newToken = await encrypt(session);
      
      // Update cookie
      const cookieStore = await cookies();
      cookieStore.set({
        name: 'session',
        value: newToken,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 60 * 60 * 24 // 24 jam
      });
      
      return newToken;
    }
    
    // Token masih valid dan belum mendekati kadaluarsa
    return token;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return null;
  }
}
