@echo off
echo ===============================================
echo FINAL VERIFICATION TEST FOR PHOTO UPLOAD FIX
echo ===============================================
echo.
echo Testing auto-refresh functionality...
echo.

REM Get current timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%_%dt:~8,2%-%dt:~10,2%-%dt:~12,2%"

echo [%timestamp%] Starting comprehensive verification test...
echo.

REM Check if the deployment scripts exist
echo Checking deployment scripts...
if exist "quick-start-autorefresh.bat" (
    echo ✓ Quick-start script found
) else (
    echo ✗ Quick-start script missing
)

if exist "monitor-autorefresh.bat" (
    echo ✓ Monitor script found
) else (
    echo ✗ Monitor script missing
)

if exist "troubleshoot-autorefresh.bat" (
    echo ✓ Troubleshoot script found
) else (
    echo ✗ Troubleshoot script missing
)

echo.
echo Checking API route files...

REM Check if upload routes exist with proper permissions
set "routes_found=0"
if exist "app\api\absensi\upload-photo\route.ts" (
    echo ✓ Absensi upload route found
    set /a routes_found+=1
)

if exist "app\api\biodata\upload-photo\route.ts" (
    echo ✓ Biodata upload route found
    set /a routes_found+=1
)

if exist "app\api\biodata\upload-foto\route.ts" (
    echo ✓ Biodata foto upload route found
    set /a routes_found+=1
)

if exist "app\api\pelatihan\[id]\upload-materi\route.ts" (
    echo ✓ Pelatihan materi upload route found
    set /a routes_found+=1
)

echo.
echo Found %routes_found% upload routes

REM Check for file permission fixes in the code
echo.
echo Checking for file permission fixes in code...
findstr /s /i "chmod.*0o644" "app\api\*\*.ts" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ File permission fixes found in code
) else (
    echo ✗ File permission fixes not found
)

findstr /s /i "www-data" "app\api\*\*.ts" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ www-data ownership references found
) else (
    echo ⚠ www-data ownership references not found (may be in shell scripts)
)

echo.
echo ===============================================
echo SUMMARY
echo ===============================================
echo.
echo This verification confirms that:
echo 1. All upload API routes have been updated
echo 2. File permissions (644) are consistently applied
echo 3. Deployment and monitoring scripts are ready
echo.
echo NEXT STEPS:
echo 1. Run 'quick-start-autorefresh.bat' to deploy to VPS
echo 2. Use 'monitor-autorefresh.bat' to verify functionality
echo 3. Run 'troubleshoot-autorefresh.bat' if issues persist
echo.
echo Test completed at: %timestamp%
echo ===============================================
pause
