'use client';
import Link from 'next/link';
import { useState } from 'react';

// Definisikan interface untuk props
interface UserTableActionsProps {
  viewHref: string;
  editHref: string; 
  onDelete: () => Promise<void>;
  showLabels?: boolean; // Optional prop to show text labels
}

export default function UserTableActions({ 
  viewHref, 
  editHref, 
  onDelete, 
  showLabels = true 
}: UserTableActionsProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent click event from bubbling
   
    if (window.confirm('Apakah Anda yakin ingin menghapus pengguna ini?')) {
      setIsDeleting(true);
      
      try {
        await onDelete();
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <div className="flex items-center justify-end space-x-1 sm:space-x-2 md:space-x-3">
      {/* Detail <PERSON> */}
      <Link 
        href={viewHref} 
        className="inline-flex items-center px-2 py-1 text-xs text-blue-600 transition-colors duration-200 rounded sm:text-sm hover:text-blue-900 hover:bg-blue-50"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        {showLabels && <span className="hidden ml-1 sm:inline">Detail</span>}
      </Link>
      
      {/* Edit Button */}
      <Link 
        href={editHref} 
        className="inline-flex items-center px-2 py-1 text-xs text-blue-600 transition-colors duration-200 rounded sm:text-sm hover:text-blue-900 hover:bg-blue-50"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        {showLabels && <span className="hidden ml-1 sm:inline">Edit</span>}
      </Link>
      
      {/* Delete Button */}
      <button
        onClick={handleDelete}
        disabled={isDeleting}
        className={`inline-flex items-center px-2 py-1 text-xs sm:text-sm text-red-600 hover:text-red-900 hover:bg-red-50 rounded transition-colors duration-200 ${
          isDeleting ? 'opacity-50 cursor-not-allowed' : ''
        }`}
      >
        {isDeleting ? (
          <>
            <svg className="animate-spin h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
            </svg>
            {showLabels && <span className="hidden sm:inline">Menghapus</span>}
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            {showLabels && <span className="hidden ml-1 sm:inline">Hapus</span>}
          </>
        )}
      </button>
    </div>
  );
}