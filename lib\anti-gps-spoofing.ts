// Anti-GPS Spoofing Library untuk Absensi Internal
// Khusus untuk user yang berada di luar kantor dengan lokasi berpindah-pindah
// Fokus pada deteksi GPS palsu tanpa batasan geofencing

export interface LocationReading {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  speed?: number;
  heading?: number;
  altitude?: number;
}

export interface SpoofingAnalysis {
  isGenuine: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  riskScore: number; // 0-100
  detectionFlags: {
    accuracyNormal: boolean;
    speedConsistent: boolean;
    coordinatePrecision: boolean;
    timestampValid: boolean;
    movementPattern: boolean;
    deviceConsistency: boolean;
  };
  warnings: string[];
  recommendations: string[];
}

export interface LocationHistory {
  readings: LocationReading[];
  maxReadings: number;
}

/**
 * Advanced GPS Spoofing Detection for Internal Attendance
 * Tidak menggunakan geofencing, fokus pada validasi keabsahan GPS
 */
export class AntiGpsSpoofing {
  private locationHistory: LocationHistory;

  constructor(maxHistorySize = 10) {
    this.locationHistory = {
      readings: [],
      maxReadings: maxHistorySize
    };
  }

  /**
   * Analyze current GPS reading for spoofing indicators
   */
  analyzeLocation(currentLocation: LocationReading): SpoofingAnalysis {
    const analysis: SpoofingAnalysis = {
      isGenuine: true,
      riskLevel: 'LOW',
      riskScore: 0,
      detectionFlags: {
        accuracyNormal: true,
        speedConsistent: true,
        coordinatePrecision: true,
        timestampValid: true,
        movementPattern: true,
        deviceConsistency: true
      },
      warnings: [],
      recommendations: []
    };

    let riskScore = 0;

    // 1. Accuracy Analysis (Sangat Penting)
    const accuracyCheck = this.checkAccuracy(currentLocation);
    analysis.detectionFlags.accuracyNormal = accuracyCheck.isNormal;
    riskScore += accuracyCheck.riskPoints;
    if (accuracyCheck.warning) analysis.warnings.push(accuracyCheck.warning);

    // 2. Coordinate Precision Analysis
    const precisionCheck = this.checkCoordinatePrecision(currentLocation);
    analysis.detectionFlags.coordinatePrecision = precisionCheck.isNormal;
    riskScore += precisionCheck.riskPoints;
    if (precisionCheck.warning) analysis.warnings.push(precisionCheck.warning);

    // 3. Timestamp Validation
    const timestampCheck = this.checkTimestamp(currentLocation);
    analysis.detectionFlags.timestampValid = timestampCheck.isValid;
    riskScore += timestampCheck.riskPoints;
    if (timestampCheck.warning) analysis.warnings.push(timestampCheck.warning);

    // 4. Movement Pattern Analysis (jika ada history)
    if (this.locationHistory.readings.length > 0) {
      const movementCheck = this.analyzeMovementPattern(currentLocation);
      analysis.detectionFlags.speedConsistent = movementCheck.speedNormal;
      analysis.detectionFlags.movementPattern = movementCheck.patternNormal;
      riskScore += movementCheck.riskPoints;
      if (movementCheck.warnings.length > 0) {
        analysis.warnings.push(...movementCheck.warnings);
      }
    }

    // 5. Device Consistency Check
    const deviceCheck = this.checkDeviceConsistency(currentLocation);
    analysis.detectionFlags.deviceConsistency = deviceCheck.isConsistent;
    riskScore += deviceCheck.riskPoints;
    if (deviceCheck.warning) analysis.warnings.push(deviceCheck.warning);

    // Calculate final risk score and level
    analysis.riskScore = Math.min(riskScore, 100);
    analysis.riskLevel = this.calculateRiskLevel(analysis.riskScore);
    analysis.isGenuine = analysis.riskScore < 70; // Threshold untuk GPS genuine

    // Add recommendations based on risk level
    analysis.recommendations = this.generateRecommendations(analysis);

    // Add to history
    this.addToHistory(currentLocation);

    return analysis;
  }

  /**
   * Check GPS accuracy for spoofing indicators
   */
  private checkAccuracy(location: LocationReading): { isNormal: boolean; riskPoints: number; warning?: string } {
    const accuracy = location.accuracy;

    // GPS palsu sering memiliki accuracy yang terlalu bagus (< 3m) atau terlalu buruk (> 100m)
    if (accuracy < 3) {
      return {
        isNormal: false,
        riskPoints: 30,
        warning: "GPS accuracy terlalu tinggi - kemungkinan GPS palsu"
      };
    }

    if (accuracy > 100) {
      return {
        isNormal: false,
        riskPoints: 25,
        warning: `GPS accuracy sangat rendah: ${accuracy.toFixed(1)}m`
      };
    }

    if (accuracy > 50) {
      return {
        isNormal: false,
        riskPoints: 15,
        warning: `GPS accuracy rendah: ${accuracy.toFixed(1)}m`
      };
    }

    return { isNormal: true, riskPoints: 0 };
  }

  /**
   * Check coordinate precision for artificial patterns
   */
  private checkCoordinatePrecision(location: LocationReading): { isNormal: boolean; riskPoints: number; warning?: string } {
    const lat = location.latitude;
    const lng = location.longitude;

    // Check decimal places
    const latDecimals = this.getDecimalPlaces(lat);
    const lngDecimals = this.getDecimalPlaces(lng);

    // GPS palsu sering menggunakan koordinat dengan decimal places yang tidak natural
    if (latDecimals < 4 || lngDecimals < 4) {
      return {
        isNormal: false,
        riskPoints: 20,
        warning: "Koordinat GPS tidak cukup presisi - kemungkinan dimanipulasi"
      };
    }

    // Check for rounded coordinates (suspicious)
    if (latDecimals === 1 || lngDecimals === 1) {
      return {
        isNormal: false,
        riskPoints: 35,
        warning: "Koordinat GPS terlalu bulat - kemungkinan GPS palsu"
      };
    }

    // Check for exact same coordinates as previous readings
    const recentReadings = this.locationHistory.readings.slice(-3);
    const exactMatches = recentReadings.filter(reading => 
      reading.latitude === lat && reading.longitude === lng
    ).length;

    if (exactMatches > 0) {
      return {
        isNormal: false,
        riskPoints: 15,
        warning: "Koordinat GPS tidak berubah - kemungkinan statis/palsu"
      };
    }

    return { isNormal: true, riskPoints: 0 };
  }

  /**
   * Validate timestamp for freshness and consistency
   */
  private checkTimestamp(location: LocationReading): { isValid: boolean; riskPoints: number; warning?: string } {
    const now = Date.now();
    const age = now - location.timestamp;

    // Data GPS harus fresh (kurang dari 1 menit)
    if (age > 60000) {
      return {
        isValid: false,
        riskPoints: 20,
        warning: `Data GPS terlalu lama: ${Math.round(age / 1000)} detik`
      };
    }

    // Data GPS tidak boleh dari masa depan
    if (age < -5000) {
      return {
        isValid: false,
        riskPoints: 30,
        warning: "Timestamp GPS dari masa depan - kemungkinan dimanipulasi"
      };
    }

    return { isValid: true, riskPoints: 0 };
  }

  /**
   * Analyze movement pattern for unrealistic changes
   */
  private analyzeMovementPattern(currentLocation: LocationReading): {
    speedNormal: boolean;
    patternNormal: boolean;
    riskPoints: number;
    warnings: string[];
  } {
    const warnings: string[] = [];
    let riskPoints = 0;
    let speedNormal = true;
    let patternNormal = true;

    const lastReading = this.locationHistory.readings[this.locationHistory.readings.length - 1];
    
    if (lastReading) {
      const timeDiff = (currentLocation.timestamp - lastReading.timestamp) / 1000; // seconds
      
      if (timeDiff > 0) {
        const distance = this.calculateDistance(
          lastReading.latitude, lastReading.longitude,
          currentLocation.latitude, currentLocation.longitude
        );
        
        const calculatedSpeed = distance / timeDiff; // m/s
        const speedKmh = calculatedSpeed * 3.6; // km/h

        // Check for teleportation (impossible speeds)
        if (speedKmh > 500) {
          speedNormal = false;
          riskPoints += 40;
          warnings.push(`Kecepatan tidak mungkin: ${speedKmh.toFixed(1)} km/h - kemungkinan teleportasi GPS`);
        } else if (speedKmh > 200) {
          speedNormal = false;
          riskPoints += 25;
          warnings.push(`Kecepatan sangat tinggi: ${speedKmh.toFixed(1)} km/h`);
        } else if (speedKmh > 120) {
          riskPoints += 10;
          warnings.push(`Kecepatan tinggi: ${speedKmh.toFixed(1)} km/h`);
        }

        // Check consistency with reported speed (if available)
        if (currentLocation.speed !== undefined) {
          const reportedSpeedKmh = currentLocation.speed * 3.6;
          const speedDifference = Math.abs(speedKmh - reportedSpeedKmh);
          
          if (speedDifference > 50) {
            patternNormal = false;
            riskPoints += 20;
            warnings.push("Kecepatan yang dilaporkan tidak konsisten dengan pergerakan");
          }
        }
      }
    }

    // Check movement pattern across multiple readings
    if (this.locationHistory.readings.length >= 3) {
      const erraticMovement = this.detectErraticMovement();
      if (erraticMovement.isErratic) {
        patternNormal = false;
        riskPoints += erraticMovement.riskPoints;
        warnings.push(erraticMovement.warning);
      }
    }

    return {
      speedNormal,
      patternNormal,
      riskPoints,
      warnings
    };
  }

  /**
   * Check device consistency indicators
   */
  private checkDeviceConsistency(location: LocationReading): { isConsistent: boolean; riskPoints: number; warning?: string } {
    // Check for altitude consistency (if available)
    if (location.altitude !== undefined) {
      const recentAltitudes = this.locationHistory.readings
        .filter(r => r.altitude !== undefined)
        .slice(-3)
        .map(r => r.altitude!);

      if (recentAltitudes.length > 0) {
        const altitudeJumps = recentAltitudes.map(alt => Math.abs(location.altitude! - alt));
        const maxJump = Math.max(...altitudeJumps);

        // Altitude jumps yang tidak wajar (lebih dari 200m dalam sekali baca)
        if (maxJump > 200) {
          return {
            isConsistent: false,
            riskPoints: 25,
            warning: `Perubahan altitude tidak wajar: ${maxJump.toFixed(0)}m`
          };
        }
      }
    }

    return { isConsistent: true, riskPoints: 0 };
  }

  /**
   * Detect erratic movement patterns
   */
  private detectErraticMovement(): { isErratic: boolean; riskPoints: number; warning: string } {
    const readings = this.locationHistory.readings.slice(-3);
    const speeds: number[] = [];

    for (let i = 1; i < readings.length; i++) {
      const timeDiff = (readings[i].timestamp - readings[i-1].timestamp) / 1000;
      if (timeDiff > 0) {
        const distance = this.calculateDistance(
          readings[i-1].latitude, readings[i-1].longitude,
          readings[i].latitude, readings[i].longitude
        );
        speeds.push((distance / timeDiff) * 3.6); // km/h
      }
    }

    if (speeds.length >= 2) {
      const speedVariation = Math.max(...speeds) - Math.min(...speeds);
      
      // Variasi kecepatan yang terlalu besar menunjukkan pola tidak wajar
      if (speedVariation > 100) {
        return {
          isErratic: true,
          riskPoints: 20,
          warning: `Pola pergerakan tidak konsisten: variasi kecepatan ${speedVariation.toFixed(1)} km/h`
        };
      }
    }

    return { isErratic: false, riskPoints: 0, warning: '' };
  }

  /**
   * Calculate risk level based on score
   */
  private calculateRiskLevel(score: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (score >= 80) return 'CRITICAL';
    if (score >= 60) return 'HIGH';
    if (score >= 30) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(analysis: SpoofingAnalysis): string[] {
    const recommendations: string[] = [];

    if (analysis.riskLevel === 'CRITICAL') {
      recommendations.push("🚫 GPS terdeteksi palsu - gunakan GPS asli perangkat");
      recommendations.push("📱 Matikan aplikasi pemalsuan lokasi (fake GPS apps)");
      recommendations.push("🔄 Restart GPS dan coba lagi");
    } else if (analysis.riskLevel === 'HIGH') {
      recommendations.push("⚠️ GPS mencurigakan - pastikan tidak menggunakan aplikasi fake GPS");
      recommendations.push("📍 Aktifkan 'High Accuracy' mode pada GPS");
    } else if (analysis.riskLevel === 'MEDIUM') {
      recommendations.push("🔍 Periksa pengaturan GPS perangkat");
      recommendations.push("📶 Pastikan sinyal GPS kuat");
    }

    if (!analysis.detectionFlags.accuracyNormal) {
      recommendations.push("📍 Pindah ke area dengan sinyal GPS lebih baik");
    }

    return recommendations;
  }

  /**
   * Utility functions
   */
  private getDecimalPlaces(num: number): number {
    const str = num.toString();
    const decimal = str.split('.')[1];
    return decimal ? decimal.length : 0;
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private addToHistory(location: LocationReading): void {
    this.locationHistory.readings.push(location);
    
    // Keep only the most recent readings
    if (this.locationHistory.readings.length > this.locationHistory.maxReadings) {
      this.locationHistory.readings.shift();
    }
  }

  /**
   * Get current location history for debugging
   */
  getLocationHistory(): LocationReading[] {
    return [...this.locationHistory.readings];
  }

  /**
   * Clear location history
   */
  clearHistory(): void {
    this.locationHistory.readings = [];
  }

  /**
   * Export analysis for server-side verification
   */
  exportAnalysisData(analysis: SpoofingAnalysis, location: LocationReading) {
    return {
      location,
      analysis,
      deviceInfo: {
        timestamp: Date.now(),
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        platform: typeof navigator !== 'undefined' ? navigator.platform : 'unknown'
      },
      locationHistory: this.locationHistory.readings
    };
  }
}

/**
 * Quick validation function for simple cases
 */
export function quickValidateGPS(location: LocationReading): {
  isValid: boolean;
  riskScore: number;
  message: string;
} {
  const validator = new AntiGpsSpoofing(5);
  const analysis = validator.analyzeLocation(location);
  
  return {
    isValid: analysis.isGenuine,
    riskScore: analysis.riskScore,
    message: analysis.warnings.length > 0 
      ? analysis.warnings[0] 
      : analysis.riskLevel === 'LOW' 
        ? "GPS terverifikasi asli" 
        : "GPS mencurigakan"
  };
}
