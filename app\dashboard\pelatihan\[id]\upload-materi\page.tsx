import { Metadata } from 'next';
import { getCurrentUser } from '../../../../../lib/auth';
import { redirect } from 'next/navigation';
import { prisma } from '../../../../../lib/prisma';
import MaterialUploader, { MaterialFile } from '../../../../../components/MaterialUploader';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Upload Materi Kegiatan - Dashboard',
};

export const dynamic = 'force-dynamic';

type Props = {
  params: Promise<{ id: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function UploadMateriPage({ params }: Props) {
  const { id } = await params;
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/login');
  }

  const pelatihan = await prisma.pelatihan.findUnique({
    where: { id },
    include: {
      materiPelatihan: true,
    },
  });

  if (!pelatihan) {
    redirect('/dashboard/pelatihan');
  }

  const isAdmin = user.role === 'ADMIN';
  if (!isAdmin && pelatihan.userId !== user.id) {
    redirect('/dashboard/pelatihan');
  }

  const materialFiles: MaterialFile[] = pelatihan.materiPelatihan.map(material => ({
    id: material.id,
    nama_file: material.nama_file,
    path_file: material.path_file,
    size: material.size,
    mime_type: material.mime_type,
    createdAt: material.createdAt.toISOString()
  }));

  return (
    <div className="container px-4 py-8 mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Upload Materi Pelatihan</h1>
        <Link
          href={`/dashboard/pelatihan/${id}`}
          className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900"
        >
          Kembali ke Detail Kegiatan
        </Link>
      </div>

      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="mb-2 text-lg font-medium text-gray-900">{pelatihan.nama}</h2>
        <p className="mb-6 text-sm text-gray-500">
          Upload materi kegiatan dalam bentuk file PDF (maksimal 2MB per file)
        </p>

        <MaterialUploader
          pelatihanId={id}
          initialFiles={materialFiles}
        />
      </div>
    </div>
  );
}









