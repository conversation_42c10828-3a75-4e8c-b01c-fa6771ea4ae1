// Definisi enum untuk user role
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  GM1 = 'GM1',
  GM2 = 'GM2',
  GM3 = 'GM3',
  GM4 = 'GM4',
  GM5 = 'GM5',
  KEPALA = 'KEPALA',
  KASUBAG = 'KASUBAG'
}

// Definisi enum untuk jenjang pelatihan
export enum PelatihanJenjang {
  PAUD = 'PAUD',
  SD = 'SD',
  SMP = 'SMP',
  SMA = 'SMA',
  UMUM = 'UMUM',
  DINAS = 'DINAS',
  BPMP = 'BPMP',
  LAINNYA = 'LAINNYA'
}

// Helper function untuk mengecek apakah string merupakan UserRole yang valid
export function isValidUserRole(role: string): role is UserRole {
  return Object.values(UserRole).includes(role as UserRole);
}

// Helper function untuk mengecek apakah string merupakan PelatihanJenjang yang valid
export function isValidPelatihanJenjang(jenjang: string): jenjang is PelatihanJenjang {
  return Object.values(PelatihanJenjang).includes(jenjang as <PERSON>elatihanJenjang);
}