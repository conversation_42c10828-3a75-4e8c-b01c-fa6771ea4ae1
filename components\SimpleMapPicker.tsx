'use client';

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import dynamic from 'next/dynamic';
import { MapPin, Target, CheckCircle, AlertTriangle } from 'lucide-react';

// Dynamic import for Leaflet components with loading component
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { 
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-full bg-gray-100">Loading map...</div>
});
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false });

interface SimpleMapPickerProps {
  onLocationSelect: (lat: number, lng: number, address: string, validation: SimpleValidation) => void;
  initialLat?: number;
  initialLng?: number;
  enableCache?: boolean;
  timeoutDuration?: number;
}

interface SimpleValidation {
  isValid: boolean;
  riskScore: number;
  warnings: string[];
}

interface Position {
  lat: number;
  lng: number;
}

// Constants
const DEFAULT_POSITION = { lat: -0.471852, lng: 117.157982 };
const DEFAULT_TIMEOUT = 6000;
const GPS_TIMEOUT = 10000;
const GPS_MAX_AGE = 30000;
const COORDINATE_PRECISION = 6;
const CACHE_PRECISION = 4;

// Custom hooks
const useGeocodeCache = (enableCache = true) => {
  const cache = useRef(new Map<string, string>());

  const getCacheKey = useCallback((lat: number, lng: number) => 
    `${lat.toFixed(CACHE_PRECISION)},${lng.toFixed(CACHE_PRECISION)}`, []);

  const getFromCache = useCallback((key: string) => {
    if (!enableCache) return null;
    
    // Try memory cache first
    if (cache.current.has(key)) {
      return cache.current.get(key) || null;
    }
    
    // Try session storage
    try {
      return sessionStorage.getItem(`geocode_${key}`);
    } catch {
      return null;
    }
  }, [enableCache]);

  const setToCache = useCallback((key: string, value: string) => {
    if (!enableCache) return;
    
    // Set to memory cache
    cache.current.set(key, value);
    
    // Set to session storage
    try {
      sessionStorage.setItem(`geocode_${key}`, value);
    } catch {
      // Ignore storage errors
    }
  }, [enableCache]);

  return { getCacheKey, getFromCache, setToCache };
};

const useLeafletIcon = () => {
  const [leafletIcon, setLeafletIcon] = useState<any>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    let mounted = true;

    import('leaflet').then(L => {
      if (!mounted) return;

      const icon = L.divIcon({
        className: 'custom-div-icon',
        html: `
          <div style="
            background-color: #3b82f6;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          "></div>
        `,
        iconSize: [20, 20],
        iconAnchor: [10, 10]
      });

      setLeafletIcon(icon);
    }).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to load Leaflet icon:', error);
      }
    });

    return () => { mounted = false; };
  }, []);

  return leafletIcon;
};

export default function SimpleMapPicker({
  onLocationSelect,
  initialLat = DEFAULT_POSITION.lat,
  initialLng = DEFAULT_POSITION.lng,
  enableCache = true,
  timeoutDuration = DEFAULT_TIMEOUT
}: SimpleMapPickerProps) {
  // State
  const [position, setPosition] = useState<Position | null>(null);
  const [addressInfo, setAddressInfo] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // Refs
  const onLocationSelectRef = useRef(onLocationSelect);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Custom hooks
  const { getCacheKey, getFromCache, setToCache } = useGeocodeCache(enableCache);
  const leafletIcon = useLeafletIcon();

  // Memoized values
  const initialPosition = useMemo(() => ({ lat: initialLat, lng: initialLng }), [initialLat, initialLng]);
  const mapCenter = useMemo(() => position || initialPosition, [position, initialPosition]);

  // Update ref when prop changes
  useEffect(() => {
    onLocationSelectRef.current = onLocationSelect;
  }, [onLocationSelect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Utility functions
  const createSimpleValidation = useCallback((accuracy?: number): SimpleValidation => {
    const riskScore = accuracy && accuracy > 50 ? 40 : 10;
    
    return {
      isValid: true,
      riskScore,
      warnings: accuracy && accuracy > 50 ? ['Akurasi GPS rendah'] : []
    };
  }, []);

  const createCoordinateAddress = useCallback((lat: number, lng: number, suffix = '') => 
    `Koordinat: ${lat.toFixed(COORDINATE_PRECISION)}, ${lng.toFixed(COORDINATE_PRECISION)}${suffix}`, []);

  // Improved geocoding with better error handling
  const fetchAddress = useCallback(async (lat: number, lng: number): Promise<string> => {
    const cacheKey = getCacheKey(lat, lng);
    
    // Check cache first
    const cached = getFromCache(cacheKey);
    if (cached) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using cached geocoding result');
      }
      return cached;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const controller = new AbortController();
    abortControllerRef.current = controller;
    
    const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);
    
    try {
      const response = await fetch(`/api/geocode?lat=${lat}&lng=${lng}`, {
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const address = data.address || 'Lokasi tidak dikenal';
      
      // Cache successful result
      setToCache(cacheKey, address);
      
      return address;
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        if (process.env.NODE_ENV === 'development') {
          console.log('Geocoding request cancelled or timed out');
        }
        return createCoordinateAddress(lat, lng, ' (Timeout)');
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.warn('Geocoding failed:', error.message || error);
      }
      
      return createCoordinateAddress(lat, lng);
    }
  }, [getCacheKey, getFromCache, setToCache, timeoutDuration, createCoordinateAddress]);

  // Main location selection handler
  const handleLocationSelect = useCallback(async (lat: number, lng: number, accuracy?: number) => {
    setIsLoading(true);
    setAddressInfo('Mengambil alamat...');
    setLocationError(null);
    
    try {
      // Set position immediately
      const newPosition = { lat, lng };
      setPosition(newPosition);
      
      // Fetch address
      let address = createCoordinateAddress(lat, lng);
      try {
        address = await fetchAddress(lat, lng);
      } catch (_addressError) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Address fetch failed, using coordinates fallback');
        }
      }
      
      setAddressInfo(address);
      
      // Create validation and notify parent
      const validation = createSimpleValidation(accuracy);
      onLocationSelectRef.current(lat, lng, address, validation);
      
    } catch (error) {
      const errorMessage = 'Gagal memproses lokasi yang dipilih';
      const fallbackAddress = createCoordinateAddress(lat, lng);
      
      console.warn('Error handling location selection:', error);
      setLocationError(errorMessage);
      setAddressInfo(fallbackAddress);
      
      // Still notify parent with fallback data
      const validation = createSimpleValidation(accuracy);
      onLocationSelectRef.current(lat, lng, fallbackAddress, validation);
    } finally {
      setIsLoading(false);
    }
  }, [fetchAddress, createSimpleValidation, createCoordinateAddress]);

  // GPS error handler
  const handleGeoLocationError = useCallback((error: GeolocationPositionError) => {
    let errorMessage = 'Tidak dapat mengakses lokasi';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'Akses lokasi ditolak. Mohon izinkan akses lokasi di browser.';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'Lokasi tidak tersedia. Pastikan GPS aktif.';
        break;
      case error.TIMEOUT:
        errorMessage = 'Waktu habis saat mengambil lokasi GPS. Coba lagi.';
        break;
    }
    
    console.error('GPS error:', error);
    setLocationError(errorMessage);
    setAddressInfo('');
    setIsLoading(false);
  }, []);

  // GPS location getter
  const getCurrentLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setLocationError('Browser tidak mendukung GPS');
      return;
    }

    setIsLoading(true);
    setLocationError(null);
    setAddressInfo('Mencari lokasi GPS...');

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        if (process.env.NODE_ENV === 'development') {
          console.log(`GPS location found: ${latitude}, ${longitude} (accuracy: ${accuracy}m)`);
        }
        handleLocationSelect(latitude, longitude, accuracy);
      },
      handleGeoLocationError,
      {
        enableHighAccuracy: true,
        timeout: GPS_TIMEOUT,
        maximumAge: GPS_MAX_AGE
      }
    );
  }, [handleLocationSelect, handleGeoLocationError]);

  // Auto-get location on mount (only once)
  useEffect(() => {
    if (!navigator.geolocation) {
      setLocationError('Browser tidak mendukung GPS');
      return;
    }

    getCurrentLocation();
  }, [getCurrentLocation]); // Include getCurrentLocation in dependency array

  // Memoized components
  const LocationStatus = useMemo(() => {
    if (!position) return null;

    return (
      <div className="p-3 mt-3 border border-green-200 rounded-lg bg-green-50">
        <div className="flex items-center mb-2">
          <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
          <span className="text-sm font-medium text-green-800">Lokasi GPS Berhasil Dideteksi</span>
        </div>
        <p className="text-sm text-green-700">
          {addressInfo || 'Mengambil alamat...'}
        </p>
        <p className="mt-1 text-xs text-green-600">
          Koordinat: {position.lat.toFixed(COORDINATE_PRECISION)}, {position.lng.toFixed(COORDINATE_PRECISION)}
        </p>
        {addressInfo.includes('Koordinat:') && (
          <p className="mt-1 text-xs text-yellow-600">
            Alamat tidak tersedia, menggunakan koordinat GPS
          </p>
        )}
      </div>
    );
  }, [position, addressInfo]);

  const ErrorDisplay = useMemo(() => {
    if (!locationError) return null;

    return (
      <div className="p-3 mt-3 border border-red-200 rounded-lg bg-red-50">
        <div className="flex items-center">
          <AlertTriangle className="w-4 h-4 mr-2 text-red-600" />
          <span className="text-sm font-medium text-red-800">Error</span>
        </div>
        <p className="mt-1 text-sm text-red-700">{locationError}</p>
      </div>
    );
  }, [locationError]);

  return (
    <div className="w-full">
      {/* Control Panel */}
      <div className="p-4 bg-white border-b">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <button
            onClick={getCurrentLocation}
            disabled={isLoading}
            className="flex items-center justify-center px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label={isLoading ? 'Sedang mengambil lokasi' : 'Gunakan lokasi GPS saya'}
          >
            <Target className="w-4 h-4 mr-2" />
            {isLoading ? 'Mengambil Lokasi...' : 'Gunakan Lokasi Saya'}
          </button>
          
          <div className="text-sm text-gray-600">
            <MapPin className="inline w-4 h-4 mr-1" />
            Gunakan tombol di atas untuk mengaktifkan GPS dan mendeteksi lokasi Anda
          </div>
        </div>

        {LocationStatus}
        {ErrorDisplay}
      </div>

      {/* Map */}
      <div className="relative h-[300px] sm:h-[400px]">
        {typeof window !== 'undefined' && (
          <MapContainer
            center={[mapCenter.lat, mapCenter.lng]}
            zoom={position ? 16 : 13}
            style={{ height: '100%', width: '100%' }}
            className="z-0"
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />
            
            {position && leafletIcon && (
              <Marker 
                position={[position.lat, position.lng]} 
                icon={leafletIcon}
              />
            )}
          </MapContainer>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 text-sm text-gray-600 bg-gray-50">
        <p className="mb-2 font-medium">Petunjuk:</p>
        <ul className="space-y-1 list-disc list-inside">
          <li>Klik tombol &quot;Gunakan Lokasi Saya&quot; untuk mengaktifkan GPS</li>
          <li>Pastikan GPS/lokasi sudah diaktifkan pada perangkat Anda</li>
          <li>Tunggu hingga lokasi berhasil terdeteksi</li>
          <li>Lokasi akan ditampilkan secara otomatis di peta</li>
        </ul>
      </div>
    </div>
  );
}