'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import dynamic from 'next/dynamic';
import { MapPin, Target, CheckCircle, AlertTriangle } from 'lucide-react';

// Dynamic import for Leaflet components
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { ssr: false });
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false });

interface SimpleMapPickerProps {
  onLocationSelect: (lat: number, lng: number, address: string, validation: any) => void;
  initialLat?: number;
  initialLng?: number;
}

// Simple validation result interface
interface SimpleValidation {
  isValid: boolean;
  riskScore: number;
  warnings: string[];
}

export default function SimpleMapPicker({
  onLocationSelect,
  initialLat = -0.471852,
  initialLng = 117.157982
}: SimpleMapPickerProps) {
  const [position, setPosition] = useState<{ lat: number; lng: number } | null>(null);  const [addressInfo, setAddressInfo] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [leafletIcon, setLeafletIcon] = useState<any>(null);

  // Use ref to store the latest callback to avoid dependency issues
  const onLocationSelectRef = useRef(onLocationSelect);
  
  // Update ref when prop changes
  useEffect(() => {
    onLocationSelectRef.current = onLocationSelect;
  }, [onLocationSelect]);

  // Load Leaflet icon asynchronously
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('leaflet').then(L => {
        const icon = L.divIcon({
          className: 'custom-div-icon',
          html: `
            <div style="
              background-color: #3b82f6;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              border: 3px solid white;
              box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            "></div>
          `,
          iconSize: [20, 20],
          iconAnchor: [10, 10]
        });

        setLeafletIcon(icon);
      }).catch(error => {
        console.error('Failed to load Leaflet icon:', error);
      });
    }
  }, []);  // Get address from coordinates using proxy API with timeout and client-side caching
  const fetchAddress = useCallback(async (lat: number, lng: number): Promise<string> => {
    // Simple client-side cache key with reduced precision
    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;
      // Check if we have this result in session storage (temporary cache)
    const cached = sessionStorage.getItem(`geocode_${cacheKey}`);
    if (cached) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using cached geocoding result');
      }
      return cached;
    }
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 6000); // Reduced to 6 seconds
    
    try {
      const response = await fetch(`/api/geocode?lat=${lat}&lng=${lng}`, {
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const address = data.address || 'Lokasi tidak dikenal';
      
      // Cache successful result for this session
      try {
        sessionStorage.setItem(`geocode_${cacheKey}`, address);      } catch (_e) {
        // Ignore storage errors
      }
      
      return address;
    } catch (error: any) {
      clearTimeout(timeoutId);
        // Handle AbortError (timeout) gracefully without logging as error
      if (error.name === 'AbortError') {
        if (process.env.NODE_ENV === 'development') {
          console.log('Geocoding request timed out, falling back to coordinates');
        }
        return `Koordinat: ${lat.toFixed(6)}, ${lng.toFixed(6)} (Timeout)`;
      }
      
      // Only log actual errors, not expected timeouts
      if (process.env.NODE_ENV === 'development') {
        console.warn('Geocoding failed, using coordinates:', error.message || error);
      }
      
      return `Koordinat: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  }, []);

  // Create simple validation (always valid since no geofencing)
  const createSimpleValidation = useCallback((accuracy?: number): SimpleValidation => {
    const riskScore = accuracy && accuracy > 50 ? 40 : 10; // Low risk score since we accept any location
    
    return {
      isValid: true, // Always valid since no geofencing required
      riskScore,
      warnings: accuracy && accuracy > 50 ? ['Akurasi GPS rendah'] : []
    };
  }, []);  // Handle location selection (GPS only)
  const handleLocationSelect = useCallback(async (lat: number, lng: number, accuracy?: number) => {
    setIsLoading(true);
    setAddressInfo('Mengambil alamat...'); // Show loading state immediately
    
    try {
      // Always set position first, even if address fetch fails
      setPosition({ lat, lng });
        // Try to fetch address with our improved API
      let address = `Koordinat: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      try {
        address = await fetchAddress(lat, lng);      } catch (_addressError) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Address fetch failed, using coordinates fallback');
        }
        // Fallback address already set above
      }
      
      setAddressInfo(address);
      
      // Create simple validation
      const validation = createSimpleValidation(accuracy);
      
      // Call parent callback using ref to avoid dependency issues
      onLocationSelectRef.current(lat, lng, address, validation);
      
      setLocationError(null);
    } catch (error) {
      console.warn('Error handling location selection:', error);
      setLocationError('Gagal memproses lokasi yang dipilih');
      // Still set a fallback address
      const fallbackAddress = `Koordinat: ${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      setAddressInfo(fallbackAddress);
      
      // Create validation and call callback even on error
      const validation = createSimpleValidation(accuracy);
      onLocationSelectRef.current(lat, lng, fallbackAddress, validation);
    } finally {
      setIsLoading(false);
    }
  }, [fetchAddress, createSimpleValidation]); // Now stable dependencies
  // Get current GPS location
  const getCurrentLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setLocationError('Browser tidak mendukung GPS');
      return;
    }

    setIsLoading(true);
    setLocationError(null);
    setAddressInfo('Mencari lokasi GPS...');

    navigator.geolocation.getCurrentPosition(      (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        if (process.env.NODE_ENV === 'development') {
          console.log(`GPS location found: ${latitude}, ${longitude} (accuracy: ${accuracy}m)`);
        }
        handleLocationSelect(latitude, longitude, accuracy);
      },
      (error) => {
        let errorMessage = 'Tidak dapat mengakses lokasi';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Akses lokasi ditolak. Mohon izinkan akses lokasi di browser.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Lokasi tidak tersedia. Pastikan GPS aktif.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Waktu habis saat mengambil lokasi GPS. Coba lagi.';
            break;
        }
        
        console.error('GPS error:', error);
        setLocationError(errorMessage);
        setAddressInfo('');
        setIsLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000, // Reduced to 10 seconds
        maximumAge: 30000 // Reduced to 30 seconds for fresher location
      }
    );  }, [handleLocationSelect]);
  // Auto-get location on component mount (only once)
  useEffect(() => {
    const autoGetLocation = async () => {
      if (!navigator.geolocation) {
        setLocationError('Browser tidak mendukung GPS');
        return;
      }

      setIsLoading(true);
      setLocationError(null);
      setAddressInfo('Mencari lokasi GPS...');

      navigator.geolocation.getCurrentPosition(        async (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          if (process.env.NODE_ENV === 'development') {
            console.log(`GPS location found: ${latitude}, ${longitude} (accuracy: ${accuracy}m)`);
          }
          
          // Inline location handling to avoid dependency issues
          setIsLoading(true);
          setAddressInfo('Mengambil alamat...');
          
          try {
            setPosition({ lat: latitude, lng: longitude });
              let address = `Koordinat: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
            try {
              address = await fetchAddress(latitude, longitude);
            } catch (_addressError) {
              if (process.env.NODE_ENV === 'development') {
                console.log('Address fetch failed, using coordinates fallback');
              }
            }
            
            setAddressInfo(address);
            
            const validation = createSimpleValidation(accuracy);
            onLocationSelectRef.current(latitude, longitude, address, validation);
            setLocationError(null);
          } catch (error) {
            console.warn('Error handling location selection:', error);
            setLocationError('Gagal memproses lokasi yang dipilih');
            const fallbackAddress = `Koordinat: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
            setAddressInfo(fallbackAddress);
            
            const validation = createSimpleValidation(accuracy);
            onLocationSelectRef.current(latitude, longitude, fallbackAddress, validation);
          } finally {
            setIsLoading(false);
          }
        },
        (error) => {
          let errorMessage = 'Tidak dapat mengakses lokasi';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Akses lokasi ditolak. Mohon izinkan akses lokasi di browser.';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Lokasi tidak tersedia. Pastikan GPS aktif.';
              break;
            case error.TIMEOUT:
              errorMessage = 'Waktu habis saat mengambil lokasi GPS. Coba lagi.';
              break;
          }
          
          console.error('GPS error:', error);
          setLocationError(errorMessage);
          setAddressInfo('');
          setIsLoading(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 30000
        }
      );
    };
    
    autoGetLocation();
  }, [fetchAddress, createSimpleValidation]); // Only include stable dependencies

  return (
    <div className="w-full">
      {/* Control Panel */}
      <div className="p-4 bg-white border-b">
        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <button
            onClick={getCurrentLocation}
            disabled={isLoading}
            className="flex items-center justify-center px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Target className="w-4 h-4 mr-2" />
            {isLoading ? 'Mengambil Lokasi...' : 'Gunakan Lokasi Saya'}
          </button>          <div className="text-sm text-gray-600">
            <MapPin className="inline w-4 h-4 mr-1" />
            Gunakan tombol di atas untuk mengaktifkan GPS dan mendeteksi lokasi Anda
          </div>
        </div>        {/* Location Status */}
        {position && (
          <div className="p-3 mt-3 border border-green-200 rounded-lg bg-green-50">
            <div className="flex items-center mb-2">
              <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
              <span className="text-sm font-medium text-green-800">Lokasi GPS Berhasil Dideteksi</span>
            </div>
            <p className="text-sm text-green-700">
              {addressInfo || 'Mengambil alamat...'}
            </p>
            <p className="mt-1 text-xs text-green-600">
              Koordinat: {position.lat.toFixed(6)}, {position.lng.toFixed(6)}
            </p>
            {addressInfo.includes('Koordinat:') && (
              <p className="mt-1 text-xs text-yellow-600">
                Alamat tidak tersedia, menggunakan koordinat GPS
              </p>
            )}
          </div>
        )}

        {/* Error Display */}
        {locationError && (
          <div className="p-3 mt-3 border border-red-200 rounded-lg bg-red-50">
            <div className="flex items-center">
              <AlertTriangle className="w-4 h-4 mr-2 text-red-600" />
              <span className="text-sm font-medium text-red-800">Error</span>
            </div>
            <p className="mt-1 text-sm text-red-700">{locationError}</p>
          </div>
        )}
      </div>

      {/* Map */}
      <div className="relative h-[300px] sm:h-[400px]">
        {typeof window !== 'undefined' && (
          <MapContainer
            center={position ? [position.lat, position.lng] : [initialLat, initialLng]}
            zoom={position ? 16 : 13}            style={{ height: '100%', width: '100%' }}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />            
            {/* Remove manual click events - GPS only */}
            
            {position && leafletIcon && (
              <Marker 
                position={[position.lat, position.lng]} 
                icon={leafletIcon}
              />
            )}
          </MapContainer>
        )}
      </div>

      {/* Instructions */}
      <div className="p-4 text-sm text-gray-600 bg-gray-50">
        <p className="mb-2 font-medium">Petunjuk:</p>        <ul className="space-y-1 list-disc list-inside">
          <li>Klik tombol &quot;Gunakan Lokasi Saya&quot; untuk mengaktifkan GPS</li>
          <li>Pastikan GPS/lokasi sudah diaktifkan pada perangkat Anda</li>
          <li>Tunggu hingga lokasi berhasil terdeteksi</li>
          <li>Lokasi akan ditampilkan secara otomatis di peta</li>
        </ul>
      </div>
    </div>
  );
}
