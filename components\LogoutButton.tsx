'use client';

import { useRouter } from 'next/navigation';

export default function LogoutButton() {
  const router = useRouter();

  const handleLogout = async () => {
    try {
      // Use form submission approach instead of fetch
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/api/auth/logout';
      document.body.appendChild(form);
      form.submit();
      
      // No need to manually redirect as the API will handle it
    } catch (_error) {
      // Fallback if the form submission fails
      router.push('/login');
    }
  };

  return (
    <button
      onClick={handleLogout}
      className="px-3 py-1 text-sm text-red-600 hover:text-red-800"
    >
      Logout
    </button>
  );
}
