// Definisi opsi jenjang untuk dropdown
export const jenjangOptions = [
  { value: 'PAUD', label: 'PAUD' },
  { value: 'SD', label: 'SD' },
  { value: 'SMP', label: 'SMP' },
  { value: 'SMA', label: 'SMA' },
  { value: 'UMUM', label: 'UMUM' },
  { value: 'DINAS', label: 'DINAS' },
  { value: 'BPMP', label: 'BPMP' },
  { value: 'LAINNYA', label: 'LAINNYA' }
];

// Tambahkan konstanta lain yang mungkin diperlukan aplikasi
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];

// Konstanta untuk pagination
export const ITEMS_PER_PAGE = 10;

// Status pelatihan
export const PELATIHAN_STATUS = {
  UPCOMING: 'UPCOMING',
  ONGOING: 'ONGOING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

// Helper untuk mendapatkan base URL aplikasi
export const getBaseUrl = () => {
  if (typeof window === 'undefined') {
    // Server-side
    return process.env.APP_URL || process.env.NEXT_PUBLIC_BASE_URL || '';
  } else {
    // Client-side
    return window.location.origin || process.env.NEXT_PUBLIC_BASE_URL || '';
  }
};
