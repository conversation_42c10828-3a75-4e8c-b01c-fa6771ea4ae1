import { getCurrentUser } from '../../../lib/auth';
import { redirect } from 'next/navigation';
import { prisma } from '../../../lib/prisma';
import { getUserAccessScope } from '../../../utils/helpers';
import PegawaiRekapTable from './components/PegawaiRekapTable';
import FilterBar from '../../../components/FilterBar';

export const metadata = {
  title: 'Rekap Penugasan Pegawai',
  description: 'Rekap penugasan pegawai pada kegiatan yang dibuat oleh GM',
};

export default async function RekapPenugasanPage(
  props: {
    searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
  }
) {
  const searchParams = await props.searchParams;
  const user = await getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  // Get access scope based on user role
  const { isGM: _isGM, whereClause } = getUserAccessScope(user);
  const _isAdmin = user.role === 'ADMIN';

  // Prepare filters
  const startDate = searchParams.start_date as string | undefined;
  const endDate = searchParams.end_date as string | undefined;
  const pegawaiId = searchParams.pegawai as string | undefined;

  // Build date filter if provided
  let dateFilter = {};
  if (startDate && endDate) {
    dateFilter = {
      AND: [
        { tgl_mulai: { gte: new Date(startDate) } },
        { tgl_berakhir: { lte: new Date(endDate) } }
      ]
    };
  } else if (startDate) {
    dateFilter = { tgl_mulai: { gte: new Date(startDate) } };
  } else if (endDate) {
    dateFilter = { tgl_berakhir: { lte: new Date(endDate) } };
  }

  // Fetch all pegawai for filter dropdown
  const pegawaiList = await prisma.pegawai.findMany({
    orderBy: { nama: 'asc' },
    select: {
      id: true,
      nama: true,
      nip: true,
      jabatan: true
    }
  });

  // Build pegawai filter
  const pegawaiFilter = pegawaiId ? { pegawaiId } : {};

  // Fetch pelatihan and their panitia assignments
  const pelatihan = await prisma.pelatihan.findMany({
    where: {
      ...whereClause,
      ...dateFilter
    },
    orderBy: {
      tgl_mulai: 'desc'
    },
    select: {
      id: true,
      nama: true,
      tempat: true,
      tgl_mulai: true,
      tgl_berakhir: true,
      panitia: {
        where: pegawaiFilter,
        include: {
          pegawai: true,
          lokasi: true
        }
      },
      user: {
        select: {
          id: true,
          name: true,
          role: true
        }
      }
    }
  });

  // Process data to get a pegawai-centric view
  const pegawaiAssignments = new Map();

  pelatihan.forEach(pel => {
    pel.panitia.forEach(pan => {
      const pegawai = pan.pegawai;
      if (!pegawaiAssignments.has(pegawai.id)) {
        pegawaiAssignments.set(pegawai.id, {
          id: pegawai.id,
          nama: pegawai.nama,
          nip: pegawai.nip,
          jabatan: pegawai.jabatan,
          assignments: []
        });
      }
      
      pegawaiAssignments.get(pegawai.id).assignments.push({
        pelatihanId: pel.id,
        namaPelatihan: pel.nama,
        tempat: pel.tempat,
        tglMulai: pel.tgl_mulai,
        tglBerakhir: pel.tgl_berakhir,
        jabatanPanitia: pan.jabatan,
        lokasiTugas: pan.lokasi.kab_kota,
        gmName: pel.user.name,
        gmRole: pel.user.role
      });
    });
  });

  const pegawaiRekapData = Array.from(pegawaiAssignments.values());

  // Prepare filter options for FilterBar
  const pegawaiOptions = pegawaiList.map(p => ({
    id: p.id,
    label: `${p.nama} (${p.nip}) - ${p.jabatan}`,
    value: p.id
  }));

  // Create filter configuration for FilterBar
  const filterConfig = [
    {
      name: 'start_date',
      label: 'Tanggal Mulai',
      type: 'date' as const
    },
    {
      name: 'end_date',
      label: 'Tanggal Selesai',
      type: 'date' as const
    },
    {
      name: 'pegawai',
      label: 'Pegawai',
      type: 'select' as const,
      options: pegawaiOptions
    }
  ];

  // Current params for FilterBar
  const currentParams = {
    start_date: startDate,
    end_date: endDate,
    pegawai: pegawaiId
  };

  return (
    <div className="container px-4 mx-auto sm:px-6 lg:px-8">
      <div className="flex flex-col items-start justify-between gap-4 my-4 sm:my-6 sm:flex-row sm:items-center">
        <h1 className="text-xl font-semibold sm:text-2xl">Rekap Penugasan Pegawai</h1>
      </div>

      {/* Filter component */}
      <FilterBar
        filters={filterConfig}
        baseUrl="/dashboard/rekap-penugasan"
        currentParams={currentParams}
      />

      {/* Main content */}
      <div className="mt-6">
        <PegawaiRekapTable data={pegawaiRekapData} />
      </div>
    </div>
  );
}