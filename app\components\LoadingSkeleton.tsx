export default function LoadingSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
      
      <div className="space-y-4">
        {/* Table header skeleton */}
        <div className="h-10 bg-gray-200 rounded w-full"></div>
        
        {/* Table rows skeleton */}
        {[...Array(5)].map((_, index) => (
          <div key={index} className="h-16 bg-gray-200 rounded w-full"></div>
        ))}
      </div>
    </div>
  );
}