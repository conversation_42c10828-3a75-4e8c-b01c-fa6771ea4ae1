// File: /app/api/verify-absensi/route.ts
import { NextResponse, NextRequest } from 'next/server';
import { validateAbsensiLink } from '../../../lib/validateLink';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const link = searchParams.get('link');

    if (!link) {
      return NextResponse.json(
        { valid: false, message: 'Link tidak valid' },
        { status: 400 }
      );
    }

    const validation = await validateAbsensiLink(link);

    return NextResponse.json(validation);
  } catch (error) {
    console.error('Validate absensi link API error:', error);
    return NextResponse.json(
      { valid: false, message: '<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han server' },
      { status: 500 }
    );
  }
}