@echo off
setlocal enabledelayedexpansion

:: VERIFICATION SCRIPT - Check if auto-refresh fix is working
:: Test real-time photo upload functionality

title Auto-Refresh Fix Verification

echo.
echo ================================================
echo    AUTO-REFRESH FIX VERIFICATION SYSTEM
echo    kegiatan.bpmpkaltim.id Status Check
echo ================================================
echo.

:: Configuration
set VPS_IP=*************
set VPS_USER=root

echo [36m🔍 RUNNING COMPREHENSIVE VERIFICATION...[0m
echo.

:: Step 1: Basic connectivity
echo [35m[1/8][0m Testing VPS connectivity...
ssh %VPS_USER%@%VPS_IP% "echo 'Connection OK'" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ VPS Connection: OK[0m
) else (
    echo [31m❌ VPS Connection: FAILED[0m
    echo [31m[ERROR][0m Cannot connect to VPS. Check SSH connection.
    pause
    exit /b 1
)

:: Step 2: Service status
echo [35m[2/8][0m Checking service status...

:: Nginx
ssh %VPS_USER%@%VPS_IP% "systemctl is-active nginx" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Nginx Service: ACTIVE[0m
) else (
    echo [31m❌ Nginx Service: INACTIVE[0m
    set ISSUES_FOUND=1
)

:: Advanced Photo Monitor
ssh %VPS_USER%@%VPS_IP% "systemctl is-active advanced-photo-monitor" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Photo Monitor Service: ACTIVE[0m
) else (
    echo [31m❌ Photo Monitor Service: INACTIVE[0m
    set ISSUES_FOUND=1
)

:: Step 3: Directory structure
echo [35m[3/8][0m Checking directory structure...

ssh %VPS_USER%@%VPS_IP% "ls -d /home/<USER>/htdocs/public/uploads 2>/dev/null | head -1" >temp_dir.txt 2>nul
set /p UPLOAD_DIR=<temp_dir.txt
del temp_dir.txt >nul 2>&1

if defined UPLOAD_DIR (
    echo [32m✅ Upload Directory: %UPLOAD_DIR%[0m
) else (
    echo [31m❌ Upload Directory: NOT FOUND[0m
    set ISSUES_FOUND=1
)

:: Step 4: Nginx configuration
echo [35m[4/8][0m Checking nginx configuration...

ssh %VPS_USER%@%VPS_IP% "nginx -t" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Nginx Configuration: VALID[0m
) else (
    echo [31m❌ Nginx Configuration: ERROR[0m
    set ISSUES_FOUND=1
)

:: Check uploads location config
ssh %VPS_USER%@%VPS_IP% "grep -r 'location /uploads' /etc/nginx/" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Uploads Location: CONFIGURED[0m
) else (
    echo [31m❌ Uploads Location: NOT CONFIGURED[0m
    set ISSUES_FOUND=1
)

:: Step 5: File permissions
echo [35m[5/8][0m Checking file permissions...

if defined UPLOAD_DIR (
    ssh %VPS_USER%@%VPS_IP% "test -w '%UPLOAD_DIR%'" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [32m✅ Upload Directory: WRITABLE[0m
    ) else (
        echo [31m❌ Upload Directory: NOT WRITABLE[0m
        set ISSUES_FOUND=1
    )
    
    :: Check for recent photos
    ssh %VPS_USER%@%VPS_IP% "find '%UPLOAD_DIR%' -name '*.jpg' -o -name '*.png' -o -name '*.jpeg' | head -1" >temp_photo.txt 2>nul
    set /p SAMPLE_PHOTO=<temp_photo.txt
    del temp_photo.txt >nul 2>&1
    
    if defined SAMPLE_PHOTO (
        echo [32m✅ Sample Photos: FOUND[0m
    ) else (
        echo [33m⚠️ Sample Photos: NONE (Normal for new setup)[0m
    )
) else (
    echo [33m⚠️ Skipping permission check - directory not found[0m
)

:: Step 6: Log files
echo [35m[6/8][0m Checking log files...

ssh %VPS_USER%@%VPS_IP% "test -f /var/log/advanced-photo-monitor.log" >nul 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Monitor Log: EXISTS[0m
    
    :: Check for recent activity
    ssh %VPS_USER%@%VPS_IP% "tail -1 /var/log/advanced-photo-monitor.log" >temp_log.txt 2>nul
    set /p LAST_LOG=<temp_log.txt
    del temp_log.txt >nul 2>&1
    
    if defined LAST_LOG (
        echo [36m   Last activity: !LAST_LOG![0m
    )
) else (
    echo [31m❌ Monitor Log: NOT FOUND[0m
    set ISSUES_FOUND=1
)

:: Step 7: Network connectivity test
echo [35m[7/8][0m Testing network connectivity...

ssh %VPS_USER%@%VPS_IP% "curl -s -o /dev/null -w '%%{http_code}' http://localhost/uploads/ --max-time 5" >temp_http.txt 2>nul
set /p HTTP_STATUS=<temp_http.txt
del temp_http.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo [32m✅ HTTP Access: OK (Status: %HTTP_STATUS%)[0m
) else if "%HTTP_STATUS%"=="404" (
    echo [33m⚠️ HTTP Access: Not Found (Status: %HTTP_STATUS%) - Normal if no files[0m
) else if "%HTTP_STATUS%"=="403" (
    echo [31m❌ HTTP Access: Forbidden (Status: %HTTP_STATUS%)[0m
    set ISSUES_FOUND=1
) else (
    echo [31m❌ HTTP Access: Error (Status: %HTTP_STATUS%)[0m
    set ISSUES_FOUND=1
)

:: Step 8: Real-time test
echo [35m[8/8][0m Running real-time functionality test...

if defined UPLOAD_DIR (
    :: Create test file
    ssh %VPS_USER%@%VPS_IP% "mkdir -p '%UPLOAD_DIR%/test-verification' && echo 'test' > '%UPLOAD_DIR%/test-verification/test-file.txt'"
    
    :: Wait a moment
    timeout /t 2 >nul
    
    :: Check if monitor detected it
    ssh %VPS_USER%@%VPS_IP% "grep -q 'test-verification' /var/log/advanced-photo-monitor.log 2>/dev/null" >nul 2>&1
    if %errorlevel% equ 0 (
        echo [32m✅ Real-time Monitoring: WORKING[0m
    ) else (
        echo [33m⚠️ Real-time Monitoring: May not detect text files (normal)[0m
    )
    
    :: Cleanup
    ssh %VPS_USER%@%VPS_IP% "rm -rf '%UPLOAD_DIR%/test-verification'" >nul 2>&1
) else (
    echo [33m⚠️ Skipping real-time test - directory not found[0m
)

:: Final assessment
echo.
echo ================================================
echo    VERIFICATION RESULTS
echo ================================================
echo.

if defined ISSUES_FOUND (
    echo [31m❌ ISSUES FOUND - Auto-refresh may not work properly[0m
    echo.
    echo [33m🔧 RECOMMENDED ACTIONS:[0m
    echo   1. Run troubleshooter: troubleshoot-autorefresh.bat
    echo   2. Check service logs: ssh %VPS_USER%@%VPS_IP% "journalctl -u advanced-photo-monitor -n 20"
    echo   3. Restart services: ssh %VPS_USER%@%VPS_IP% "systemctl restart nginx advanced-photo-monitor"
    echo   4. Re-deploy if needed: deploy-final-autorefresh.bat
    echo.
    
    set /p run_troubleshooter="Run automated troubleshooter now? (y/n): "
    if /i "!run_troubleshooter!"=="y" (
        if exist "%~dp0troubleshoot-autorefresh.bat" (
            call "%~dp0troubleshoot-autorefresh.bat"
        ) else (
            ssh %VPS_USER%@%VPS_IP% "/tmp/troubleshoot-autorefresh.sh"
        )
    )
    
) else (
    echo [32m✅ ALL CHECKS PASSED - Auto-refresh should work properly![0m
    echo.
    echo [33m🧪 MANUAL TEST STEPS:[0m
    echo   1. Open: https://kegiatan.bpmpkaltim.id
    echo   2. Login and upload a photo in attendance form
    echo   3. Immediately open attendance detail
    echo   4. Photo should appear WITHOUT nginx reload
    echo.
    
    set /p open_app="Open application for manual testing? (y/n): "
    if /i "!open_app!"=="y" (
        echo [35m[ACTION][0m Opening application...
        start "" "https://kegiatan.bpmpkaltim.id"
        
        echo [35m[ACTION][0m Starting real-time monitoring...
        start "" cmd /c "title Photo Monitor Logs && ssh %VPS_USER%@%VPS_IP% tail -f /var/log/advanced-photo-monitor.log"
    )
)

echo.
echo [33m📊 MONITORING COMMANDS:[0m
echo   Real-time logs: ssh %VPS_USER%@%VPS_IP% "tail -f /var/log/advanced-photo-monitor.log"
echo   Service status: ssh %VPS_USER%@%VPS_IP% "systemctl status advanced-photo-monitor nginx"
echo   Dashboard: monitor-autorefresh.bat
echo   Management: management-center.bat
echo.

echo [36m[INFO][0m Verification completed.
pause
