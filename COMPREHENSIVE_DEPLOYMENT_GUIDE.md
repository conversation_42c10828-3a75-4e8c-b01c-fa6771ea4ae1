# Deployment Guide - Photo Upload Fix for kegiatan.bpmpkaltim.id

## 🎯 Problem Summary
- **Issue**: Photos uploaded in absensi detail pages show 404 errors
- **Root Cause**: Files saved to `/public/public/uploads/` but accessed via `/uploads/`
- **Domain**: kegiatan.bpmpkaltim.id 
- **Server**: Ubuntu 22.04 VPS

## 🔧 Solution Overview
1. Fix public folder structure in Next.js standalone build
2. Update nginx configuration to serve static files correctly
3. Set proper file permissions for upload directories
4. Ensure correct URL routing for uploaded files

## 📋 Step-by-Step Implementation

### Phase 1: Local Build Preparation (COMPLETED ✅)

**Already completed on your local machine:**
- [x] Fixed `deploy.sh` script for proper public folder handling
- [x] Created `fix-deployment-paths.mjs` for path correction
- [x] Generated deployment archive: `.next/deployment.tar.gz` (34.9 MB)
- [x] Created nginx configuration template
- [x] Generated VPS deployment scripts

### Phase 2: VPS Deployment (NEXT STEPS)

#### Step 1: Upload Files to VPS
```bash
# From your local machine (Windows)
scp .next/deployment.tar.gz <EMAIL>:/home/<USER>/
scp fix-photo-upload-vps.sh <EMAIL>:/home/<USER>/
```

#### Step 2: Extract and Setup on VPS
```bash
# SSH into your VPS
ssh <EMAIL>

# Create deployment directory
mkdir -p /home/<USER>/deployment
cd /home/<USER>/deployment

# Extract deployment files
tar -xzf ../deployment.tar.gz

# Verify extraction
ls -la  # Should see: server.js, public/, package.json, etc.
```

#### Step 3: Run the Automated Fix Script
```bash
# Make script executable
chmod +x /home/<USER>/fix-photo-upload-vps.sh

# Update the script configuration
nano /home/<USER>/fix-photo-upload-vps.sh
# Update: DEPLOYMENT_PATH="/home/<USER>/deployment"

# Run the fix script
cd /home/<USER>/deployment
sudo /home/<USER>/fix-photo-upload-vps.sh
```

**What the script does:**
- ✅ Creates upload directories with correct permissions
- ✅ Updates nginx configuration with proper static file serving
- ✅ Sets proper file ownership (www-data:www-data)
- ✅ Restarts nginx and application
- ✅ Runs verification tests

#### Step 4: Manual Verification

1. **Check file structure:**
```bash
ls -la public/uploads/absensi/photos/
ls -la public/uploads/biodata/photos/
```

2. **Verify nginx configuration:**
```bash
sudo nginx -t
sudo systemctl status nginx
```

3. **Check application status:**
```bash
pm2 status
pm2 logs
```

4. **Test file permissions:**
```bash
# Should be writable by www-data
ls -la public/uploads/ 
```

### Phase 3: Testing (CRITICAL)

#### Test 1: Upload New Photo
1. Go to: `https://kegiatan.bpmpkaltim.id/public-pages/absensi/internal/[some-link]`
2. Fill form and take/upload photo
3. Submit form
4. Check file created: `ls public/uploads/absensi/photos/[pelatihan-id]/`

#### Test 2: Display Verification  
1. Go to admin panel → Absensi list
2. Click "Detail" on recent absensi
3. Verify photo displays correctly (no 404 error)
4. Check browser network tab for successful image load

#### Test 3: Direct URL Access
```bash
# Test direct file access
curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/[pelatihan-id]/[photo-file]
# Should return: HTTP/1.1 200 OK
```

## 📊 Technical Details

### Current File Flow (FIXED)
- **Upload API**: `/api/absensi/upload-photo/route.ts`
- **Save Location**: `{deployment_path}/public/uploads/absensi/photos/{pelatihanId}/`
- **URL Access**: `https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/{pelatihanId}/`
- **Nginx Root**: `{deployment_path}/public`

### Key Configuration Changes
1. **Nginx root directory**: Points to `/deployment/public` (not `/deployment/public/public`)
2. **Upload location handler**: Direct `/uploads/` serving from root
3. **File permissions**: `www-data:www-data` with `755` directories, `644` files

## 🚨 Troubleshooting

### If photos still don't display:

1. **Check nginx error logs:**
```bash
sudo tail -f /var/log/nginx/error.log
```

2. **Verify file locations:**
```bash
find public/uploads -name "*.jpg" -ls
```

3. **Test direct file access:**
```bash
# Replace with actual file path
curl -v https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/test.jpg
```

4. **Check application logs:**
```bash
pm2 logs nextjs-app
```

### Common Issues & Solutions:

| Issue | Solution |
|-------|----------|
| 404 on image URLs | Check nginx root path in config |
| Permission denied | Run: `sudo chown -R www-data:www-data public/uploads/` |
| Upload fails | Check disk space and directory permissions |
| Nginx config error | Restore backup: `sudo cp /etc/nginx/sites-available/kegiatan.bpmpkaltim.id.backup.* /etc/nginx/sites-available/kegiatan.bpmpkaltim.id` |

## 📁 Files Generated

- ✅ `.next/deployment.tar.gz` - Main deployment archive
- ✅ `nginx-config.txt` - Nginx configuration template  
- ✅ `fix-photo-upload-vps.sh` - Automated VPS fix script
- ✅ `DEPLOYMENT_FIX_GUIDE.md` - Detailed instructions
- ✅ `deploy-to-vps.bat` - Windows deployment helper

## 🎯 Success Criteria

The fix is successful when:
- [ ] New photos upload without errors
- [ ] Uploaded photos display correctly in detail views  
- [ ] No 404 errors in browser console for image files
- [ ] Direct image URLs return HTTP 200 status
- [ ] File permissions are correct (www-data:www-data)

## 📞 Next Actions

1. **Deploy to VPS** using the generated files and scripts
2. **Run verification tests** to confirm photo upload functionality
3. **Monitor** for any remaining issues in production
4. **Document** final server configuration for future reference

---

**Status**: Ready for VPS deployment  
**Estimated Time**: 15-30 minutes  
**Risk Level**: Low (includes rollback procedures)
