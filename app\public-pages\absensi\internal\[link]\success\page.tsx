'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { CheckCircle, Download, MapPin, Camera, Clock, User } from 'lucide-react';

interface AbsensiData {
  id: string;
  nama: string;
  unit_kerja: string;
  waktu: string;
  pelatihan: {
    nama: string;
    tempat: string;
  };
  alamat?: string;
  latitude?: number;
  longitude?: number;
  foto_url?: string;
}

export default function AbsensiInternalSuccessPage() {
  const params = useParams<{ link: string }>();
  const [absensiData, setAbsensiData] = useState<AbsensiData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Prevent going back to the form page
  useEffect(() => {
    // Replace the current history entry to prevent going back
    window.history.replaceState(null, '', window.location.href);

    // Handle the popstate event (when user clicks back button)
    const handlePopState = () => {
      window.history.pushState(null, '', window.location.href);
    };

    // Add event listener for back button clicks
    window.addEventListener('popstate', handlePopState);

    return () => {
      // Clean up event listener
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  // Fetch the latest absensi data for this link
  useEffect(() => {
    const fetchAbsensiData = async () => {
      try {
        const response = await fetch(`/api/absensi/latest?link=${params.link}`);
        if (response.ok) {
          const data = await response.json();
          setAbsensiData(data);
        } else {
          setError('Gagal memuat data absensi');
        }
      } catch (err) {
        console.error('Error fetching absensi data:', err);
        setError('Terjadi kesalahan saat memuat data');
      } finally {
        setIsLoading(false);
      }
    };

    if (params.link) {
      fetchAbsensiData();
    }
  }, [params.link]);

  const handlePrintCertificate = () => {
    window.print();
  };

  const handleDownloadData = () => {
    if (!absensiData) return;
    
    const dataText = `
BUKTI ABSENSI INTERNAL

Nama: ${absensiData.nama}
Unit Kerja: ${absensiData.unit_kerja}
Kegiatan: ${absensiData.pelatihan.nama}
Tempat: ${absensiData.pelatihan.tempat}
Waktu Absensi: ${new Date(absensiData.waktu).toLocaleString('id-ID')}
${absensiData.alamat ? `Lokasi: ${absensiData.alamat}` : ''}
${absensiData.latitude && absensiData.longitude ? `Koordinat: ${absensiData.latitude.toFixed(6)}, ${absensiData.longitude.toFixed(6)}` : ''}

© ${new Date().getFullYear()} BPMP Provinsi Kalimantan Timur
    `.trim();

    const blob = new Blob([dataText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `absensi-${absensiData.nama}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat data absensi...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center px-4">
        <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Terjadi Kesalahan</h1>
          <p className="text-red-600 mb-4">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6 text-center">
          <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Absensi Internal Berhasil!
          </h1>
          <p className="text-gray-600 mb-4">
            Data kehadiran Anda telah berhasil disimpan dan diverifikasi
          </p>
          <div className="inline-block bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">
            ✓ Foto + GPS Terverifikasi
          </div>
        </div>

        {/* Attendance Summary */}
        {absensiData && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <User className="w-5 h-5" />
              Ringkasan Absensi
            </h2>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Nama Peserta</p>
                  <p className="font-medium">{absensiData.nama}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Unit Kerja</p>
                  <p className="font-medium">{absensiData.unit_kerja}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Kegiatan</p>
                  <p className="font-medium">{absensiData.pelatihan.nama}</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    Waktu Absensi
                  </p>
                  <p className="font-medium">
                    {new Date(absensiData.waktu).toLocaleString('id-ID')}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Tempat Kegiatan</p>
                  <p className="font-medium">{absensiData.pelatihan.tempat}</p>
                </div>
                {absensiData.alamat && (
                  <div>
                    <p className="text-sm text-gray-500 flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      Lokasi Absensi
                    </p>
                    <p className="font-medium text-sm">{absensiData.alamat}</p>
                    {absensiData.latitude && absensiData.longitude && (
                      <a
                        href={`https://www.google.com/maps?q=${absensiData.latitude},${absensiData.longitude}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-sm"
                      >
                        Lihat di Google Maps
                      </a>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Verification Status */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Status Verifikasi</h2>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <Camera className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Foto Kehadiran</p>
                <p className="text-sm text-green-600">✓ Foto selfie berhasil diambil dan disimpan</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <MapPin className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Lokasi GPS</p>
                <p className="text-sm text-green-600">✓ Koordinat GPS high-accuracy berhasil direkam</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Validasi Data</p>
                <p className="text-sm text-green-600">✓ Semua data telah divalidasi dan disimpan</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Tindakan Lanjutan</h2>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handlePrintCertificate}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <Download className="w-4 h-4" />
              Cetak Bukti Absensi
            </Button>
            
            {absensiData && (
              <Button
                onClick={handleDownloadData}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Unduh Data Absensi
              </Button>
            )}
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Catatan:</strong> Simpan bukti absensi ini sebagai konfirmasi kehadiran Anda. 
              Data telah tersimpan di sistem dan dapat diakses oleh penyelenggara.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>© {new Date().getFullYear()} BPMP Provinsi Kalimantan Timur</p>
          <p className="mt-1">Sistem Absensi Pelatihan Internal</p>
        </div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .no-print {
            display: none !important;
          }
          
          body {
            background: white !important;
          }
          
          .bg-gradient-to-br {
            background: white !important;
          }
          
          .shadow-lg {
            box-shadow: none !important;
            border: 1px solid #e5e7eb !important;
          }
          
          .text-blue-600 {
            color: #000 !important;
          }
          
          .bg-green-50 {
            background: #f9f9f9 !important;
          }
          
          .bg-blue-50 {
            background: #f9f9f9 !important;
          }
        }
      `}</style>
    </div>
  );
}
