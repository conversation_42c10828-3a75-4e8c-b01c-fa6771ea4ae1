#!/bin/bash

# Real-time Monitoring Dashboard untuk Advanced Auto-Refresh Fix
# Monitor status nginx, file uploads, dan permissions real-time

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

# Clear screen function
clear_screen() {
    clear
    echo -e "${BOLD}${CYAN}"
    echo "=============================================="
    echo "   ADVANCED AUTO-REFRESH MONITORING DASHBOARD"
    echo "=============================================="
    echo -e "${NC}"
}

# Service status check
check_services() {
    echo -e "${BOLD}${BLUE}🔧 SERVICES STATUS:${NC}"
    
    # Nginx
    if systemctl is-active --quiet nginx; then
        echo -e "  • Nginx: ${GREEN}✅ ACTIVE${NC}"
    else
        echo -e "  • Nginx: ${RED}❌ INACTIVE${NC}"
    fi
    
    # Advanced Photo Monitor
    if systemctl is-active --quiet advanced-photo-monitor.service 2>/dev/null; then
        echo -e "  • Photo Monitor: ${GREEN}✅ ACTIVE${NC}"
    else
        echo -e "  • Photo Monitor: ${RED}❌ INACTIVE${NC}"
    fi
    
    # Next.js App (check port 3000)
    if netstat -tlnp 2>/dev/null | grep -q ":3000"; then
        echo -e "  • Next.js App: ${GREEN}✅ RUNNING${NC}"
    else
        echo -e "  • Next.js App: ${YELLOW}⚠️ NOT DETECTED${NC}"
    fi
    
    echo ""
}

# File upload statistics
show_upload_stats() {
    echo -e "${BOLD}${PURPLE}📊 UPLOAD STATISTICS:${NC}"
    
    # Find upload directories
    UPLOAD_DIRS=()
    while IFS= read -r -d '' dir; do
        if [ -d "$dir" ]; then
            UPLOAD_DIRS+=("$dir")
        fi
    done < <(find /home -path "*/htdocs/public/uploads" -type d -print0 2>/dev/null || true)
    
    if [ ${#UPLOAD_DIRS[@]} -eq 0 ]; then
        UPLOAD_DIRS=("/var/www/html/public/uploads")
    fi
    
    for upload_dir in "${UPLOAD_DIRS[@]}"; do
        if [ -d "$upload_dir" ]; then
            TOTAL_PHOTOS=$(find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" 2>/dev/null | wc -l)
            RECENT_PHOTOS=$(find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" -mtime -1 2>/dev/null | wc -l)
            DIR_SIZE=$(du -sh "$upload_dir" 2>/dev/null | cut -f1 || echo "0")
            
            echo -e "  📂 ${CYAN}$(basename "$(dirname "$(dirname "$upload_dir")")")${NC}"
            echo -e "     Total Photos: ${GREEN}$TOTAL_PHOTOS${NC}"
            echo -e "     Recent (24h): ${YELLOW}$RECENT_PHOTOS${NC}"
            echo -e "     Directory Size: ${BLUE}$DIR_SIZE${NC}"
            echo ""
        fi
    done
}

# Recent monitoring logs
show_recent_logs() {
    echo -e "${BOLD}${GREEN}📋 RECENT MONITORING ACTIVITY:${NC}"
    
    if [ -f "/var/log/advanced-photo-monitor.log" ]; then
        echo -e "${CYAN}Last 5 events:${NC}"
        tail -5 /var/log/advanced-photo-monitor.log 2>/dev/null | while read line; do
            if [[ "$line" =~ SUCCESS ]]; then
                echo -e "  ${GREEN}✅${NC} $line"
            elif [[ "$line" =~ WARNING ]]; then
                echo -e "  ${YELLOW}⚠️${NC} $line"
            elif [[ "$line" =~ ERROR ]]; then
                echo -e "  ${RED}❌${NC} $line"
            else
                echo -e "  ${BLUE}ℹ️${NC} $line"
            fi
        done
    else
        echo -e "  ${YELLOW}⚠️ Monitor log not found${NC}"
    fi
    echo ""
}

# Nginx access statistics
show_nginx_stats() {
    echo -e "${BOLD}${CYAN}🌐 NGINX ACCESS STATISTICS:${NC}"
    
    # Recent uploads access
    if [ -f "/var/log/nginx/photo_access.log" ]; then
        PHOTO_REQUESTS=$(tail -100 /var/log/nginx/photo_access.log 2>/dev/null | wc -l)
        RECENT_200=$(tail -100 /var/log/nginx/photo_access.log 2>/dev/null | grep " 200 " | wc -l)
        RECENT_404=$(tail -100 /var/log/nginx/photo_access.log 2>/dev/null | grep " 404 " | wc -l)
        
        echo -e "  Recent Photo Requests: ${BLUE}$PHOTO_REQUESTS${NC}"
        echo -e "  Successful (200): ${GREEN}$RECENT_200${NC}"
        echo -e "  Not Found (404): ${RED}$RECENT_404${NC}"
        
        if [ $PHOTO_REQUESTS -gt 0 ]; then
            SUCCESS_RATE=$(( (RECENT_200 * 100) / PHOTO_REQUESTS ))
            if [ $SUCCESS_RATE -ge 90 ]; then
                echo -e "  Success Rate: ${GREEN}$SUCCESS_RATE%${NC}"
            elif [ $SUCCESS_RATE -ge 70 ]; then
                echo -e "  Success Rate: ${YELLOW}$SUCCESS_RATE%${NC}"
            else
                echo -e "  Success Rate: ${RED}$SUCCESS_RATE%${NC}"
            fi
        fi
    else
        echo -e "  ${YELLOW}⚠️ Photo access log not found${NC}"
    fi
    echo ""
}

# System resources
show_system_resources() {
    echo -e "${BOLD}${PURPLE}💻 SYSTEM RESOURCES:${NC}"
    
    # Memory usage
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", ($3/$2) * 100.0}')
    echo -e "  Memory Usage: ${BLUE}$MEM_USAGE%${NC}"
    
    # Disk usage for upload directories
    for upload_dir in "${UPLOAD_DIRS[@]}"; do
        if [ -d "$upload_dir" ]; then
            DISK_USAGE=$(df "$(dirname "$upload_dir")" | tail -1 | awk '{print $5}' | sed 's/%//')
            if [ $DISK_USAGE -ge 90 ]; then
                echo -e "  Disk Usage: ${RED}$DISK_USAGE%${NC}"
            elif [ $DISK_USAGE -ge 80 ]; then
                echo -e "  Disk Usage: ${YELLOW}$DISK_USAGE%${NC}"
            else
                echo -e "  Disk Usage: ${GREEN}$DISK_USAGE%${NC}"
            fi
            break
        fi
    done
    
    # inotify usage
    INOTIFY_WATCHES=$(find /proc/*/fd -lname anon_inode:inotify 2>/dev/null | wc -l)
    echo -e "  inotify Watches: ${BLUE}$INOTIFY_WATCHES${NC}"
    echo ""
}

# Real-time test
run_realtime_test() {
    echo -e "${BOLD}${YELLOW}🧪 REAL-TIME TEST:${NC}"
    
    # Find first upload directory
    TEST_DIR=""
    for upload_dir in "${UPLOAD_DIRS[@]}"; do
        if [ -d "$upload_dir" ]; then
            TEST_DIR="$upload_dir/test-monitoring-$(date +%s)"
            break
        fi
    done
    
    if [ -n "$TEST_DIR" ]; then
        echo -e "  Creating test file..."
        mkdir -p "$TEST_DIR"
        TEST_FILE="$TEST_DIR/test-photo.jpg"
        
        # Create minimal JPEG
        printf '\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\x27 ($\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9' > "$TEST_FILE"
        
        sleep 1
        
        # Check permissions
        if [ -f "$TEST_FILE" ]; then
            OWNER=$(stat -c '%U:%G' "$TEST_FILE" 2>/dev/null || echo "unknown")
            echo -e "  File Owner: ${GREEN}$OWNER${NC}"
            
            # Test HTTP access
            TEST_URL="http://localhost/uploads/$(basename "$(dirname "$TEST_FILE")")/$(basename "$TEST_FILE")"
            HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 5 2>/dev/null || echo "000")
            
            if [ "$HTTP_STATUS" = "200" ]; then
                echo -e "  HTTP Access: ${GREEN}✅ SUCCESS ($HTTP_STATUS)${NC}"
            else
                echo -e "  HTTP Access: ${RED}❌ FAILED ($HTTP_STATUS)${NC}"
            fi
        else
            echo -e "  ${RED}❌ Test file creation failed${NC}"
        fi
        
        # Cleanup
        rm -rf "$TEST_DIR" 2>/dev/null || true
    else
        echo -e "  ${YELLOW}⚠️ No upload directory found for testing${NC}"
    fi
    echo ""
}

# Main monitoring loop
main_loop() {
    local mode="$1"
    
    if [ "$mode" = "continuous" ]; then
        while true; do
            clear_screen
            check_services
            show_upload_stats
            show_recent_logs
            show_nginx_stats
            show_system_resources
            
            echo -e "${BOLD}${CYAN}🔄 Auto-refresh in 10 seconds... (Press Ctrl+C to exit)${NC}"
            sleep 10
        done
    else
        clear_screen
        check_services
        show_upload_stats
        show_recent_logs
        show_nginx_stats
        show_system_resources
        
        if [ "$mode" = "test" ]; then
            run_realtime_test
        fi
    fi
}

# Usage function
show_usage() {
    echo "Usage: $0 [option]"
    echo ""
    echo "Options:"
    echo "  -c, --continuous    Run continuous monitoring (auto-refresh every 10s)"
    echo "  -t, --test         Run single check with real-time test"
    echo "  -s, --single       Run single check only (default)"
    echo "  -h, --help         Show this help"
    echo ""
}

# Command line options
case "${1:-}" in
    -c|--continuous)
        main_loop "continuous"
        ;;
    -t|--test)
        main_loop "test"
        ;;
    -s|--single|"")
        main_loop "single"
        ;;
    -h|--help)
        show_usage
        ;;
    *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac

echo -e "${BOLD}${GREEN}Monitoring completed.${NC}"
