@echo off
echo ========================================
echo   QUICK FIX PHOTO UPLOAD 404
echo ========================================
echo.

set VPS_HOST=kegiatan.bpmpkaltim.id
set VPS_USER=root

echo Status saat ini: 404 error saat akses foto
echo Solusi: Upload dan jalankan diagnosa script dengan auto-fix
echo.

echo [STEP 1] Upload diagnosa script ke VPS...
scp diagnosa-photo-upload.sh %VPS_USER%@%VPS_HOST%:/root/
if %errorlevel% neq 0 (
    echo ERROR: Gagal upload. Coba manual dengan WinSCP
    echo File: diagnosa-photo-upload.sh
    echo Dest: /root/
    pause
    exit /b 1
)

echo [STEP 2] Connecting ke VPS dan jalankan diagnosa...
echo.
echo Commands yang akan dijalankan di VPS:
echo   chmod +x diagnosa-photo-upload.sh
echo   sudo ./diagnosa-photo-upload.sh
echo.
echo Pilih 'y' ketika ditanya perbaikan otomatis!
echo.
pause

ssh %VPS_USER%@%VPS_HOST% "chmod +x diagnosa-photo-upload.sh && sudo ./diagnosa-photo-upload.sh"

echo.
echo ========================================
echo   QUICK FIX COMPLETED
echo ========================================
echo.
echo Test sekarang:
echo 1. Buka: https://kegiatan.bpmpkaltim.id/admin
echo 2. Upload foto absensi baru
echo 3. Cek apakah foto muncul tanpa 404
echo.
pause
