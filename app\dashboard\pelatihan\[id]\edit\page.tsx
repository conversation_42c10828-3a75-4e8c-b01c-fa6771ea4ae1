// File: /app/dashboard/pelatihan/[id]/edit/page.tsx
import { notFound, redirect } from 'next/navigation';
import { getCurrentUser } from '../../../../../lib/auth';
import { prisma } from '../../../../../lib/prisma';
import PelatihanForm from '../../form';

export default async function EditPelatihanPage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = await props.params;
  const user = await getCurrentUser();
 
  if (!user) {
    redirect('/login');
  }
 
  // Fetch pelatihan data with jenjangTargets
  const pelatihan = await prisma.pelatihan.findUnique({
    where: { id: params.id },
    include: {
      jenjangTargets: {
        select: {
          id: true,
          jenjang: true,
          target_peserta: true
        }
      }
    }
  });
 
  if (!pelatihan) {
    notFound();
  }

  // Format dates for form inputs (YYYY-MM-DD format)
  const formattedPelatihan = {
    ...pelatihan,
    tgl_mulai: pelatihan.tgl_mulai.toISOString().split('T')[0],
    tgl_berakhir: pelatihan.tgl_berakhir.toISOString().split('T')[0],
  };
 
  return (
    <div className="container px-4 py-4 mx-auto sm:px-6 lg:px-8">
      <div className="mb-4 sm:mb-6">
        <h1 className="text-xl font-semibold sm:text-2xl">Edit Kegiatan</h1>
      </div>
     
      <div className="overflow-hidden bg-white rounded-lg shadow">
        <div className="p-4 sm:p-6">
          <PelatihanForm pelatihan={formattedPelatihan} />
        </div>
      </div>
    </div>
  );
}