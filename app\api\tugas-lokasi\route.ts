import { NextResponse, NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

// GET /api/tugas-lokasi - Mendapatkan semua lokasi tugas
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    const { searchParams } = new URL(request.url);
    const isForDropdown = searchParams.get('dropdown') === 'true';

    // If not for dropdown, only admins can access the location list
    if (!isForDropdown && (!currentUser || currentUser.role !== 'ADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // For dropdown list or admin management
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const skip = (page - 1) * pageSize;

    // Build where condition
    const where = search ? { kab_kota: { contains: search } } : {};

    // If for dropdown (simple list), don't apply pagination
    if (isForDropdown) {
      const locations = await prisma.t_tugas.findMany({
        orderBy: { kab_kota: 'asc' },
      });

      return NextResponse.json(locations);
    }

    // For admin management with pagination
    const locations = await prisma.t_tugas.findMany({
      where,
      orderBy: { kab_kota: 'asc' },
      skip,
      take: pageSize,
    });

    // Get total count for pagination
    const totalLocations = await prisma.t_tugas.count({ where });

    return NextResponse.json({
      locations,
      pagination: {
        currentPage: page,
        pageSize,
        totalPages: Math.ceil(totalLocations / pageSize),
        totalItems: totalLocations,
      },
    });
  } catch (error) {
    console.error('Error fetching locations:', error);
    return NextResponse.json(
      { error: 'Gagal mengambil data lokasi tugas' },
      { status: 500 }
    );
  }
}

// POST /api/tugas-lokasi - Menambahkan lokasi tugas baru
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser();

    // Hanya admin yang boleh menambahkan lokasi tugas
    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { kab_kota } = body;

    // Validasi input
    if (!kab_kota) {
      return NextResponse.json(
        { error: 'Nama kabupaten/kota harus diisi' },
        { status: 400 }
      );
    }

    // Cek apakah lokasi dengan nama yang sama sudah ada menggunakan contains
    // (alternatif case-insensitive karena mode: 'insensitive' tidak didukung)
    const lowerCaseInput = kab_kota.toLowerCase();
    const existingLocations = await prisma.t_tugas.findMany();
    const duplicateLocation = existingLocations.find(
      (loc) => loc.kab_kota.toLowerCase() === lowerCaseInput
    );

    if (duplicateLocation) {
      return NextResponse.json(
        { error: 'Lokasi dengan nama yang sama sudah terdaftar' },
        { status: 409 }
      );
    }

    // Create new location
    const newLocation = await prisma.t_tugas.create({
      data: {
        kab_kota,
      },
    });

    return NextResponse.json(newLocation, { status: 201 });
  } catch (error) {
    console.error('Error creating location:', error);
    return NextResponse.json(
      { error: 'Gagal menambahkan lokasi tugas' },
      { status: 500 }
    );
  }
}