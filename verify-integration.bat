@echo off
echo ========================================
echo    IMAGEKIT INTEGRATION VERIFICATION
echo ========================================
echo.

echo [1/4] Checking Next.js Configuration...
if exist "next.config.js" (
    echo ✅ next.config.js found
) else (
    echo ❌ next.config.js missing
    goto :error
)

echo.
echo [2/4] Checking ImageKit Integration Files...
if exist "lib\imagekit.ts" (
    echo ✅ lib\imagekit.ts found
) else (
    echo ❌ lib\imagekit.ts missing
    goto :error
)

if exist "app\api\upload-imagekit\route.ts" (
    echo ✅ app\api\upload-imagekit\route.ts found
) else (
    echo ❌ ImageKit upload API missing
    goto :error
)

if exist "app\api\imagekit-auth\route.ts" (
    echo ✅ app\api\imagekit-auth\route.ts found
) else (
    echo ❌ ImageKit auth API missing
    goto :error
)

echo.
echo [3/4] Checking Enhanced Components...
if exist "components\PhotoUpload.tsx" (
    echo ✅ components\PhotoUpload.tsx found
) else (
    echo ❌ PhotoUpload component missing
    goto :error
)

if exist "components\OptimizedImage.tsx" (
    echo ✅ components\OptimizedImage.tsx found
) else (
    echo ❌ OptimizedImage component missing
    goto :error
)

if exist "components\BiodataExport.tsx" (
    echo ✅ components\BiodataExport.tsx found
) else (
    echo ❌ BiodataExport component missing
    goto :error
)

echo.
echo [4/4] Testing Build Process...
call npm run build > build-test.log 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Build successful
    del build-test.log 2>nul
) else (
    echo ❌ Build failed - check build-test.log
    goto :error
)

echo.
echo ========================================
echo        🎉 ALL CHECKS PASSED! 🎉
echo ========================================
echo.
echo Your ImageKit integration is complete and ready!
echo.
echo Next steps:
echo 1. Run: npm run dev
echo 2. Navigate to: http://localhost:3000/biodata  
echo 3. Test the photo upload functionality
echo.
echo The system includes:
echo ✓ ImageKit primary upload with optimization
echo ✓ Cloudinary backup upload
echo ✓ Local storage fallback
echo ✓ Responsive image delivery
echo ✓ Error handling and user feedback
echo.
goto :end

:error
echo.
echo ========================================
echo           ❌ VERIFICATION FAILED
echo ========================================
echo.
echo Please check the missing components above.
echo.

:end
pause
