import { getCurrentUser } from '../../lib/auth';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { prisma } from '../../lib/prisma';
import { getUserAccessScope } from '../../utils/helpers';
import { formatIndonesiaDate } from '@/utils/dateUtils';

export default async function DashboardContent() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/login');
  }

  // Mendapatkan access scope berdasarkan role user
  const { whereClause } = getUserAccessScope(user);
  const isAdmin = user.role === 'ADMIN';

  // Mengambil data statistik dari database dengan filter akses
  const totalPelatihan = await prisma.pelatihan.count({
    where: whereClause
  });
  
  // Untuk biodata dan absensi, kita perlu mengambil pelatihan-pelatihan milik user
  // jika user bukan admin
  let accessiblePelatihanIds: string[] = [];
  
  if (!isAdmin) {
    const userPelatihan = await prisma.pelatihan.findMany({
      where: whereClause,
      select: { id: true }
    });
    accessiblePelatihanIds = userPelatihan.map(p => p.id);
  }
  
  // Hitung biodata dan absensi yang terkait dengan pelatihan yang bisa diakses user
  const totalBiodata = await prisma.biodata.count({
    where: !isAdmin ? { pelatihanId: { in: accessiblePelatihanIds } } : {}
  });
  
  const totalAbsensi = await prisma.absensi.count({
    where: !isAdmin ? { pelatihanId: { in: accessiblePelatihanIds } } : {}
  });
  
  // Total users hanya ditampilkan full untuk admin
  const totalUsers = isAdmin 
    ? await prisma.user.count()
    : await prisma.user.count({ where: { id: user.id } });

  // Mengambil pelatihan terbaru dengan filter akses
  const pelatihanTerbaru = await prisma.pelatihan.findMany({
    where: whereClause,
    take: 5,
    orderBy: {
      createdAt: 'desc',
    },
    select: {
      id: true,
      nama: true,
      tgl_mulai: true,
      tgl_berakhir: true,
    },
  });

  // Menghitung progress dari pelatihan yang sedang berjalan
  const today = new Date();
  const pelatihanWithProgress = pelatihanTerbaru.map(pelatihan => {
    const start = new Date(pelatihan.tgl_mulai);
    const end = new Date(pelatihan.tgl_berakhir);
    
    // Determine status based on dates
    let status = 'Mendatang';
    if (today > end) {
      status = 'SELESAI';
    } else if (today >= start && today <= end) {
      status = 'BERJALAN';
    }
    
    // Calculate progress
    let progress = 0;
    if (status === 'SELESAI') {
      progress = 100;
    } else if (status === 'BERJALAN') {
      const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      const daysPassed = Math.ceil((today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
      progress = Math.min(Math.round((daysPassed / totalDays) * 100), 100);
    }
    
    return { ...pelatihan, progress, status };
  });
  // Format tanggal ke format yang lebih mudah dibaca
  function formatDate(date: Date): string {
    return formatIndonesiaDate(date);
  }
  
  return (
    <div>
      {/* Card Statistics */}
      <div className="grid grid-cols-1 gap-4 mb-8 sm:grid-cols-2 lg:grid-cols-4">
        <div className="p-6 bg-white rounded-lg shadow">
          <p className="text-sm font-medium text-gray-500">Total Kegiatan</p>
          <p className="text-3xl font-bold text-gray-900">{totalPelatihan}</p>
          <p className="mt-2 text-xs text-gray-500">
            <Link href="/dashboard/pelatihan" className="text-blue-600 hover:underline">
              Lihat semua kegiatan →
            </Link>
          </p>
        </div>
        
        <div className="p-6 bg-white rounded-lg shadow">
          <p className="text-sm font-medium text-gray-500">Total Biodata</p>
          <p className="text-3xl font-bold text-gray-900">{totalBiodata}</p>
          <p className="mt-2 text-xs text-gray-500">
            <Link href="/dashboard/biodata" className="text-blue-600 hover:underline">
              Lihat semua biodata →
            </Link>
          </p>
        </div>
        
        <div className="p-6 bg-white rounded-lg shadow">
          <p className="text-sm font-medium text-gray-500">Total Absensi</p>
          <p className="text-3xl font-bold text-gray-900">{totalAbsensi}</p>
          <p className="mt-2 text-xs text-gray-500">
            <Link href="/dashboard/absensi" className="text-blue-600 hover:underline">
              Lihat semua absensi →
            </Link>
          </p>
        </div>
        
        <div className="p-6 bg-white rounded-lg shadow">
          <p className="text-sm font-medium text-gray-500">Total Pengguna</p>
          <p className="text-3xl font-bold text-gray-900">{totalUsers}</p>
          <p className="mt-2 text-xs text-gray-500">
            {user.role === 'ADMIN' && (
              <Link href="/dashboard/users" className="text-blue-600 hover:underline">
                Kelola pengguna →
              </Link>
            )}
          </p>
        </div>
      </div>

      {/* Tabel Pelatihan */}
      <div className="p-6 mb-8 bg-white rounded-lg shadow">
        <h2 className="mb-4 text-lg font-medium text-gray-900">Kegiatan Terbaru</h2>
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead className="text-xs font-medium text-gray-500 uppercase bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left">Nama Kegiatan</th>
                <th className="px-4 py-3 text-left">Tanggal</th>
                <th className="px-4 py-3 text-left">Status</th>
                <th className="px-4 py-3 text-left">Progress</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {pelatihanWithProgress.map((pelatihan) => (
                <tr key={pelatihan.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4">
                    <Link href={`/dashboard/pelatihan/${pelatihan.id}`} className="text-blue-600 hover:underline">
                      {pelatihan.nama}
                    </Link>
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {formatDate(pelatihan.tgl_mulai)} - {formatDate(pelatihan.tgl_berakhir)}
                  </td>
                  <td className="px-4 py-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      pelatihan.status === 'SELESAI' 
                        ? 'bg-green-100 text-green-800' 
                        : pelatihan.status === 'BERJALAN' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {pelatihan.status}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div 
                        className="bg-blue-600 h-2.5 rounded-full" 
                        style={{ width: `${pelatihan.progress}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500">{pelatihan.progress}%</span>
                  </td>
                </tr>
              ))}
              {pelatihanWithProgress.length === 0 && (
                <tr>
                  <td colSpan={4} className="px-4 py-4 text-center text-gray-500">
                    Belum ada kegiatan yang ditambahkan
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        <div className="mt-4 text-right">
          <Link href="/dashboard/pelatihan" className="text-sm text-blue-600 hover:underline">
            Lihat semua kegiatan →
          </Link>
        </div>
      </div>

   
    </div>
  );
}
