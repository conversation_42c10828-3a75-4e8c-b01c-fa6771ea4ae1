// File: /app/dashboard/biodata/[id]/page.tsx
import Link from 'next/link';
import Image from 'next/image';
import { notFound, redirect } from 'next/navigation';
import { getCurrentUser } from '../../../../lib/auth';
import { prisma } from '../../../../lib/prisma';
import { formatDate } from '../../../../utils/helpers';
import { checkBiodataAccessPermission } from '../../../../utils/biodataHelpers';
import { Metadata } from 'next';
import Card from '../../../../components/Card';

export const metadata: Metadata = {
  title: 'Detail Peserta',
  description: ''
};

export default async function BiodataDetailPage(
  props: {
    params: Promise<{ id: string }>;
  }
) {
  const params = await props.params;
  const user = await getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  try {
    // Check if user has permission to view this biodata
    const hasAccess = await checkBiodataAccessPermission(prisma, user.id, params.id);
    
    if (!hasAccess) {
      notFound();
    }

    // Fetch biodata with optimized query (select only needed fields)
    const biodata = await prisma.biodata.findUnique({
      where: { id: params.id },
      include: {
        pelatihan: {
          select: {
            nama: true,
            tempat: true,
            tgl_mulai: true,
            tgl_berakhir: true
          }
        },
      },
    });

    if (!biodata) {
      notFound();
    }

    return (
      <div className="container px-4 py-4 mx-auto sm:px-6 lg:px-8">
        <div className="flex flex-col items-start justify-between gap-4 mb-4 sm:mb-6 sm:flex-row sm:items-center">
          <h1 className="text-xl font-semibold sm:text-2xl">Detail Peserta</h1>
          <div className="flex flex-col w-full gap-2 sm:w-auto sm:flex-row">
            <Link
              href="/dashboard/biodata"
              className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors w-full sm:w-auto text-center"
            >
              Kembali
            </Link>
          </div>
        </div>        <Card className="mb-4 sm:mb-6">
          <div className="p-4 sm:p-6">
            <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Informasi Peserta</h2>            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
              {/* Foto Peserta */}
              <div className="sm:col-span-2 flex justify-center mb-4">
                <div className="relative w-32 h-32 overflow-hidden rounded-full border-2 border-gray-200 shadow-sm bg-gray-50">
                  {biodata.foto_path ? (
                    <Image
                      src={biodata.foto_path}
                      alt={`Foto ${biodata.nama}`}
                      width={128}
                      height={128}
                      className="object-cover w-full h-full"
                      priority={false}
                    />
                  ) : (
                    <div className="flex items-center justify-center w-full h-full bg-gray-100">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Nama Lengkap</p>
                <p className="text-sm font-medium break-words sm:text-base">{biodata.nama}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Email</p>
                <p className="text-sm font-medium break-all sm:text-base">{biodata.email}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Nomor HP</p>
                <p className="text-sm font-medium sm:text-base">{biodata.no_hp}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Tempat, Tanggal Lahir</p>
                <p className="text-sm font-medium sm:text-base">
                  {biodata.tempat_lahir}, {formatDate(biodata.tanggal_lahir)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Jenis Kelamin</p>
                <p className="text-sm font-medium sm:text-base">{biodata.jenis_kelamin}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Pendidikan</p>
                <p className="text-sm font-medium sm:text-base">{biodata.pendidikan}</p>
              </div>
              {biodata.nip && (
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">NIP</p>
                  <p className="text-sm font-medium break-words sm:text-base">{biodata.nip}</p>
                </div>
              )}
              {biodata.pangkat_golongan && (
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Pangkat/Golongan</p>
                  <p className="text-sm font-medium sm:text-base">{biodata.pangkat_golongan}</p>
                </div>
              )}
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Jabatan</p>
                <p className="text-sm font-medium break-words sm:text-base">{biodata.jabatan}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Unit Kerja</p>
                <p className="text-sm font-medium break-words sm:text-base">{biodata.unit_kerja}</p>
              </div>
              <div className="sm:col-span-2">
                <p className="text-xs text-gray-500 sm:text-sm">Alamat Unit Kerja</p>
                <p className="text-sm font-medium break-words sm:text-base">{biodata.alamat_unit_kerja}</p>
              </div>
              {biodata.npwp && (
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">NPWP</p>
                  <p className="text-sm font-medium break-words sm:text-base">{biodata.npwp}</p>
                </div>
              )}
            </div>
          </div>
        </Card>

        <Card className="mb-4 sm:mb-6">
          <div className="p-4 sm:p-6">
            <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Informasi Kegiatan</h2>
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Nama Kegiatan</p>
                <p className="text-sm font-medium break-words sm:text-base">{biodata.pelatihan.nama}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Tempat</p>
                <p className="text-sm font-medium break-words sm:text-base">{biodata.pelatihan.tempat}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Tanggal Pelatihan</p>
                <p className="text-sm font-medium sm:text-base">
                  {formatDate(biodata.pelatihan.tgl_mulai)} - {formatDate(biodata.pelatihan.tgl_berakhir)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 sm:text-sm">Tanggal Pendaftaran</p>
                <p className="text-sm font-medium sm:text-base">{formatDate(biodata.createdAt)}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-4 sm:p-6">
            <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Tanda Tangan</h2>
            <div className="p-2 overflow-auto border border-gray-300 rounded-md sm:p-4">
              <Image
                src={biodata.tanda_tangan}
                alt="Tanda Tangan"
                className="mx-auto"
                width={300}
                height={100}
                style={{ height: 'auto', maxWidth: '100%' }}
                priority={false}
              />
            </div>
          </div>
        </Card>
      </div>
    );  } catch (_error) {
    return (
      <div className="container px-4 py-4 mx-auto sm:px-6 lg:px-8">
        <div className="flex flex-col items-start justify-between gap-4 mb-4 sm:mb-6 sm:flex-row sm:items-center">
          <h1 className="text-xl font-semibold sm:text-2xl">Detail Peserta</h1>
          <Link
            href="/dashboard/biodata"
            className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors w-full sm:w-auto text-center"
          >
            Kembali
          </Link>
        </div>
        
        <Card>
          <div className="p-4 my-4 text-red-700 bg-red-100 border border-red-300 rounded-md">
            <p>Terjadi kesalahan saat mengambil data detail peserta. Silakan coba lagi nanti.</p>
          </div>
        </Card>
      </div>
    );
  }
}