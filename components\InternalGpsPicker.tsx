'use client';

import { useState, useEffect, useRef } from 'react';
import { AntiGpsSpoofing, type LocationReading, type SpoofingAnalysis } from '../lib/anti-gps-spoofing';

interface InternalGpsPickerProps {
  onLocationSelect: (lat: number, lng: number, address: string) => void;
  onValidationChange: (validation: {
    isValid: boolean;
    riskScore: number;
    warnings: string[];
    analysis?: SpoofingAnalysis;
  }) => void;
}

export default function InternalGpsPicker({ onLocationSelect, onValidationChange }: InternalGpsPickerProps) {
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [address, setAddress] = useState<string>('');
  const [gpsStatus, setGpsStatus] = useState<'idle' | 'searching' | 'found' | 'error'>('idle');
  const [spoofingAnalysis, setSpoofingAnalysis] = useState<SpoofingAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  
  const antiSpoofingRef = useRef<AntiGpsSpoofing>(new AntiGpsSpoofing(10));
  const watchIdRef = useRef<number | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<google.maps.Marker | null>(null);

  useEffect(() => {
    // Load Google Maps
    if (typeof window !== 'undefined' && !window.google) {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = initializeMap;
      document.head.appendChild(script);
    } else if (window.google) {
      initializeMap();
    }

    return () => {
      stopLocationTracking();
    };
  }, []);

  const initializeMap = () => {
    if (!window.google) return;

    const mapElement = document.getElementById('internal-gps-map');
    if (!mapElement) return;

    // Default to Indonesia center
    const defaultCenter = { lat: -2.5489, lng: 118.0149 };
    
    mapRef.current = new google.maps.Map(mapElement, {
      zoom: 5,
      center: defaultCenter,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
    });
  };

  const startLocationTracking = () => {
    if (!navigator.geolocation) {
      setErrorMessage('GPS tidak didukung oleh browser ini');
      setGpsStatus('error');
      return;
    }

    setGpsStatus('searching');
    setErrorMessage('');
    setIsAnalyzing(true);

    // Clear previous analysis
    antiSpoofingRef.current.clearHistory();

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 5000
    };

    // Get initial position
    navigator.geolocation.getCurrentPosition(
      handleLocationSuccess,
      handleLocationError,
      options
    );

    // Start watching position for continuous validation
    watchIdRef.current = navigator.geolocation.watchPosition(
      handleLocationSuccess,
      handleLocationError,
      {
        ...options,
        timeout: 30000, // Longer timeout for watch
        maximumAge: 10000 // Allow slightly older readings for watch
      }
    );
  };

  const handleLocationSuccess = async (position: GeolocationPosition) => {
    const { latitude, longitude, accuracy } = position.coords;
    const timestamp = position.timestamp;

    // Create location reading for analysis
    const locationReading: LocationReading = {
      latitude,
      longitude,
      accuracy,
      timestamp,
      speed: position.coords.speed || undefined,
      heading: position.coords.heading || undefined,
      altitude: position.coords.altitude || undefined
    };

    // Analyze for GPS spoofing
    const analysis = antiSpoofingRef.current.analyzeLocation(locationReading);
    setSpoofingAnalysis(analysis);

    // Update validation status
    onValidationChange({
      isValid: analysis.isGenuine && analysis.riskScore < 70,
      riskScore: analysis.riskScore,
      warnings: analysis.warnings,
      analysis
    });

    // Update location state
    setCurrentLocation({ lat: latitude, lng: longitude });
    setGpsStatus('found');

    // Update map
    if (mapRef.current) {
      const position = { lat: latitude, lng: longitude };
      mapRef.current.setCenter(position);
      mapRef.current.setZoom(16);

      // Update marker
      if (markerRef.current) {
        markerRef.current.setPosition(position);
      } else {
        markerRef.current = new google.maps.Marker({
          position,
          map: mapRef.current,
          title: 'Lokasi Saat Ini',
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="8" fill="${analysis.riskLevel === 'LOW' ? '#10B981' : 
                  analysis.riskLevel === 'MEDIUM' ? '#F59E0B' : 
                  analysis.riskLevel === 'HIGH' ? '#EF4444' : '#DC2626'}" stroke="white" stroke-width="2"/>
                <circle cx="12" cy="12" r="3" fill="white"/>
              </svg>
            `),
            scaledSize: new google.maps.Size(24, 24),
            anchor: new google.maps.Point(12, 12)
          }
        });
      }

      // Add accuracy circle
      new google.maps.Circle({
        map: mapRef.current,
        center: position,
        radius: accuracy,
        fillColor: analysis.riskLevel === 'LOW' ? '#10B981' : 
                   analysis.riskLevel === 'MEDIUM' ? '#F59E0B' : '#EF4444',
        fillOpacity: 0.1,
        strokeColor: analysis.riskLevel === 'LOW' ? '#10B981' : 
                     analysis.riskLevel === 'MEDIUM' ? '#F59E0B' : '#EF4444',
        strokeOpacity: 0.3,
        strokeWeight: 1
      });
    }

    // Get address
    try {
      const geocoder = new google.maps.Geocoder();
      const response = await new Promise<google.maps.GeocoderResponse>((resolve, reject) => {
        geocoder.geocode({ location: { lat: latitude, lng: longitude } }, (results, status) => {
          if (status === 'OK' && results) {
            resolve({ results } as google.maps.GeocoderResponse);
          } else {
            reject(new Error('Geocoding failed'));
          }
        });
      });

      if (response.results && response.results[0]) {
        const addr = response.results[0].formatted_address;
        setAddress(addr);
        onLocationSelect(latitude, longitude, addr);
      }
    } catch (error) {
      console.error('Error getting address:', error);
      setAddress(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
      onLocationSelect(latitude, longitude, `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
    }

    setIsAnalyzing(false);
  };

  const handleLocationError = (error: GeolocationPositionError) => {
    let message = 'Gagal mendapatkan lokasi GPS';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        message = 'Akses GPS ditolak. Silakan aktifkan izin lokasi.';
        break;
      case error.POSITION_UNAVAILABLE:
        message = 'GPS tidak tersedia. Pastikan GPS aktif.';
        break;
      case error.TIMEOUT:
        message = 'Timeout mendapatkan GPS. Coba lagi.';
        break;
    }

    setErrorMessage(message);
    setGpsStatus('error');
    setIsAnalyzing(false);
  };

  const stopLocationTracking = () => {
    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
    }
  };

  const retryGPS = () => {
    stopLocationTracking();
    startLocationTracking();
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return 'text-green-600 bg-green-50 border-green-200';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'HIGH': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'CRITICAL': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return '✅';
      case 'MEDIUM': return '⚠️';
      case 'HIGH': return '🔶';
      case 'CRITICAL': return '🚫';
      default: return '❓';
    }
  };

  return (
    <div className="w-full space-y-4">
      {/* Control Buttons */}
      <div className="flex justify-center space-x-3">
        {gpsStatus === 'idle' && (
          <button
            onClick={startLocationTracking}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            📍 Dapatkan Lokasi GPS
          </button>
        )}
        
        {gpsStatus === 'searching' && (
          <div className="flex items-center space-x-2 px-4 py-2 bg-blue-50 text-blue-700 rounded-lg">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span>Mencari GPS...</span>
          </div>
        )}
        
        {(gpsStatus === 'found' || gpsStatus === 'error') && (
          <button
            onClick={retryGPS}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            🔄 Coba Lagi
          </button>
        )}
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">❌ {errorMessage}</p>
        </div>
      )}

      {/* GPS Analysis Results */}
      {spoofingAnalysis && (
        <div className={`p-4 border rounded-lg ${getRiskColor(spoofingAnalysis.riskLevel)}`}>
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold flex items-center space-x-2">
              <span>{getRiskIcon(spoofingAnalysis.riskLevel)}</span>
              <span>Status GPS: {spoofingAnalysis.riskLevel === 'LOW' ? 'Aman' : 
                                spoofingAnalysis.riskLevel === 'MEDIUM' ? 'Perhatian' :
                                spoofingAnalysis.riskLevel === 'HIGH' ? 'Berisiko' : 'Bahaya'}</span>
            </h3>
            <span className="text-sm font-mono">
              Risk: {spoofingAnalysis.riskScore}/100
            </span>
          </div>

          {/* Detection Flags */}
          <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
            <div className="flex items-center space-x-1">
              <span>{spoofingAnalysis.detectionFlags.accuracyNormal ? '✅' : '❌'}</span>
              <span>Akurasi Normal</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>{spoofingAnalysis.detectionFlags.coordinatePrecision ? '✅' : '❌'}</span>
              <span>Presisi Koordinat</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>{spoofingAnalysis.detectionFlags.speedConsistent ? '✅' : '❌'}</span>
              <span>Kecepatan Konsisten</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>{spoofingAnalysis.detectionFlags.timestampValid ? '✅' : '❌'}</span>
              <span>Timestamp Valid</span>
            </div>
          </div>

          {/* Warnings */}
          {spoofingAnalysis.warnings.length > 0 && (
            <div className="mb-3">
              <p className="text-sm font-medium mb-1">Peringatan:</p>
              <ul className="text-xs space-y-1">
                {spoofingAnalysis.warnings.map((warning, index) => (
                  <li key={index} className="flex items-start space-x-1">
                    <span>•</span>
                    <span>{warning}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Recommendations */}
          {spoofingAnalysis.recommendations.length > 0 && (
            <div>
              <p className="text-sm font-medium mb-1">Rekomendasi:</p>
              <ul className="text-xs space-y-1">
                {spoofingAnalysis.recommendations.map((rec, index) => (
                  <li key={index} className="flex items-start space-x-1">
                    <span>•</span>
                    <span>{rec}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Map */}
      <div className="h-[300px] bg-gray-100 rounded-lg overflow-hidden border">
        <div id="internal-gps-map" className="w-full h-full"></div>
      </div>

      {/* Location Info */}
      {currentLocation && address && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-700 text-sm">
            <span className="font-medium">📍 Lokasi:</span> {address}
          </p>
          <p className="text-green-600 text-xs mt-1">
            Koordinat: {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
          </p>
        </div>
      )}

      {/* Analysis Status */}
      {isAnalyzing && (
        <div className="flex items-center justify-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-blue-700 text-sm">Menganalisis keamanan GPS...</span>
        </div>
      )}

      {/* Help Text */}
      <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-gray-600 text-xs">
          💡 <strong>Untuk Absensi Internal:</strong> Sistem akan memvalidasi keaslian GPS Anda tanpa membatasi lokasi. 
          Pastikan GPS aktif dan tidak menggunakan aplikasi pemalsuan lokasi.
        </p>
      </div>
    </div>
  );
}
