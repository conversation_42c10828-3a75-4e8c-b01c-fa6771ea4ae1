'use client';

import { CSRF } from '../lib/csrf';

/**
 * Fetches the CSRF token from the server
 * @returns Promise<string> - The CSRF token
 */
export async function getCsrfToken(): Promise<string> {
  try {
    // Get existing token from cookie if available
    const cookies = document.cookie.split(';');
    const csrfCookie = cookies
      .find(cookie => cookie.trim().startsWith(`${CSRF.cookieName}=`));
    
    if (csrfCookie) {
      const token = csrfCookie.split('=')[1];
      if (token) return token;
    }
    
    // If no token in cookies, request a new one
    const response = await fetch('/api/auth/csrf', {
      method: 'GET',
      credentials: 'include',
    });
    
    if (!response.ok) {
      throw new Error('Failed to get CSRF token');
    }
    
    const data = await response.json();
    return data.csrfToken;  } catch (error) {
    throw error;
  }
}

/**
 * Performs a fetch request with CSRF protection
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns Promise<Response> - The fetch response
 */
export async function csrfFetch(url: string, options: RequestInit = {}): Promise<Response> {
  try {
    // Only add CSRF for non-GET methods
    if (options.method && options.method !== 'GET') {
      const csrfToken = await getCsrfToken();
      
      // Create new headers object
      const headers = new Headers(options.headers || {});
      
      // Add CSRF token to headers
      headers.set(CSRF.headerName, csrfToken);
      
      // If JSON body, add CSRF token to body as well
      if (headers.get('Content-Type') === 'application/json' && options.body) {
        try {
          const bodyObj = JSON.parse(options.body.toString());
          bodyObj[CSRF.formFieldName] = csrfToken;
          options.body = JSON.stringify(bodyObj);
        } catch (_e) {
          // Not a JSON body, continue
        }
      }
      
      // If FormData, add CSRF token to FormData
      if (options.body instanceof FormData) {
        options.body.append(CSRF.formFieldName, csrfToken);
      }
      
      options.headers = headers;
    }
    
    return fetch(url, {
      ...options,
      credentials: 'include', // Always send cookies
    });  } catch (error) {
    throw error;
  }
}

export default csrfFetch;