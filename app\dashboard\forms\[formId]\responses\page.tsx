'use client'
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { ArrowLeft, Download, FileText, FileSpreadsheet, FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import ResponseAnalysis from '@/components/ResponseAnalysis';
import { formatDate } from '@/utils/helpers';
import ExcelJS from 'exceljs';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import {  isValidElement } from 'react';
import html2canvas from 'html2canvas';

// Type definitions
type FormSubmission = {
  id: string;
  formId: string;
  respondentInfo: any;
  submittedAt: string;
  answers: Array<{
    id: string;
    questionId: string;
    answerValue?: string;
    answerJson?: any;
    fileUrl?: string;
    question: {
      questionText: string;
      questionType: string;
    };
  }>;
};

type Form = {
  id: string;
  title: string;
  description?: string;
  questions: Array<{
    id: string;
    questionText: string;
    questionType: string;
    isRequired: boolean;
    order: number;
    options?: {
      choices?: string[];
    };
  }>;
};

// Component that safely processes params
function FormResponsesContent({ formId }: { formId: string }) {
  const router = useRouter();
  const [form, setForm] = useState<Form | null>(null);
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('summary');
  const [exportLoading, setExportLoading] = useState<{ excel: boolean; pdf: boolean }>({ excel: false, pdf: false });
  const chartRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const chartInstances = useRef<{ [key: string]: any }>({}); // Simpan instance Chart.js

  useEffect(() => {
    const fetchData = async () => {
      try {
        const formResponse = await fetch(`/api/forms/${formId}`);

        if (!formResponse.ok) {
          const errorText = await formResponse.text();
          console.error(`Form fetch failed: ${formResponse.status}`, errorText);
          throw new Error(`Failed to fetch form: ${formResponse.status}`);
        }

        const formData = await formResponse.json();
        setForm(formData);

        const submissionsResponse = await fetch(`/api/forms/${formId}/submissions`);

        if (!submissionsResponse.ok) {
          const errorText = await submissionsResponse.text();
          console.error(`Submissions fetch failed: ${submissionsResponse.status}`, errorText);
          throw new Error(`Failed to fetch submissions: ${submissionsResponse.status}`);
        }

        const submissionsData = await submissionsResponse.json();
        setSubmissions(submissionsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setForm(null);
        setSubmissions([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [formId]);

  // Process form data to get statistics per question for the summary
  const getSummaryData = () => {
    if (!form || !submissions.length) return {};

    const summary: Record<string, any> = {
      formTitle: form.title,
      totalSubmissions: submissions.length,
      lastSubmissionDate: submissions[0].submittedAt,
      questions: {}
    };

    // Process each question
    form.questions.forEach(question => {
      const questionData: Record<string, any> = {
        text: question.questionText,
        type: question.questionType,
        totalAnswers: 0
      };

      // For choice questions, count responses per option
      if (['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'DROPDOWN', 'CHECKLIST'].includes(question.questionType)) {
        const choices = question.options?.choices || [];
        const choiceCounts: Record<string, number> = {};
        
        // Initialize counts
        choices.forEach(choice => {
          choiceCounts[choice] = 0;
        });

        // Count answers
        submissions.forEach(submission => {
          const answer = submission.answers.find(a => a.questionId === question.id);
          if (!answer) return;
          
          if (question.questionType === 'MULTIPLE_CHOICE' || question.questionType === 'CHECKLIST') {
            try {
              let selectedChoices: string[] = [];
              
              // Parse from answerValue or answerJson
              if (answer.answerValue) {
                if (typeof answer.answerValue === 'string') {
                  try {
                    const parsed = JSON.parse(answer.answerValue);
                    if (Array.isArray(parsed)) {
                      selectedChoices = parsed;
                    } else {
                      selectedChoices = [answer.answerValue];
                    }
                  } catch (_e) {
                    selectedChoices = [answer.answerValue];
                  }
                }
              } else if (answer.answerJson) {
                if (Array.isArray(answer.answerJson)) {
                  selectedChoices = answer.answerJson;
                } else if (typeof answer.answerJson === 'object' && answer.answerJson !== null) {
                  // Format {index: boolean} for CHECKLIST
                  Object.entries(answer.answerJson).forEach(([index, checked]) => {
                    if (checked && choices[parseInt(index)]) {
                      selectedChoices.push(choices[parseInt(index)]);
                    }
                  });
                }
              }
              
              // Count each selected choice
              selectedChoices.forEach(choice => {
                if (choiceCounts[choice] !== undefined) {
                  choiceCounts[choice]++;
                  questionData.totalAnswers++;
                }
              });
            } catch (_e) {
              console.error('Error processing multiple choice answer:', _e);
            }
          } else {
            // For SINGLE_CHOICE, DROPDOWN, and other formats
            if (answer.answerValue && choiceCounts[answer.answerValue] !== undefined) {
              choiceCounts[answer.answerValue]++;
              questionData.totalAnswers++;
            }
          }
        });

        questionData.choices = choiceCounts;
      } else {
        // For text, number, and other question types
        questionData.answers = submissions
          .map(submission => {
            const answer = submission.answers.find(a => a.questionId === question.id);
            return answer?.answerValue || '';
          })
          .filter(Boolean);
        
        questionData.totalAnswers = questionData.answers.length;
      }

      summary.questions[question.id] = questionData;
    });

    return summary;
  };

  // Export summary data to PDF
  const exportToPdf = async () => {
    if (!form || !submissions.length) return;
    
    try {
      setExportLoading(prev => ({ ...prev, pdf: true }));
      const summaryData = getSummaryData();
      const pdfDoc = await PDFDocument.create();
      let currentPage = pdfDoc.addPage([595.28, 841.89]); // A4 size
      const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
      const { height } = currentPage.getSize();
      let yPosition = height - 50;
      const margin = 50;
      currentPage.drawText(`Ringkasan Respons: ${form.title}`, {
        x: margin,
        y: yPosition,
        size: 16,
        font: helveticaBold,
      });
      yPosition -= 30;
      currentPage.drawText(`Total Respons: ${submissions.length}`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: helveticaFont,
      });
      yPosition -= 20;
      currentPage.drawText(`Respons Terakhir: ${formatDate(submissions[0].submittedAt)}`, {
        x: margin,
        y: yPosition,
        size: 12,
        font: helveticaFont,
      });
      yPosition -= 40;
      for (const questionId in summaryData.questions) {
        const question = summaryData.questions[questionId];
        if (yPosition < 200) {
          currentPage = pdfDoc.addPage([595.28, 841.89]);
          yPosition = height - 50;
        }
        currentPage.drawText(question.text, {
          x: margin,
          y: yPosition,
          size: 14,
          font: helveticaBold,
        });
        yPosition -= 25;
        currentPage.drawText(`Tipe: ${question.type.replace(/_/g, ' ')}`, {
          x: margin,
          y: yPosition,
          size: 10,
          font: helveticaFont,
        });
        yPosition -= 15;
        currentPage.drawText(`Dijawab: ${question.totalAnswers} dari ${submissions.length} responden`, {
          x: margin,
          y: yPosition,
          size: 10,
          font: helveticaFont,
        });
        yPosition -= 25;
        if (['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'DROPDOWN', 'CHECKLIST'].includes(question.type) && 
            question.choices && Object.keys(question.choices).length > 0) {
          
          // Ambil instance Chart.js
          const chartInstance = chartInstances.current[questionId];
          let chartPng = null;
          const pdfWidth = 595.28 - margin * 2;
          let pdfHeight = 200;
          // Fallback 1: Chart.js toBase64Image
          if (chartInstance && typeof chartInstance.toBase64Image === 'function') {
            try {
              chartPng = chartInstance.toBase64Image();
              const aspect = chartInstance.height / chartInstance.width;
              pdfHeight = pdfWidth * aspect;
            } catch (_e) {
              chartPng = null;
            }
          }
          // Fallback 2: Chart.js canvas.toDataURL
          if (!chartPng && chartInstance && chartInstance.canvas) {
            try {
              chartPng = chartInstance.canvas.toDataURL('image/png');
              const aspect = chartInstance.height / chartInstance.width;
              pdfHeight = pdfWidth * aspect;
            } catch (_e) {
              chartPng = null;
            }
          }
          // Fallback 3: html2canvas pada chart container
          if (!chartPng && chartRefs.current[questionId]) {
            try {
              const canvas = await html2canvas(chartRefs.current[questionId], { backgroundColor: '#fff', scale: 2 });
              chartPng = canvas.toDataURL('image/png');
              const aspect = canvas.height / canvas.width;
              pdfHeight = pdfWidth * aspect;
            } catch (_e) {
              chartPng = null;
            }
          }
          if (chartPng) {
            try {
              const chartImage = await pdfDoc.embedPng(chartPng);
              if (yPosition - pdfHeight < margin) {
                currentPage = pdfDoc.addPage([595.28, 841.89]);
                yPosition = height - 50;
              }
              currentPage.drawImage(chartImage, {
                x: margin,
                y: yPosition - pdfHeight,
                width: pdfWidth,
                height: pdfHeight,
              });
              yPosition -= pdfHeight + 30;
            } catch (error) {
              console.error('Error embedding chart image to PDF:', error);
              currentPage.drawText('Error: Could not add chart to PDF', {
                x: margin,
                y: yPosition - 20,
                size: 10,
                font: helveticaFont,
                color: rgb(1, 0, 0),
              });
              yPosition -= 40;
            }
          } else {
            // Fallback: text data
            currentPage.drawText('Data Pilihan:', {
              x: margin,
              y: yPosition,
              size: 12,
              font: helveticaBold,
            });
            yPosition -= 20;
            Object.entries(question.choices).forEach(([choice, count]) => {
              const countValue = typeof count === 'number' ? count : 0;
              const total = Object.values(question.choices).reduce(
                (sum: number, c) => sum + (typeof c === 'number' ? c : 0),
                0
              );
              const percentage = total > 0 ? ((countValue / total) * 100).toFixed(1) : '0';
              if (yPosition < margin + 20) {
                currentPage = pdfDoc.addPage([595.28, 841.89]);
                yPosition = height - 50;
              }
              currentPage.drawText(`${choice}: ${countValue} (${percentage}%)`, {
                x: margin + 10,
                y: yPosition,
                size: 10,
                font: helveticaFont,
              });
              yPosition -= 15;
            });
            yPosition -= 20;
          }
        } else if (question.answers && question.answers.length > 0) {
          // For text, number, and other question types
          question.answers = submissions
            .map(submission => {
              const answer = submission.answers.find(a => a.questionId === question.id);
              return answer?.answerValue || '';
            })
            .filter(Boolean);
          question.totalAnswers = question.answers.length;
        }

        yPosition -= 20;
      }
      
      // Save the PDF
      const pdfBytes = await pdfDoc.save();
      
      // Create a blob and download
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${form.title}_respons.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Terjadi kesalahan saat mengekspor PDF');
    } finally {
      setExportLoading(prev => ({ ...prev, pdf: false }));
    }
  };
  
  // Export summary data to Excel
  const exportToExcel = async () => {
    if (!form || !submissions.length) return;
    
    try {
      setExportLoading(prev => ({ ...prev, excel: true }));
      const summaryData = getSummaryData();
      
      // Create a new workbook
      const workbook = new ExcelJS.Workbook();
      workbook.creator = 'Form System';
      workbook.lastModifiedBy = 'Form System';
      workbook.created = new Date();
      workbook.modified = new Date();
      
      // Add summary sheet
      const summarySheet = workbook.addWorksheet('Ringkasan');
      
      // Add title and basic info
      summarySheet.addRow(['Ringkasan Respons Form']);
      summarySheet.addRow(['Judul Form', form.title]);
      summarySheet.addRow(['Total Respons', submissions.length.toString()]);
      summarySheet.addRow(['Respons Terakhir', formatDate(submissions[0].submittedAt)]);
      summarySheet.addRow(['']);
      
      // Add question summaries
      Object.values(summaryData.questions).forEach((question: any) => {
        summarySheet.addRow(['']);
        summarySheet.addRow(['Pertanyaan', question.text]);
        summarySheet.addRow(['Tipe', question.type.replace(/_/g, ' ')]);
        summarySheet.addRow(['Dijawab', `${question.totalAnswers} dari ${submissions.length} responden`]);
        
        if (question.choices) {
          summarySheet.addRow(['Pilihan', 'Jumlah Respons', 'Persentase']);
          
          const values = Object.values(question.choices) as any[];
          const total: number = values.reduce((sum: number, count: any) => {
            return sum + (typeof count === 'number' ? count : 0);
          }, 0);
          
          // Add each choice with its count and percentage
          Object.entries(question.choices).forEach(([choice, count]: [string, any]) => {
            const countValue = typeof count === 'number' ? count : 0;
            const percentage = total > 0 ? ((countValue / total) * 100).toFixed(1) : '0';
            summarySheet.addRow([choice, countValue, `${percentage}%`]);
          });
          
          // Add note about charts
          summarySheet.addRow(['']);
          summarySheet.addRow(['Note: Charts are available in the PDF export']);
        }
        
        summarySheet.addRow(['']);
      });
      
      // Add detailed responses sheet
      const detailedSheet = workbook.addWorksheet('Respons Detail');
      
      // Create headers
      const detailedHeaders = ['ID', 'Tanggal Pengisian'];
      
      // Add respondent info headers
      if (submissions[0]?.respondentInfo) {
        Object.keys(submissions[0].respondentInfo).forEach(key => {
          detailedHeaders.push(key);
        });
      }
      
      // Add question headers
      form.questions.forEach(question => {
        detailedHeaders.push(question.questionText);
      });
      
      detailedSheet.addRow(detailedHeaders);
      
      // Add data rows
      submissions.forEach(submission => {
        const row = [
          submission.id,
          formatDate(submission.submittedAt)
        ];
        
        // Add respondent info
        if (submission.respondentInfo) {
          Object.keys(submissions[0].respondentInfo).forEach(key => {
            row.push(submission.respondentInfo[key] || '');
          });
        }
        
        // Add answers
        form.questions.forEach(question => {
          const answer = submission.answers.find(a => a.questionId === question.id);
          if (answer) {
            if (answer.answerValue) {
              // For multiple choice, try to parse the JSON
              if (question.questionType === 'MULTIPLE_CHOICE') {
                try {
                  const parsed = JSON.parse(answer.answerValue);
                  if (Array.isArray(parsed)) {
                    row.push(parsed.join(', '));
                  } else {
                    row.push(answer.answerValue);
                  }
                } catch {
                  row.push(answer.answerValue);
                }
              } else {
                row.push(answer.answerValue);
              }
            } else if (answer.fileUrl) {
              row.push(answer.fileUrl);
            } else {
              row.push('');
            }
          } else {
            row.push('');
          }
        });
        
        detailedSheet.addRow(row);
      });
      
      // Apply some styling
      summarySheet.getColumn(1).width = 20;
      summarySheet.getColumn(2).width = 40;
      detailedSheet.columns.forEach(column => {
        column.width = 20;
      });
      
      // Generate and download the file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${form.title}_respons.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error exporting Excel:', error);
      alert('Terjadi kesalahan saat mengekspor Excel');
    } finally {
      setExportLoading(prev => ({ ...prev, excel: false }));
    }
  };

  const exportToCSV = async () => {
    if (!form || !submissions.length) return;

    try {
      const summaryData = getSummaryData();

      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      csvContent += 'Ringkasan Respons Form\n';
      csvContent += `Judul Form,${form.title}\n`;
      csvContent += `Total Respons,${submissions.length}\n`;
      csvContent += `Respons Terakhir,${formatDate(submissions[0].submittedAt)}\n\n`;

      // Add question summaries to CSV
      Object.values(summaryData.questions).forEach((question: any) => {
        csvContent += `Pertanyaan,${question.text}\n`;
        csvContent += `Tipe,${question.type.replace(/_/g, ' ')}\n`;
        csvContent += `Dijawab,${question.totalAnswers} dari ${submissions.length} responden\n`;

        if (question.choices) {
          csvContent += 'Pilihan,Jumlah Respons,Persentase\n';
          const total = Object.values(question.choices).reduce((sum: number, count: any) => sum + (typeof count === 'number' ? count : 0), 0);

          Object.entries(question.choices).forEach(([choice, count]: [string, any]) => {
            const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : '0';
            csvContent += `${choice},${count},${percentage}%\n`;
          });
        }
        csvContent += '\n';
      });

      // Create a blob and download the CSV
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `${form.title}_respons.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Terjadi kesalahan saat mengekspor CSV');
    }
  };

  if (isLoading) {
    return (
      <div className="container flex items-center justify-center h-64 py-6 mx-auto">
        <p>Memuat data...</p>
      </div>
    );
  }

  if (!form) {
    return (
      <div className="container py-6 mx-auto">
        <Alert variant="destructive">
          <AlertDescription>
            Formulir tidak ditemukan. Silakan kembali ke daftar formulir.
          </AlertDescription>
        </Alert>
        <Button className="mt-4" onClick={() => router.push('/dashboard/forms')}>
          Kembali ke Daftar Formulir
        </Button>
      </div>
    );
  }

  return (
    <div className="container py-6 mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.push('/dashboard/forms')}>
            <ArrowLeft className="w-4 h-4 mr-1" />
            Kembali
          </Button>
          <h1 className="text-2xl font-bold">Respons: {form.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={exportToExcel}
            disabled={submissions.length === 0 || exportLoading.excel}
          >
            <FileSpreadsheet className="w-4 h-4 mr-1" />
            Ekspor Excel
          </Button>
          <Button
            variant="outline"
            onClick={exportToCSV}
            disabled={submissions.length === 0}
          >
            <Download className="w-4 h-4 mr-1" />
            Ekspor CSV
          </Button>
          <Button
            variant="outline"
            onClick={exportToPdf}
            disabled={submissions.length === 0 || exportLoading.pdf}
          >
            <FileUp className="w-4 h-4 mr-1" />
            Ekspor PDF
          </Button>
        </div>
      </div>

      <Tabs defaultValue="summary" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="summary">Ringkasan</TabsTrigger>
          <TabsTrigger value="individual">Respons Individual</TabsTrigger>
          <TabsTrigger value="questions">Berdasarkan Pertanyaan</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="mt-0">
          <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Total Respons</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold">{submissions.length}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Respons Terakhir</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl">
                  {submissions.length > 0
                    ? formatDate(submissions[0].submittedAt)
                    : 'Belum ada respons'}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Tingkat Penyelesaian</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl">
                  {submissions.length > 0
                    ? '100%'
                    : '0%'}
                </p>
              </CardContent>
            </Card>
          </div>

          {submissions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <FileText className="w-12 h-12 mb-4 text-gray-400" />
              <h3 className="text-lg font-medium">Belum ada respons</h3>
              <p className="mt-1 mb-4 text-sm text-gray-500">
                Belum ada yang mengisi formulir ini.
              </p>
            </div>
          ) : (
            <>
              {/* Analisis Respons */}
              {form.questions
                .filter((question) => question.questionType === 'MULTIPLE_CHOICE')
                .map((question) => {
                  const analysis = (
                    <ResponseAnalysis
                      key={question.id}
                      question={question}
                      submissions={submissions}
                    />
                  );
                  const patched = isValidElement(analysis)
                    ? React.cloneElement(
                        analysis as React.ReactElement<React.ComponentProps<typeof ResponseAnalysis>>,
                        {
                          chartContainerRef: (el: HTMLDivElement | null) => {
                            chartRefs.current[question.id] = el;
                          },
                          onChartInstance: (chart: any) => {
                            chartInstances.current[question.id] = chart;
                          },
                        }
                      )
                    : analysis;
                  return patched;
                })}
              {form.questions
                .filter((question) =>
                  ['SINGLE_CHOICE', 'DROPDOWN', 'CHECKLIST'].includes(question.questionType)
                )
                .map((question) => {
                  const analysis = (
                    <ResponseAnalysis
                      key={question.id}
                      question={question}
                      submissions={submissions}
                    />
                  );
                  const patched = isValidElement(analysis)
                    ? React.cloneElement(
                        analysis as React.ReactElement<React.ComponentProps<typeof ResponseAnalysis>>,
                        {
                          chartContainerRef: (el: HTMLDivElement | null) => {
                            chartRefs.current[question.id] = el;
                          },
                          onChartInstance: (chart: any) => {
                            chartInstances.current[question.id] = chart;
                          },
                        }
                      )
                    : analysis;
                  return patched;
                })}

              {/* Tabel Semua Respons */}
              <Card>
                <CardHeader>
                  <CardTitle>Semua Respons</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Tanggal Pengisian</TableHead>
                        {submissions[0]?.respondentInfo &&
                          Object.keys(submissions[0].respondentInfo).map((key) => (
                            <TableHead key={key}>{key}</TableHead>
                          ))}
                        <TableHead>Aksi</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {submissions.map((submission) => (
                        <TableRow key={submission.id}>
                          <TableCell className="font-medium">
                            {submission.id.substring(0, 8)}...
                          </TableCell>
                          <TableCell>{formatDate(submission.submittedAt)}</TableCell>
                          {submission.respondentInfo &&
                            Object.keys(submissions[0].respondentInfo).map((key) => (
                              <TableCell key={key}>{submission.respondentInfo[key]}</TableCell>
                            ))}
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setActiveTab('individual');
                                // You could also set a selected submission ID here
                              }}
                            >
                              Lihat Detail
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="individual" className="mt-0">
          {submissions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <FileText className="w-12 h-12 mb-4 text-gray-400" />
              <h3 className="text-lg font-medium">Belum ada respons</h3>
              <p className="mt-1 mb-4 text-sm text-gray-500">
                Belum ada yang mengisi formulir ini.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {submissions.map((submission) => (
                <Card key={submission.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Respons #{submission.id.substring(0, 8)}</CardTitle>
                      <p className="text-sm text-gray-500">
                        Diisi pada: {formatDate(submission.submittedAt)}
                      </p>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {submission.respondentInfo && (
                      <div className="p-4 mb-4 rounded-md bg-gray-50">
                        <h3 className="mb-2 font-medium">Informasi Responden</h3>
                        <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                          {Object.entries(submission.respondentInfo).map(([key, value]) => (
                            <div key={key}>
                              <span className="font-medium">{key}: </span>
                              <span>{value as string}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="space-y-4">
                      {form.questions
                        .sort((a, b) => a.order - b.order)
                        .map((question) => {
                          const answer = submission.answers.find((a) => a.questionId === question.id);
                          return (
                            <div key={question.id} className="p-4 border rounded-md">
                              <h3 className="mb-1 font-medium">{question.questionText}</h3>
                              <p className="mb-2 text-sm text-gray-500">
                                {question.questionType.replace(/_/g, ' ')}
                              </p>
                              {answer ? (
                                <div className="mt-2">
                                  {answer.answerValue && <p>{answer.answerValue}</p>}
                                  {answer.answerJson && (
                                    <pre className="p-2 overflow-x-auto text-sm rounded bg-gray-50">
                                      {JSON.stringify(answer.answerJson, null, 2)}
                                    </pre>
                                  )}
                                  {answer.fileUrl && (
                                    <a
                                      href={answer.fileUrl}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:underline"
                                    >
                                      Lihat File
                                    </a>
                                  )}
                                </div>
                              ) : (
                                <p className="italic text-gray-500">Tidak dijawab</p>
                              )}
                            </div>
                          );
                        })}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="questions" className="mt-0">
          <div className="mb-6">
            <h2 className="text-xl font-bold">Analisis Respons Per Butir Pertanyaan</h2>
            <p className="mt-1 text-sm text-gray-500">
              Analisis ini menunjukkan persentase responden yang memilih setiap opsi jawaban untuk setiap pertanyaan.
              Untuk pertanyaan pilihan ganda dan checklist, satu responden dapat memilih lebih dari satu opsi.
            </p>
          </div>
          {submissions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <FileText className="w-12 h-12 mb-4 text-gray-400" />
              <h3 className="text-lg font-medium">Belum ada respons</h3>
              <p className="mt-1 mb-4 text-sm text-gray-500">
                Belum ada yang mengisi formulir ini.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {form.questions
                .sort((a, b) => a.order - b.order)
                .map((question) => {
                  // Get all answers for this question
                  const answers = submissions
                    .map((submission) => submission.answers.find((a) => a.questionId === question.id))
                    .filter(Boolean);

                  // Patch: inject ref ke chart container ResponseAnalysis
                  const analysis = (
                    <ResponseAnalysis
                      key={question.id}
                      question={question}
                      submissions={submissions}
                    />
                  );
                  const patched = isValidElement(analysis)
                    ? React.cloneElement(
                        analysis as React.ReactElement<React.ComponentProps<typeof ResponseAnalysis>>,
                        {
                          chartContainerRef: (el: HTMLDivElement | null) => {
                            chartRefs.current[question.id] = el;
                          },
                          onChartInstance: (chart: any) => {
                            chartInstances.current[question.id] = chart;
                          },
                        }
                      )
                    : analysis;

                  return (
                    <Card key={question.id}>
                      <CardHeader>
                        <CardTitle>{question.questionText}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="mb-4 text-sm text-gray-500">
                          Tipe: {question.questionType.replace(/_/g, ' ')}
                          <br />
                          Dijawab: {answers.length} dari {submissions.length} responden
                        </p>

                        {/* Display answers based on question type */}
                        {['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'DROPDOWN', 'CHECKLIST'].includes(
                          question.questionType
                        ) ? (
                          patched
                        ) : (
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Responden</TableHead>
                                <TableHead>Jawaban</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {submissions.map((submission) => {
                                const answer = submission.answers.find(
                                  (a) => a.questionId === question.id
                                );
                                return (
                                  <TableRow key={submission.id}>
                                    <TableCell>
                                      {submission.respondentInfo?.name ||
                                        submission.id.substring(0, 8)}
                                    </TableCell>
                                    <TableCell>
                                      {answer ? (
                                        <>
                                          {answer.answerValue && <span>{answer.answerValue}</span>}
                                          {answer.answerJson && (
                                            <pre className="p-2 overflow-x-auto text-sm rounded bg-gray-50">
                                              {JSON.stringify(answer.answerJson, null, 2)}
                                            </pre>
                                          )}
                                          {answer.fileUrl && (
                                            <a
                                              href={answer.fileUrl}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="text-blue-600 hover:underline"
                                            >
                                              Lihat File
                                            </a>
                                          )}
                                        </>
                                      ) : (
                                        <span className="italic text-gray-500">Tidak dijawab</span>
                                      )}
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Main component that unwraps params safely
export default function FormResponsesPage({ params }: { params: Promise<{ formId: string }> }) {
  // Unwrap params using React.use() in the top-level component
  const unwrappedParams = React.use(params);
  const { formId } = unwrappedParams;

  // Pass the unwrapped formId to the content component
  return <FormResponsesContent formId={formId} />;
}

