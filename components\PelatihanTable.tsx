'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface PelatihanItem {
  id: string | number;
  nama: string;
  // Add other properties as needed
}
interface PelatihanTableProps {
  isViewOnly?: boolean;
}

export default function PelatihanTable({ isViewOnly = false }: PelatihanTableProps) {
  const [pelatihan, setPelatihan] = useState<PelatihanItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  
  useEffect(() => {
    const fetchPelatihan = async () => {
      try {
        const response = await fetch('/api/pelatihan');
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const data = await response.json();
        setPelatihan(data);
        setLoading(false);
      } catch (err: unknown) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setLoading(false);
      }
    };
    
    fetchPelatihan();
  }, []);

  if (loading) {
    return <div className="py-4 text-center">Loading...</div>;
  }

  if (error) {
    return <div className="py-4 text-center text-red-500">Error: {error}</div>;
  }

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
            Nama Kegiatan
          </th>
          {/* Other table headers */}
          <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
            Aksi
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {pelatihan.map((item) => (
          <tr key={item.id}>
            <td className="px-6 py-4 whitespace-nowrap">
              <div className="text-sm font-medium text-gray-900">{item.nama}</div>
            </td>
            {/* Other table cells */}
            <td className="px-6 py-4 whitespace-nowrap">
              <Link
                href={`/dashboard/pelatihan/${item.id}`}
                className="text-indigo-600 hover:text-indigo-900"
              >
                Lihat
              </Link>
              
              {!isViewOnly && (
                <>
                  {' | '}
                  <Link
                    href={`/dashboard/pelatihan/${item.id}/edit`}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    Edit
                  </Link>
                </>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
