'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { csrfFetch } from '@/utils/csrfClient';
import Toast, { ToastType } from '@/components/Toast';

interface Location {
  id: string;
  kab_kota: string;
  createdAt: string;
  updatedAt: string;
}

export default function EditLocationPage() {
  const [location, setLocation] = useState<Location | null>(null);
  const [kabKota, setKabKota] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [toast, setToast] = useState<{ message: string, type: ToastType } | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const params = useParams();
  const router = useRouter();
  const locationId = params.id as string;

  useEffect(() => {
    const fetchLocation = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/tugas-lokasi/${locationId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch location details');
        }

        const locationData = await response.json();
        setLocation(locationData);
        setKabKota(locationData.kab_kota);
      } catch (_err) {
        setError('Gagal memuat data lokasi');
        setToast({
          message: 'Gagal memuat data lokasi',
          type: 'error',
        });
      } finally {
        setLoading(false);
      }
    };

    if (locationId) {
      fetchLocation();
    }
  }, [locationId]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Validasi input
      if (!kabKota.trim()) {
        throw new Error('Nama kabupaten/kota harus diisi');
      }

      const response = await csrfFetch(`/api/tugas-lokasi/${locationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ kab_kota: kabKota.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal memperbarui lokasi');
      }

      setToast({
        message: 'Lokasi berhasil diperbarui',
        type: 'success'
      });

      // Redirect after successful update
      setTimeout(() => {
        router.push('/dashboard/tempat-tugas');
      }, 2000);
    } catch (error) {
      setToast({
        message: error instanceof Error ? error.message : 'Gagal memperbarui lokasi',
        type: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="w-12 h-12 border-t-2 border-b-2 border-blue-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error || !location) {
    return (
      <div className="p-4 mb-4 text-red-800 border border-red-200 rounded-lg bg-red-50">
        <p>Error: {error || 'Data lokasi tidak ditemukan'}</p>
        <Link href="/dashboard/tempat-tugas" className="inline-block mt-2 text-blue-600 hover:underline">
          Kembali ke daftar lokasi
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Edit Tempat Tugas</h1>
        <Link
          href="/dashboard/tempat-tugas"
          className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
        >
          Kembali
        </Link>
      </div>

      {/* Toast Notification */}
      {toast && (
        <Toast
          toast={{ id: 'edit-location-toast', message: toast.message, type: toast.type }}
          onClose={() => setToast(null)}
        />
      )}

      {/* Form */}
      <div className="p-6 bg-white rounded-lg shadow">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="kab_kota" className="block mb-2 text-sm font-medium text-gray-700">
              Nama Kabupaten/Kota
            </label>
            <input
              type="text"
              id="kab_kota"
              value={kabKota}
              onChange={(e) => setKabKota(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Contoh: Kabupaten Lampung Timur"
              required
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Link
              href="/dashboard/tempat-tugas"
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              Batal
            </Link>
            <button
              type="submit"
              disabled={saving}
              className={`px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 ${saving ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {saving ? (
                <span className="flex items-center">
                  <svg className="w-5 h-5 mr-2 animate-spin" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Menyimpan...
                </span>
              ) : (
                'Simpan Perubahan'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}