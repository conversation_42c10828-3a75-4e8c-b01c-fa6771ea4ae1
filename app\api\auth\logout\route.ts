// File: /app/api/auth/logout/route.ts
import { NextResponse } from 'next/server';
import { logoutUser } from '../../../../lib/auth';
import { cookies } from 'next/headers';
import { getBaseUrl } from '../../../../lib/constants';

export async function POST() {
  try {
    await logoutUser();
    
    // Gunakan await untuk cookies()
    const cookieStore = await cookies();
    cookieStore.delete('your-auth-cookie-name'); // Sesuaikan dengan nama cookie yang digunakan
    
    // Gunakan helper function untuk mendapatkan base URL
    const baseUrl = getBaseUrl();
    
    // Redirect ke halaman login
    return NextResponse.redirect(new URL('/login', baseUrl), 303);
  } catch (error) {
    console.error('Logout API error:', error);
    const baseUrl = getBaseUrl();
    return NextResponse.redirect(new URL('/login', baseUrl), 303);
  }
}
