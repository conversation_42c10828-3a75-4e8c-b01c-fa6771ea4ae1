import React from 'react';

export default function DashboardSkeleton() {
  return (
    <div className="animate-pulse">
      {/* Card Statistics */}
      <div className="grid grid-cols-1 gap-4 mb-8 sm:grid-cols-2 lg:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="p-6 bg-white rounded-lg shadow">
            <div className="h-4 mb-2 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 mb-4 bg-gray-300 rounded w-1/2"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>

      {/* Tabel Pelatihan */}
      <div className="p-6 mb-8 bg-white rounded-lg shadow">
        <div className="h-6 mb-6 bg-gray-200 rounded w-1/4"></div>
        <table className="w-full">
          <thead>
            <tr>
              <th className="h-8 bg-gray-200 rounded"></th>
              <th className="h-8 bg-gray-200 rounded"></th>
              <th className="h-8 bg-gray-200 rounded"></th>
              <th className="h-8 bg-gray-200 rounded"></th>
            </tr>
          </thead>
          <tbody>
            {[1, 2, 3, 4].map((i) => (
              <tr key={i} className="border-t">
                <td className="py-4 pr-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                </td>
                <td className="py-4 pr-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                </td>
                <td className="py-4 pr-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                </td>
                <td className="py-4">
                  <div className="h-4 bg-gray-200 rounded"></div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Chart & Recent Activities */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="p-6 bg-white rounded-lg shadow">
          <div className="h-6 mb-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
        <div className="p-6 bg-white rounded-lg shadow">
          <div className="h-6 mb-6 bg-gray-200 rounded w-1/3"></div>
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="mb-4 last:mb-0">
              <div className="h-5 mb-1 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-2/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}