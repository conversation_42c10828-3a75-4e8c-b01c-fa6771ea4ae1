// File: /app/dashboard/absensi/[id]/page.tsx
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import { getCurrentUser } from '../../../../lib/auth';
import { prisma } from '../../../../lib/prisma';
import { formatDateTime } from '../../../../utils/helpers';
import { formatIndonesiaDate } from '../../../../utils/dateUtils';
import { Metadata } from 'next';
import { Suspense } from 'react';
import { ArrowLeft, ChevronLeft, ChevronRight } from 'lucide-react';
import SignatureImage from '../../../../components/SignatureImage';
import AttendancePhoto from '../../../../components/AttendancePhoto';

export const metadata: Metadata = {
  title: 'Detail Absensi',
  description: 'Informasi lengkap data kehadiran peserta'
};

export default async function AbsensiDetailPage(
  props: {
    params: Promise<{ id: string }>;
    searchParams: Promise<{ backUrl?: string }>;
  }
) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/login');
  }
  
  try {    // Fetch absensi
    const absensi = await prisma.absensi.findUnique({
      where: { id: params.id },
      include: {
        pelatihan: {
          select: {
            id: true,
            nama: true,
            tempat: true,
            tgl_mulai: true,
            tgl_berakhir: true,
            userId: true
          }
        },
      },
    });
    
    if (!absensi) {
      notFound();
    }
    
    // Validate if user has access to this data
    if (absensi.pelatihan.userId !== user.id) {
      // User doesn't have permission to access this data
      redirect('/dashboard/absensi');
    }
    
    // Fetch adjacent absensi entries for navigation (from the same pelatihan)
    const [prevAbsensi, nextAbsensi] = await Promise.all([
      // Previous entry (newer by time)
      prisma.absensi.findFirst({
        where: {
          pelatihanId: absensi.pelatihanId,
          waktu: {
            gt: absensi.waktu
          }
        },
        orderBy: {
          waktu: 'asc'
        },
        select: {
          id: true,
          nama: true
        }
      }),
      // Next entry (older by time)
      prisma.absensi.findFirst({
        where: {
          pelatihanId: absensi.pelatihanId,
          waktu: {
            lt: absensi.waktu
          }
        },
        orderBy: {
          waktu: 'desc'
        },
        select: {
          id: true,
          nama: true
        }
      })
    ]);

    // Determine back URL, default to the absensi list
    const backUrl = searchParams.backUrl || '/dashboard/absensi';
    
    return (
      <div className="container px-4 py-4 mx-auto sm:px-6 lg:px-8">
        <Suspense fallback={<div className="p-4 text-center">Memuat data...</div>}>
          <div className="flex flex-col items-start justify-between gap-4 mb-4 sm:mb-6 sm:flex-row sm:items-center">
            <h1 className="text-xl font-semibold sm:text-2xl">Detail Absensi</h1>
            <Link
              href={backUrl}
              className="flex items-center gap-1 px-3 py-1.5 sm:px-4 sm:py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors w-full sm:w-auto text-center"
            >
              <ArrowLeft size={16} /> Kembali
            </Link>
          </div>
          
          {/* Navigation between entries */}
          {(prevAbsensi || nextAbsensi) && (
            <div className="flex justify-between mb-4 text-sm">
              {prevAbsensi ? (
                <Link
                  href={`/dashboard/absensi/${prevAbsensi.id}?backUrl=${encodeURIComponent(backUrl)}`}
                  className="flex items-center text-blue-600 hover:underline"
                >
                  <ChevronLeft size={16} /> 
                  <span>
                    <span className="hidden sm:inline">Sebelumnya: </span>
                    {prevAbsensi.nama}
                  </span>
                </Link>
              ) : (
                <span></span>
              )}
              {nextAbsensi && (
                <Link
                  href={`/dashboard/absensi/${nextAbsensi.id}?backUrl=${encodeURIComponent(backUrl)}`}
                  className="flex items-center text-blue-600 hover:underline"
                >
                  <span>
                    <span className="hidden sm:inline">Berikutnya: </span>
                    {nextAbsensi.nama}
                  </span>
                  <ChevronRight size={16} />
                </Link>
              )}
            </div>
          )}
          
          <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
            <div className="p-4 sm:p-6">
              <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Informasi Absensi</h2>
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Nama</p>
                  <p className="text-sm font-medium break-words sm:text-base">{absensi.nama}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Waktu Absensi</p>
                  <p className="text-sm font-medium break-words sm:text-base">{formatDateTime(absensi.waktu)}</p>
                </div>
                {absensi.nip_nik && (
                  <div>
                    <p className="text-xs text-gray-500 sm:text-sm">NIP/NIK</p>
                    <p className="text-sm font-medium break-words sm:text-base">{absensi.nip_nik}</p>
                  </div>
                )}
                {absensi.jabatan && (
                  <div>
                    <p className="text-xs text-gray-500 sm:text-sm">Jabatan</p>
                    <p className="text-sm font-medium break-words sm:text-base">{absensi.jabatan}</p>
                  </div>
                )}
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Unit Kerja</p>
                  <p className="text-sm font-medium break-words sm:text-base">{absensi.unit_kerja}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Nomor HP</p>
                  <p className="text-sm font-medium break-words sm:text-base">{absensi.no_hp}</p>
                </div>                {absensi.jenjang && (
                  <div>
                    <p className="text-xs text-gray-500 sm:text-sm">Jenjang</p>
                    <p className="text-sm font-medium break-words sm:text-base">{absensi.jenjang}</p>
                  </div>
                )}
                {(absensi.latitude || absensi.longitude || absensi.alamat) && (
                  <div className="sm:col-span-2">
                    <p className="text-xs text-gray-500 sm:text-sm">Lokasi Absensi</p>
                    <div className="mt-1 space-y-1">
                      {absensi.alamat && (
                        <p className="text-sm font-medium break-words sm:text-base">{absensi.alamat}</p>
                      )}
                      {(absensi.latitude && absensi.longitude) && (
                        <div className="flex flex-col gap-1 sm:flex-row sm:gap-4">
                          <p className="text-xs text-gray-600">
                            Koordinat: {absensi.latitude.toFixed(6)}, {absensi.longitude.toFixed(6)}
                          </p>
                          <a
                            href={`https://www.google.com/maps?q=${absensi.latitude},${absensi.longitude}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-blue-600 hover:underline"
                          >
                            Lihat di Google Maps
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
            <div className="p-4 sm:p-6">
              <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">
                <Link href={`/dashboard/pelatihan/${absensi.pelatihanId}`} className="text-blue-600 hover:underline">
                  Informasi Kegiatan
                </Link>
              </h2>
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Nama Kegiatan</p>
                  <p className="text-sm font-medium break-words sm:text-base">{absensi.pelatihan.nama}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500 sm:text-sm">Tempat</p>
                  <p className="text-sm font-medium break-words sm:text-base">{absensi.pelatihan.tempat}</p>
                </div>
                <div className="sm:col-span-2">
                  <p className="text-xs text-gray-500 sm:text-sm">Tanggal</p>
                  <p className="text-sm font-medium sm:text-base">
                    {formatIndonesiaDate(absensi.pelatihan.tgl_mulai)} - {formatIndonesiaDate(absensi.pelatihan.tgl_berakhir)}
                  </p>
                </div>
              </div>
            </div>
          </div>            {/* Photo Section - Only for internal attendance */}
          {((absensi as any).foto_url || (absensi as any).foto_path) && (
            <div className="mb-4 overflow-hidden bg-white rounded-lg shadow sm:mb-6">
              <div className="p-4 sm:p-6">
                <div className="flex items-center gap-2 mb-3 sm:mb-4">
                  <h2 className="text-base font-medium sm:text-lg">Foto Kehadiran</h2>
                  <div className="inline-block px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full">
                    Peserta Internal
                  </div>
                </div>                <div className="flex justify-center">
                  <div className="max-w-md">
                    <AttendancePhoto
                      src={(absensi as any).foto_url || `/uploads/absensi/photos/${(absensi as any).foto_path}`}
                      alt="Foto Kehadiran"
                      width={400}
                      height={400}
                      className="w-full h-auto border rounded-lg shadow-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="overflow-hidden bg-white rounded-lg shadow">
            <div className="p-4 sm:p-6">
              <h2 className="mb-3 text-base font-medium sm:text-lg sm:mb-4">Tanda Tangan</h2>
              <div className="p-2 overflow-auto border border-gray-300 rounded-md sm:p-4">
                {/* Usando el componente cliente para la imagen de la firma */}
                <SignatureImage signatureUrl={absensi.tanda_tangan} />
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-right">
            <Link
              href={`/dashboard/absensi?pelatihan=${absensi.pelatihanId}`}
              className="text-sm text-blue-600 hover:underline"
            >
              Lihat semua absensi dari kegiatan ini
            </Link>
          </div>
        </Suspense>
      </div>
    );  } catch (_error) {
    return (
      <div className="container px-4 mx-auto sm:px-6 lg:px-8">
        <div className="p-4 my-4 text-red-700 bg-red-100 rounded-md">
          Terjadi kesalahan saat mengambil data. Silakan coba lagi nanti.
        </div>
        <div className="mt-4 text-center">
          <Link
            href="/dashboard/absensi"
            className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            Kembali ke Daftar Absensi
          </Link>
        </div>
      </div>
    );
  }
}