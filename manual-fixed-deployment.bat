@echo off
cls
color 0E
echo =========================================================
echo         MANUAL DEPLOYMENT - FIXED REALTIME PHOTO FIX
echo =========================================================
echo.

echo MASALAH YANG DIPERBAIKI:
echo ========================
echo ✅ Syntax error: unexpected EOF while looking for matching quote
echo ✅ Quote matching dalam printf command
echo ✅ JPEG file creation menggunakan echo -en
echo ✅ Improved error handling dan backup restore
echo.

echo STEP 1: Upload Script to VPS
echo =============================
echo 1. Buka WinSCP, FileZilla, atau SSH client favorit Anda
echo 2. Connect ke VPS CloudPanel Anda
echo 3. Upload file 'cloudpanel-realtime-photo-fix-fixed.sh' ke folder /tmp/
echo 4. Rename file menjadi 'cloudpanel-realtime-fix.sh' (optional)
echo.

echo STEP 2: Execute Script on VPS
echo ==============================
echo Copy dan paste command berikut ke SSH terminal:
echo.
echo # Make script executable
echo chmod +x /tmp/cloudpanel-realtime-photo-fix-fixed.sh
echo.
echo # Run the fixed script
echo /tmp/cloudpanel-realtime-photo-fix-fixed.sh
echo.
echo ATAU jika sudah rename:
echo chmod +x /tmp/cloudpanel-realtime-fix.sh
echo /tmp/cloudpanel-realtime-fix.sh
echo.

echo STEP 3: Expected Output
echo ========================
echo Script akan menampilkan:
echo - ✅ Detecting CloudPanel site
echo - ✅ Installing required packages
echo - ✅ Configuring nginx for real-time access
echo - ✅ Creating real-time monitoring service
echo - ✅ Testing real-time upload functionality
echo - ✅ SUCCESS: Photo accessible immediately without nginx reload!
echo.

echo STEP 4: Verification Commands
echo =============================
echo Setelah script selesai, verifikasi dengan:
echo.
echo # Check real-time monitor service
echo systemctl status realtime-photo-monitor
echo.
echo # Monitor real-time logs
echo tail -f /var/log/realtime-photo-monitor.log
echo.
echo # Check nginx photo logs
echo tail -f /var/log/nginx/photo_realtime.log
echo.
echo # Test nginx configuration
echo nginx -t
echo.
echo # Manual photo access test
echo curl -I http://YOUR_DOMAIN/uploads/path/to/photo.jpg
echo.

echo TROUBLESHOOTING
echo ===============
echo Jika masih ada masalah:
echo.
echo 1. Check syntax error:
echo    bash -n /tmp/cloudpanel-realtime-photo-fix-fixed.sh
echo.
echo 2. Run with debug mode:
echo    bash -x /tmp/cloudpanel-realtime-photo-fix-fixed.sh
echo.
echo 3. Check nginx config syntax:
echo    nginx -t
echo.
echo 4. View detailed logs:
echo    journalctl -u realtime-photo-monitor -f
echo    tail -f /var/log/nginx/error.log
echo.
echo 5. Restart services manually:
echo    systemctl restart nginx
echo    systemctl restart realtime-photo-monitor
echo.

echo ROLLBACK (Jika Perlu)
echo ======================
echo Jika ada masalah dan perlu rollback:
echo.
echo 1. Stop monitoring service:
echo    systemctl stop realtime-photo-monitor
echo    systemctl disable realtime-photo-monitor
echo.
echo 2. Restore nginx config backup:
echo    ls /etc/nginx/sites-available/*.backup-*
echo    cp /etc/nginx/sites-available/DOMAIN.backup-TIMESTAMP /etc/nginx/sites-available/DOMAIN
echo    nginx -t ^&^& systemctl restart nginx
echo.
echo 3. Remove monitoring files:
echo    rm /usr/local/bin/realtime-photo-monitor.sh
echo    rm /etc/systemd/system/realtime-photo-monitor.service
echo    systemctl daemon-reload
echo.

echo EXPECTED RESULTS AFTER FIX
echo ===========================
echo ✅ Photo upload langsung bisa diakses tanpa reload nginx
echo ✅ Real-time monitoring service berjalan stabil
echo ✅ Nginx headers no-cache untuk uploads directory
echo ✅ Auto permission fixing untuk file baru
echo ✅ Tidak ada syntax error lagi
echo.

echo FILES YANG DIBUAT:
echo ===================
echo - /usr/local/bin/realtime-photo-monitor.sh (monitoring script)
echo - /etc/systemd/system/realtime-photo-monitor.service (systemd service)
echo - /etc/sysctl.d/99-inotify.conf (inotify optimization)
echo - /var/log/realtime-photo-monitor.log (monitoring logs)
echo - /var/log/nginx/photo_realtime.log (nginx photo access logs)
echo.

echo =========================================================
echo    Fixed script ready: cloudpanel-realtime-photo-fix-fixed.sh
echo =========================================================
echo.
pause
