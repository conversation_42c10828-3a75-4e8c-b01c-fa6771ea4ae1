#!/bin/bash

# Advanced Troubleshooter untuk Auto-Refresh Fix
# Diagnosa dan perbaikan otomatis masalah yang mungkin terjadi

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
}

echo -e "${GREEN}"
echo "================================================="
echo "   ADVANCED AUTO-REFRESH TROUBLESHOOTER"
echo "   Diagnosa & Perbaikan Otomatis"
echo "================================================="
echo -e "${NC}"

# Global variables
ISSUES_FOUND=0
FIXES_APPLIED=0

# Issue detection functions
check_nginx_status() {
    log_action "Checking Nginx status..."
    
    if ! systemctl is-active --quiet nginx; then
        log_error "Nginx tidak aktif"
        ((ISSUES_FOUND++))
        
        log_action "Mencoba restart Nginx..."
        if systemctl restart nginx; then
            log_success "Nginx berhasil direstart"
            ((FIXES_APPLIED++))
        else
            log_error "Gagal restart Nginx"
            return 1
        fi
    else
        log_success "Nginx aktif"
    fi
    
    # Check nginx configuration
    if ! nginx -t >/dev/null 2>&1; then
        log_error "Nginx configuration error"
        ((ISSUES_FOUND++))
        
        log_action "Checking nginx error details..."
        nginx -t
        
        log_warning "Manual intervention required untuk nginx config"
        return 1
    else
        log_success "Nginx configuration OK"
    fi
}

check_monitoring_service() {
    log_action "Checking monitoring service..."
    
    if ! systemctl is-active --quiet advanced-photo-monitor.service 2>/dev/null; then
        log_error "Advanced photo monitor tidak aktif"
        ((ISSUES_FOUND++))
        
        # Check if service exists
        if systemctl list-unit-files | grep -q "advanced-photo-monitor.service"; then
            log_action "Mencoba restart monitoring service..."
            if systemctl restart advanced-photo-monitor.service; then
                log_success "Monitoring service berhasil direstart"
                ((FIXES_APPLIED++))
            else
                log_error "Gagal restart monitoring service"
                
                # Check service logs
                log_debug "Service logs:"
                journalctl -u advanced-photo-monitor.service --no-pager -n 10
            fi
        else
            log_error "Monitoring service tidak terinstall"
            log_warning "Jalankan script advanced-cloudpanel-autorefresh-fix.sh untuk install service"
        fi
    else
        log_success "Monitoring service aktif"
    fi
}

check_upload_directories() {
    log_action "Checking upload directories..."
    
    # Find all potential upload directories
    UPLOAD_DIRS=()
    while IFS= read -r -d '' dir; do
        if [ -d "$dir" ]; then
            UPLOAD_DIRS+=("$dir")
        fi
    done < <(find /home -path "*/htdocs/public/uploads" -type d -print0 2>/dev/null || true)
    
    # Add common directories
    for common_dir in "/var/www/html/public/uploads" "/var/www/uploads"; do
        if [ -d "$common_dir" ]; then
            UPLOAD_DIRS+=("$common_dir")
        fi
    done
    
    if [ ${#UPLOAD_DIRS[@]} -eq 0 ]; then
        log_error "Tidak ada upload directory ditemukan"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    # Check each directory
    for upload_dir in "${UPLOAD_DIRS[@]}"; do
        log_debug "Checking: $upload_dir"
        
        # Check permissions
        DIR_OWNER=$(stat -c '%U:%G' "$upload_dir" 2>/dev/null || echo "unknown")
        DIR_PERMS=$(stat -c '%a' "$upload_dir" 2>/dev/null || echo "000")
        
        log_info "Directory: $upload_dir"
        log_info "Owner: $DIR_OWNER, Permissions: $DIR_PERMS"
        
        # Check if writable
        if [ ! -w "$upload_dir" ]; then
            log_error "Directory tidak writable: $upload_dir"
            ((ISSUES_FOUND++))
            
            # Try to fix permissions
            log_action "Fixing permissions untuk $upload_dir..."
            
            # Detect correct owner
            if [[ "$upload_dir" =~ /home/<USER>/]+)/htdocs ]]; then
                CORRECT_USER="${BASH_REMATCH[1]}"
                CORRECT_GROUP="$CORRECT_USER"
            else
                CORRECT_USER="www-data"
                CORRECT_GROUP="www-data"
            fi
            
            if chown -R "$CORRECT_USER:$CORRECT_GROUP" "$upload_dir" && chmod -R 755 "$upload_dir"; then
                log_success "Permissions fixed untuk $upload_dir"
                ((FIXES_APPLIED++))
            else
                log_error "Gagal fix permissions untuk $upload_dir"
            fi
        else
            log_success "Directory writable: $upload_dir"
        fi
        
        # Check recent uploads
        RECENT_FILES=$(find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -mtime -1 2>/dev/null | wc -l)
        log_info "Recent uploads (24h): $RECENT_FILES"
    done
}

check_nginx_config() {
    log_action "Checking nginx configuration untuk uploads..."
    
    # Find site configurations
    SITE_CONFIGS=()
    for config_pattern in \
        "/etc/nginx/sites-available/kegiatan.bpmpkaltim.id" \
        "/etc/nginx/sites-available/kegiatan-bpmpkaltim-id" \
        "/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf" \
        "/etc/nginx/conf.d/kegiatan-bpmpkaltim-id.conf"; do
        
        if [ -f "$config_pattern" ]; then
            SITE_CONFIGS+=("$config_pattern")
        fi
    done
    
    # Find by content
    if [ ${#SITE_CONFIGS[@]} -eq 0 ]; then
        while IFS= read -r -d '' config_file; do
            if grep -l "kegiatan\|bpmpkaltim" "$config_file" >/dev/null 2>&1; then
                SITE_CONFIGS+=("$config_file")
            fi
        done < <(find /etc/nginx -name "*.conf" -print0 2>/dev/null || true)
    fi
    
    if [ ${#SITE_CONFIGS[@]} -eq 0 ]; then
        log_error "Tidak ada nginx site config ditemukan"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    # Check each config
    for config in "${SITE_CONFIGS[@]}"; do
        log_debug "Checking config: $config"
        
        # Check if uploads location exists
        if grep -q "location /uploads/" "$config"; then
            log_success "Uploads location configured di $config"
            
            # Check for no-cache headers
            if grep -A 10 "location /uploads/" "$config" | grep -q "no-cache"; then
                log_success "No-cache headers configured"
            else
                log_warning "No-cache headers mungkin tidak optimal"
                ((ISSUES_FOUND++))
                
                log_action "Adding no-cache headers..."
                # Backup and add headers
                cp "$config" "$config.backup-$(date +%s)"
                
                sed -i '/location \/uploads\/ {/,/}/ {
                    /expires/c\        expires -1;
                    /}$/i\        add_header Cache-Control "no-cache, no-store, must-revalidate";
                    /}$/i\        add_header Pragma "no-cache";
                }' "$config"
                
                log_success "No-cache headers added"
                ((FIXES_APPLIED++))
            fi
        else
            log_error "Uploads location tidak dikonfigurasi di $config"
            ((ISSUES_FOUND++))
        fi
    done
}

check_system_resources() {
    log_action "Checking system resources..."
    
    # Memory check
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", ($3/$2) * 100.0}')
    log_info "Memory usage: $MEM_USAGE%"
    
    if (( $(echo "$MEM_USAGE > 90" | bc -l) )); then
        log_warning "High memory usage: $MEM_USAGE%"
        ((ISSUES_FOUND++))
    fi
    
    # Disk space check
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    log_info "Disk usage: $DISK_USAGE%"
    
    if [ $DISK_USAGE -gt 90 ]; then
        log_warning "High disk usage: $DISK_USAGE%"
        ((ISSUES_FOUND++))
    fi
    
    # inotify limits check
    INOTIFY_WATCHES=$(cat /proc/sys/fs/inotify/max_user_watches)
    log_info "inotify max_user_watches: $INOTIFY_WATCHES"
    
    if [ $INOTIFY_WATCHES -lt 524288 ]; then
        log_warning "inotify limit mungkin terlalu rendah"
        ((ISSUES_FOUND++))
        
        log_action "Increasing inotify limits..."
        echo "fs.inotify.max_user_watches=524288" >> /etc/sysctl.conf
        sysctl -p >/dev/null 2>&1
        log_success "inotify limits increased"
        ((FIXES_APPLIED++))
    fi
}

test_file_upload_flow() {
    log_action "Testing file upload flow..."
    
    # Find first upload directory
    TEST_UPLOAD_DIR=""
    for upload_dir in "${UPLOAD_DIRS[@]}"; do
        if [ -d "$upload_dir" ] && [ -w "$upload_dir" ]; then
            TEST_UPLOAD_DIR="$upload_dir"
            break
        fi
    done
    
    if [ -z "$TEST_UPLOAD_DIR" ]; then
        log_error "Tidak ada writable upload directory untuk testing"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    # Create test directory and file
    TEST_DIR="$TEST_UPLOAD_DIR/troubleshoot-test-$(date +%s)"
    mkdir -p "$TEST_DIR"
    
    TEST_FILE="$TEST_DIR/test-photo.jpg"
    log_debug "Creating test file: $TEST_FILE"
    
    # Create minimal JPEG
    printf '\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\x27 ($\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9' > "$TEST_FILE"
    
    if [ ! -f "$TEST_FILE" ]; then
        log_error "Gagal membuat test file"
        ((ISSUES_FOUND++))
        return 1
    fi
    
    log_success "Test file created"
    
    # Wait for monitoring to process
    sleep 2
    
    # Check permissions
    ACTUAL_OWNER=$(stat -c '%U:%G' "$TEST_FILE" 2>/dev/null || echo "unknown")
    log_info "Test file owner: $ACTUAL_OWNER"
    
    # Test HTTP access
    RELATIVE_PATH=$(echo "$TEST_FILE" | sed "s|.*public/uploads/|uploads/|")
    TEST_URL="http://localhost/$RELATIVE_PATH"
    
    log_debug "Testing URL: $TEST_URL"
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 10 2>/dev/null || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        log_success "✅ HTTP access test PASSED (Status: $HTTP_STATUS)"
    else
        log_error "❌ HTTP access test FAILED (Status: $HTTP_STATUS)"
        ((ISSUES_FOUND++))
        
        # Additional debugging
        log_debug "Curl verbose test:"
        curl -v "$TEST_URL" --max-time 5 2>&1 | head -20 || true
    fi
    
    # Check if monitoring service logged the event
    if [ -f "/var/log/advanced-photo-monitor.log" ]; then
        if grep -q "$(basename "$TEST_FILE")" "/var/log/advanced-photo-monitor.log"; then
            log_success "✅ Monitoring service detected test file"
        else
            log_warning "⚠️ Monitoring service may not have detected test file"
            ((ISSUES_FOUND++))
        fi
    fi
    
    # Cleanup
    rm -rf "$TEST_DIR"
    log_info "Test cleanup completed"
}

generate_report() {
    echo ""
    echo -e "${BOLD}${CYAN}"
    echo "================================================="
    echo "   TROUBLESHOOTING REPORT"
    echo "================================================="
    echo -e "${NC}"
    
    echo -e "${BOLD}📊 SUMMARY:${NC}"
    echo -e "  Issues Found: ${RED}$ISSUES_FOUND${NC}"
    echo -e "  Fixes Applied: ${GREEN}$FIXES_APPLIED${NC}"
    echo ""
    
    if [ $ISSUES_FOUND -eq 0 ]; then
        echo -e "${GREEN}🎉 NO ISSUES DETECTED!${NC}"
        echo -e "Auto-refresh system appears to be working correctly."
    elif [ $FIXES_APPLIED -ge $ISSUES_FOUND ]; then
        echo -e "${GREEN}✅ ALL ISSUES FIXED!${NC}"
        echo -e "Auto-refresh system should now be working correctly."
        echo -e "${YELLOW}Recommendation: Test photo upload di aplikasi.${NC}"
    else
        echo -e "${YELLOW}⚠️ SOME ISSUES REQUIRE MANUAL INTERVENTION${NC}"
        echo -e "Please review the errors above and fix manually."
    fi
    
    echo ""
    echo -e "${BOLD}🔄 RECOMMENDED NEXT STEPS:${NC}"
    echo "1. Test photo upload di aplikasi absensi"
    echo "2. Check detail absensi - foto harus muncul tanpa reload"
    echo "3. Monitor logs: tail -f /var/log/advanced-photo-monitor.log"
    echo "4. If issues persist, run script lagi atau contact support"
    echo ""
    
    echo -e "${BOLD}📋 MONITORING COMMANDS:${NC}"
    echo "• Service status: systemctl status advanced-photo-monitor nginx"
    echo "• Live logs: journalctl -u advanced-photo-monitor -f"
    echo "• Photo logs: tail -f /var/log/advanced-photo-monitor.log"
    echo "• Nginx logs: tail -f /var/log/nginx/photo_access.log"
    echo ""
}

# Main troubleshooting flow
main() {
    log_info "Starting comprehensive troubleshooting..."
    echo ""
    
    # Run all checks
    check_nginx_status
    echo ""
    
    check_monitoring_service
    echo ""
    
    check_upload_directories
    echo ""
    
    check_nginx_config
    echo ""
    
    check_system_resources
    echo ""
    
    test_file_upload_flow
    echo ""
    
    # If fixes were applied, restart services
    if [ $FIXES_APPLIED -gt 0 ]; then
        log_action "Restarting services after fixes..."
        systemctl reload nginx
        systemctl restart advanced-photo-monitor.service 2>/dev/null || true
        log_success "Services restarted"
        echo ""
    fi
    
    generate_report
}

# Run main function
main

echo -e "${BOLD}${GREEN}Troubleshooting completed.${NC}"
