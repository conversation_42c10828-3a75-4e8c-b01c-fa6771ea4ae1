#!/bin/bash

# CloudPanel Ubuntu 24.04 Real-time Photo Fix
# Mengatasi masalah foto upload yang membutuhkan reload nginx manual
# Dibuat khusus untuk CloudPanel dengan auto-detection dan optimasi real-time

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

echo -e "${BOLD}${CYAN}"
echo "=================================================="
echo "  CLOUDPANEL UBUNTU 24.04 REAL-TIME PHOTO FIX"
echo "=================================================="
echo -e "${NC}"

# Auto-detect CloudPanel site
detect_cloudpanel_site() {
    echo -e "${BLUE}🔍 Detecting CloudPanel site...${NC}"
    
    SITE_DIRS=()
    if [ -d "/home" ]; then
        while IFS= read -r -d '' dir; do
            if [[ "$dir" =~ ^/home/<USER>/]+/htdocs$ ]] && [ -f "$dir/package.json" ]; then
                SITE_DIRS+=("$dir")
            fi
        done < <(find /home -maxdepth 3 -name "htdocs" -type d -print0 2>/dev/null || true)
    fi
    
    if [ ${#SITE_DIRS[@]} -eq 0 ]; then
        echo -e "${RED}❌ No CloudPanel Next.js sites detected${NC}"
        exit 1
    fi
    
    # Use first detected site
    SITE_DIR="${SITE_DIRS[0]}"
    DOMAIN=$(basename "$(dirname "$SITE_DIR")")
    UPLOAD_DIR="$SITE_DIR/public/uploads"
    
    echo -e "${GREEN}✅ Detected site: ${CYAN}$DOMAIN${NC}"
    echo -e "${GREEN}✅ Site directory: ${CYAN}$SITE_DIR${NC}"
    echo -e "${GREEN}✅ Upload directory: ${CYAN}$UPLOAD_DIR${NC}"
    echo ""
}

# Install required packages
install_requirements() {
    echo -e "${BLUE}📦 Installing required packages...${NC}"
    
    apt-get update -qq
    apt-get install -y inotify-tools curl wget > /dev/null 2>&1
    
    echo -e "${GREEN}✅ Requirements installed${NC}"
    echo ""
}

# Optimize nginx configuration for real-time
configure_nginx_realtime() {
    echo -e "${BLUE}⚙️ Configuring nginx for real-time access...${NC}"
    
    # Find nginx site config
    NGINX_CONFIG=""
    if [ -f "/etc/nginx/sites-available/$DOMAIN" ]; then
        NGINX_CONFIG="/etc/nginx/sites-available/$DOMAIN"
    elif [ -f "/etc/nginx/sites-available/$DOMAIN.conf" ]; then
        NGINX_CONFIG="/etc/nginx/sites-available/$DOMAIN.conf"
    else
        # Search in nginx configs
        NGINX_CONFIG=$(find /etc/nginx -name "*$DOMAIN*" -type f | head -1)
    fi
    
    if [ -z "$NGINX_CONFIG" ] || [ ! -f "$NGINX_CONFIG" ]; then
        echo -e "${RED}❌ Nginx config for $DOMAIN not found${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Found nginx config: $NGINX_CONFIG${NC}"
    
    # Backup original config
    cp "$NGINX_CONFIG" "$NGINX_CONFIG.backup-$(date +%Y%m%d-%H%M%S)"
    
    # Check if uploads location already exists
    if grep -q "location.*uploads.*{" "$NGINX_CONFIG"; then
        echo -e "${YELLOW}⚠️ Replacing existing uploads location...${NC}"
        # Remove existing uploads location block
        sed -i '/location.*uploads.*{/,/}/d' "$NGINX_CONFIG"
    fi
    
    # Add new uploads configuration before the last closing brace
    sed -i '$i\\n    # Real-time photo upload optimization' "$NGINX_CONFIG"
    sed -i '$i\    location ~* ^/uploads/.*\\.(jpg|jpeg|png|gif|webp)$ {' "$NGINX_CONFIG"
    sed -i '$i\        root /home/'$DOMAIN'/htdocs/public;' "$NGINX_CONFIG"
    sed -i '$i\\n        # Disable all caching for immediate access' "$NGINX_CONFIG"
    sed -i '$i\        add_header Cache-Control "no-cache, no-store, must-revalidate" always;' "$NGINX_CONFIG"
    sed -i '$i\        add_header Pragma "no-cache" always;' "$NGINX_CONFIG"
    sed -i '$i\        add_header Expires "0" always;' "$NGINX_CONFIG"
    sed -i '$i\        add_header X-Photo-Served "realtime" always;' "$NGINX_CONFIG"
    sed -i '$i\\n        # Enable CORS for photo access' "$NGINX_CONFIG"
    sed -i '$i\        add_header Access-Control-Allow-Origin "*" always;' "$NGINX_CONFIG"
    sed -i '$i\        add_header Access-Control-Allow-Methods "GET, OPTIONS" always;' "$NGINX_CONFIG"
    sed -i '$i\\n        # Optimize file serving' "$NGINX_CONFIG"
    sed -i '$i\        sendfile on;' "$NGINX_CONFIG"
    sed -i '$i\        tcp_nopush on;' "$NGINX_CONFIG"
    sed -i '$i\        tcp_nodelay on;' "$NGINX_CONFIG"
    sed -i '$i\\n        # Error handling' "$NGINX_CONFIG"
    sed -i '$i\        try_files $uri =404;' "$NGINX_CONFIG"
    sed -i '$i\\n        # Logging for monitoring' "$NGINX_CONFIG"
    sed -i '$i\        access_log /var/log/nginx/photo_realtime.log;' "$NGINX_CONFIG"
    sed -i '$i\        error_log /var/log/nginx/photo_realtime_error.log;' "$NGINX_CONFIG"
    sed -i '$i\    }' "$NGINX_CONFIG"
    
    # Optimize main nginx.conf for real-time
    if ! grep -q "sendfile_max_chunk" /etc/nginx/nginx.conf; then
        sed -i '/sendfile on;/a\\tsendfile_max_chunk 512k;' /etc/nginx/nginx.conf
    fi
    
    if ! grep -q "tcp_nodelay on" /etc/nginx/nginx.conf; then
        sed -i '/sendfile on;/a\\ttcp_nodelay on;' /etc/nginx/nginx.conf
    fi
    
    # Test nginx configuration
    if nginx -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Nginx configuration updated successfully${NC}"
    else
        echo -e "${RED}❌ Nginx configuration error, restoring backup...${NC}"
        latest_backup=$(ls -t "$NGINX_CONFIG".backup-* | head -1)
        if [ -f "$latest_backup" ]; then
            cp "$latest_backup" "$NGINX_CONFIG"
        fi
        return 1
    fi
    
    echo ""
}

# Create real-time file monitoring service
create_realtime_monitor() {
    echo -e "${BLUE}🔄 Creating real-time file monitoring service...${NC}"
    
    # Create monitoring script
    cat > /usr/local/bin/realtime-photo-monitor.sh << EOF
#!/bin/bash

UPLOAD_DIR="$UPLOAD_DIR"
DOMAIN="$DOMAIN"
LOG_FILE="/var/log/realtime-photo-monitor.log"

log_message() {
    echo "[\$(date '+%Y-%m-%d %H:%M:%S')] \$1" >> "\$LOG_FILE"
}

log_message "Realtime Photo Monitor started for \$DOMAIN"

# Monitor upload directory for new files
inotifywait -m -r -e create,moved_to "\$UPLOAD_DIR" --format '%w%f %e' 2>/dev/null | while read file event; do
    if [[ "\$file" =~ \.(jpg|jpeg|png|gif|webp)$ ]]; then
        log_message "New photo detected: \$file (\$event)"
        
        # Fix permissions immediately
        chown $DOMAIN:$DOMAIN "\$file" 2>/dev/null || true
        chmod 644 "\$file" 2>/dev/null || true
        
        # Ensure parent directory permissions
        parent_dir=\$(dirname "\$file")
        chown $DOMAIN:$DOMAIN "\$parent_dir" 2>/dev/null || true
        chmod 755 "\$parent_dir" 2>/dev/null || true
        
        # Clear any nginx cache (if exists)
        nginx -s reload >/dev/null 2>&1 || true
        
        log_message "Photo processed: \$file - permissions fixed"
        
        # Test immediate access
        relative_path=\${file#\$UPLOAD_DIR}
        test_url="http://localhost/uploads\$relative_path"
        
        sleep 0.5  # Brief delay for file system sync
        
        status_code=\$(curl -s -o /dev/null -w "%{http_code}" "\$test_url" --max-time 3 2>/dev/null || echo "000")
        if [ "\$status_code" = "200" ]; then
            log_message "SUCCESS: Photo immediately accessible (\$status_code)"
        else
            log_message "WARNING: Photo not immediately accessible (\$status_code)"
        fi
    fi
done
EOF
    
    chmod +x /usr/local/bin/realtime-photo-monitor.sh
    
    # Create systemd service
    cat > /etc/systemd/system/realtime-photo-monitor.service << EOF
[Unit]
Description=Real-time Photo Upload Monitor for $DOMAIN
After=network.target nginx.service
Wants=nginx.service

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/realtime-photo-monitor.sh
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# Resource limits
LimitNOFILE=65536
MemoryMax=100M
CPUQuota=10%

[Install]
WantedBy=multi-user.target
EOF
    
    # Enable and start service
    systemctl daemon-reload
    systemctl enable realtime-photo-monitor.service
    systemctl start realtime-photo-monitor.service
    
    if systemctl is-active --quiet realtime-photo-monitor.service; then
        echo -e "${GREEN}✅ Real-time monitoring service started${NC}"
    else
        echo -e "${RED}❌ Failed to start monitoring service${NC}"
        return 1
    fi
    
    echo ""
}

# Optimize system for file watching
optimize_system() {
    echo -e "${BLUE}🚀 Optimizing system for real-time file monitoring...${NC}"
    
    # Increase inotify limits
    cat > /etc/sysctl.d/99-inotify.conf << EOF
# Optimize inotify for real-time file monitoring
fs.inotify.max_user_watches = 1048576
fs.inotify.max_user_instances = 256
fs.inotify.max_queued_events = 32768
EOF
    
    sysctl -p /etc/sysctl.d/99-inotify.conf >/dev/null 2>&1
    
    # Optimize upload directory permissions
    if [ -d "$UPLOAD_DIR" ]; then
        chown -R $DOMAIN:$DOMAIN "$UPLOAD_DIR"
        find "$UPLOAD_DIR" -type d -exec chmod 755 {} \;
        find "$UPLOAD_DIR" -type f -exec chmod 644 {} \;
    fi
    
    echo -e "${GREEN}✅ System optimized for real-time monitoring${NC}"
    echo ""
}

# Test real-time functionality
test_realtime_upload() {
    echo -e "${BLUE}🧪 Testing real-time upload functionality...${NC}"
    
    # Create test directory
    TEST_DIR="$UPLOAD_DIR/test-realtime-$(date +%s)"
    mkdir -p "$TEST_DIR"
    
    # Create test image using base64 encoded JPEG
    TEST_FILE="$TEST_DIR/test-realtime.jpg"
    echo "Creating minimal test JPEG file..."
    
    # Use echo with hex values to create a minimal JPEG
    echo -en '\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\x09\x09\x08\x0a\x0c\x14\x0d\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c\x20\x24\x2e\x27\x20\x28\x24\x1c\x1c\x28\x37\x29\x2c\x30\x31\x34\x34\x34\x1f\x27\x39\x3d\x38\x32\x3c\x2e\x33\x34\x32\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9' > "$TEST_FILE"
    
    if [ ! -f "$TEST_FILE" ]; then
        echo -e "${RED}❌ Failed to create test file${NC}"
        return 1
    fi
    
    # Wait for monitoring service to process
    echo -e "  ${CYAN}Waiting for real-time processing...${NC}"
    sleep 2
    
    # Test immediate HTTP access
    RELATIVE_PATH="${TEST_FILE#$UPLOAD_DIR}"
    TEST_URL="http://localhost/uploads$RELATIVE_PATH"
    
    echo -e "  ${CYAN}Testing URL: $TEST_URL${NC}"
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 5 2>/dev/null || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        echo -e "${GREEN}✅ SUCCESS: Photo accessible immediately without nginx reload!${NC}"
        echo -e "  ${GREEN}HTTP Status: $HTTP_STATUS${NC}"
        
        # Test cache headers
        CACHE_HEADER=$(curl -s -I "$TEST_URL" --max-time 5 2>/dev/null | grep -i "cache-control" || echo "")
        if [[ "$CACHE_HEADER" =~ no-cache ]]; then
            echo -e "  ${GREEN}✅ No-cache headers working correctly${NC}"
        fi
        
    else
        echo -e "${RED}❌ FAILED: Photo not accessible immediately${NC}"
        echo -e "  ${RED}HTTP Status: $HTTP_STATUS${NC}"
        
        # Try manual nginx reload test
        systemctl reload nginx
        sleep 1
        HTTP_STATUS_AFTER=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 5 2>/dev/null || echo "000")
        
        if [ "$HTTP_STATUS_AFTER" = "200" ]; then
            echo -e "  ${YELLOW}⚠️ Photo accessible after nginx reload (old behavior)${NC}"
        fi
    fi
    
    # Cleanup test files
    rm -rf "$TEST_DIR" 2>/dev/null || true
    
    echo ""
}

# Show monitoring status
show_status() {
    echo -e "${BLUE}📊 Real-time Monitoring Status:${NC}"
    
    # Service status
    if systemctl is-active --quiet realtime-photo-monitor.service; then
        echo -e "  • Monitor Service: ${GREEN}✅ ACTIVE${NC}"
    else
        echo -e "  • Monitor Service: ${RED}❌ INACTIVE${NC}"
    fi
    
    # Nginx status
    if systemctl is-active --quiet nginx; then
        echo -e "  • Nginx: ${GREEN}✅ ACTIVE${NC}"
    else
        echo -e "  • Nginx: ${RED}❌ INACTIVE${NC}"
    fi
    
    # Log status
    if [ -f "/var/log/realtime-photo-monitor.log" ]; then
        LOG_LINES=$(wc -l < /var/log/realtime-photo-monitor.log 2>/dev/null || echo "0")
        echo -e "  • Monitor Log: ${GREEN}✅ $LOG_LINES entries${NC}"
        
        echo -e "\n${CYAN}Recent monitoring activity:${NC}"
        tail -3 /var/log/realtime-photo-monitor.log 2>/dev/null | while read line; do
            echo -e "    ${BLUE}$line${NC}"
        done
    else
        echo -e "  • Monitor Log: ${YELLOW}⚠️ Not found${NC}"
    fi
    
    echo ""
}

# Main execution
main() {
    echo -e "${YELLOW}This script will fix the nginx reload issue for photo uploads${NC}"
    echo -e "${YELLOW}It will create a real-time monitoring system that automatically${NC}"
    echo -e "${YELLOW}handles permissions and nginx optimization without manual reloads.${NC}"
    echo ""
    
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Operation cancelled.${NC}"
        exit 0
    fi
    
    detect_cloudpanel_site
    install_requirements
    configure_nginx_realtime
    optimize_system
    create_realtime_monitor
    
    # Restart nginx with new configuration
    echo -e "${BLUE}🔄 Restarting nginx with new configuration...${NC}"
    systemctl restart nginx
    
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✅ Nginx restarted successfully${NC}"
    else
        echo -e "${RED}❌ Nginx restart failed${NC}"
        exit 1
    fi
    
    echo ""
    test_realtime_upload
    show_status
    
    echo -e "${BOLD}${GREEN}"
    echo "=================================================="
    echo "          REAL-TIME PHOTO FIX COMPLETED!"
    echo "=================================================="
    echo -e "${NC}"
    echo -e "${GREEN}✅ Photo uploads should now be accessible immediately${NC}"
    echo -e "${GREEN}✅ No more manual nginx reloads required${NC}"
    echo -e "${GREEN}✅ Real-time monitoring service is active${NC}"
    echo ""
    echo -e "${CYAN}Monitor logs with: ${YELLOW}tail -f /var/log/realtime-photo-monitor.log${NC}"
    echo -e "${CYAN}Check status with: ${YELLOW}systemctl status realtime-photo-monitor${NC}"
    echo -e "${CYAN}View nginx photo logs: ${YELLOW}tail -f /var/log/nginx/photo_realtime.log${NC}"
    echo ""
}

# Execute main function
main "$@"
