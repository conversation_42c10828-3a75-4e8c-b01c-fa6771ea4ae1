'use client';

import Image from 'next/image';
import { useState } from 'react';

interface SignatureImageProps {
  signatureUrl: string;
}

export default function SignatureImage({ signatureUrl }: SignatureImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  return (
    <div className="relative">
      {isLoading && (
        <div className="h-[100px] w-full bg-gray-100 animate-pulse" />
      )}
      
      {hasError && (
        <div className="h-[100px] w-full bg-red-50 flex items-center justify-center text-red-600 text-sm">
          Gagal memuat tanda tangan
        </div>
      )}
      
      <Image
        src={signatureUrl}
        alt="Tanda Tangan"
        width={300}
        height={100}
        className="h-auto max-w-full mx-auto"
        priority={false}
        unoptimized={true} // Required for base64 data URLs
        onLoadingComplete={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setHasError(true);
        }}
      />
    </div>
  );
}