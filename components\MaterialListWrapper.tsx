'use client';

import { useRouter } from 'next/navigation';
import MaterialList from './MaterialList';

interface MaterialPelatihan {
  id: string;
  nama_file: string;
  path_file: string;
  size: number;
  mime_type: string;
  createdAt: Date;
}

interface MaterialListWrapperProps {
  materials: MaterialPelatihan[];
  itemsPerPage?: number;
  canUpload?: boolean;
  canDelete?: boolean;
  pelatihanId?: string;
}

export default function MaterialListWrapper({
  materials,
  itemsPerPage = 5,
  canUpload = false,
  canDelete = false,
  pelatihanId
}: MaterialListWrapperProps) {
  const router = useRouter();

  const handleMaterialDeleted = () => {
    // Refresh the page data
    router.refresh();
  };

  return (
    <MaterialList
      materials={materials}
      itemsPerPage={itemsPerPage}
      canUpload={canUpload}
      canDelete={canDelete}
      pelatihanId={pelatihanId}
      onMaterialDeleted={handleMaterialDeleted}
    />
  );
}
