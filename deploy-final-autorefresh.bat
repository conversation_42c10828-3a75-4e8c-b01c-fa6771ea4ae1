@echo off
setlocal enabledelayedexpansion

:: DEPLOY FINAL AUTO-REFRESH SOLUTION
:: Upload dan eksekusi advanced-cloudpanel-autorefresh-fix.sh

title Deploy Final Auto-Refresh Solution

echo.
echo ===============================================
echo    DEPLOY FINAL AUTO-REFRESH SOLUTION
echo    Advanced CloudPanel Ubuntu 24.04 Fix
echo ===============================================
echo.

:: Configuration
set VPS_IP=*************
set VPS_USER=root
set SCRIPT_NAME=advanced-cloudpanel-autorefresh-fix.sh

echo [36m[INFO][0m Target VPS: %VPS_IP%
echo [36m[INFO][0m Script: %SCRIPT_NAME%
echo.

:: Check if script exists
if not exist "%~dp0%SCRIPT_NAME%" (
    echo [31m[ERROR][0m Script tidak ditemukan: %SCRIPT_NAME%
    echo [33m[INFO][0m Pastikan file ada di folder yang sama dengan .bat ini
    pause
    exit /b 1
)

echo [35m[ACTION][0m Uploading advanced fix script...

:: Upload script
scp "%~dp0%SCRIPT_NAME%" %VPS_USER%@%VPS_IP%:/tmp/%SCRIPT_NAME%

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Upload failed!
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script uploaded successfully
echo.

echo [35m[ACTION][0m Making script executable...

:: Make executable
ssh %VPS_USER%@%VPS_IP% "chmod +x /tmp/%SCRIPT_NAME%"

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to make script executable!
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script made executable
echo.

echo [33m[WARNING][0m Siap menjalankan advanced auto-refresh fix!
echo.
echo [36m[INFO][0m Script ini akan:
echo   • Melakukan backup konfigurasi nginx
echo   • Mengoptimalkan nginx untuk real-time file access
echo   • Deploy advanced monitoring system dengan inotify
echo   • Auto-fix permissions secara real-time
echo   • Optimasi sistem untuk file watching
echo   • Deploy monitoring dashboard dan troubleshooter
echo.

set /p confirm="Lanjutkan deployment? (y/n): "

if /i not "%confirm%"=="y" (
    echo [33m[INFO][0m Deployment dibatalkan
    pause
    exit /b 0
)

echo.
echo [35m[ACTION][0m Executing advanced auto-refresh fix...
echo [33m[INFO][0m This may take 2-3 minutes...
echo.

:: Execute the script
ssh %VPS_USER%@%VPS_IP% "cd /tmp && ./%SCRIPT_NAME%"

set exit_code=%errorlevel%

echo.
echo ===============================================

if %exit_code% equ 0 (
    echo [32m[SUCCESS][0m ✅ ADVANCED AUTO-REFRESH FIX COMPLETED!
    echo.
    echo [36m[INFO][0m FOTO UPLOAD SEKARANG REAL-TIME!
    echo.
    echo [33m📊 TEST INSTRUCTIONS:[0m
    echo   1. Buka aplikasi absensi
    echo   2. Upload foto baru
    echo   3. Langsung buka detail absensi
    echo   4. Foto harus muncul TANPA reload nginx
    echo.
    echo [33m🔧 MONITORING COMMANDS:[0m
    echo   ssh %VPS_USER%@%VPS_IP% "systemctl status advanced-photo-monitor"
    echo   ssh %VPS_USER%@%VPS_IP% "tail -f /var/log/advanced-photo-monitor.log"
    echo   ssh %VPS_USER%@%VPS_IP% "journalctl -u advanced-photo-monitor -f"
    echo.
    echo [33m📋 DASHBOARD COMMANDS:[0m
    echo   ssh %VPS_USER%@%VPS_IP% "/tmp/monitor-autorefresh-status.sh"
    echo   ssh %VPS_USER%@%VPS_IP% "/tmp/troubleshoot-autorefresh.sh"
    echo.
    
) else (
    echo [31m[ERROR][0m ❌ DEPLOYMENT FAILED!
    echo.
    echo [33m🔍 TROUBLESHOOTING:[0m
    echo   ssh %VPS_USER%@%VPS_IP% "tail -20 /var/log/nginx/error.log"
    echo   ssh %VPS_USER%@%VPS_IP% "nginx -t"
    echo   ssh %VPS_USER%@%VPS_IP% "systemctl status nginx"
    echo.
)

echo ===============================================
echo.

:: Upload monitoring scripts
echo [35m[ACTION][0m Uploading monitoring scripts...

for %%s in (monitor-autorefresh-status.sh troubleshoot-autorefresh.sh) do (
    if exist "%~dp0%%s" (
        echo [36m[INFO][0m Uploading %%s...
        scp "%~dp0%%s" %VPS_USER%@%VPS_IP%:/tmp/%%s
        ssh %VPS_USER%@%VPS_IP% "chmod +x /tmp/%%s"
    )
)

echo.
echo [33m🎯 QUICK TEST:[0m
set /p test_now="Test upload real-time sekarang? (y/n): "

if /i "%test_now%"=="y" (
    echo.
    echo [35m[ACTION][0m Opening application for testing...
    start "" "https://kegiatan.bpmpkaltim.id"
    
    echo [36m[INFO][0m Silakan:
    echo   1. Login ke aplikasi
    echo   2. Buat absensi baru atau edit yang ada
    echo   3. Upload foto
    echo   4. Langsung buka detail absensi
    echo   5. Foto harus muncul TANPA reload
    echo.
)

echo [33m📞 SUPPORT:[0m
echo   Jika masih ada masalah, jalankan:
echo   %~dp0troubleshoot-autorefresh.bat
echo.

pause
