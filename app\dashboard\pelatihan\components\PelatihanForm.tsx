import { Controller } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, Form } from '@/components/ui/form';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Trash2, Plus } from 'lucide-react';
import { useState } from 'react';
import usePelatihanForm from '../hooks/usePelatihanForm';
import { jenjangOptions } from '@/lib/constants';

export default function PelatihanForm() {
  // Tambahkan state untuk dialog konfirmasi
  const [showConfirmation, setShowConfirmation] = useState(false);
  
  const {
    form, // Dapatkan form object lengkap
    handleSubmit,
    errors,
    isSubmitting,
    jenjangTargets,
    addJenjangTarget,
    removeJenjangTarget,
    updateJenjangTarget
  } = usePelatihanForm();

  // Tambahkan fungsi untuk konfirmasi dan pembatalan
  const confirmSubmit = () => {
    setShowConfirmation(false);
  };

  const cancelSubmit = () => {
    setShowConfirmation(false);
  };

  return (
    <>
      <Form {...form}>
        <form 
          onSubmit={(e) => {
            e.preventDefault();
            console.log("Form submit triggered in PelatihanForm component");
            handleSubmit(e);
          }} 
          className="space-y-6"
        >
          <Card>
            <CardContent className="pt-6">
              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="nama"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nama Kegiatan</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Masukkan nama kegiatan" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tempat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tempat Kegiatan</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Masukkan tempat kegiatan" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tgl_mulai"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tanggal Mulai</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tgl_berakhir"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tanggal Berakhir</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">Jenjang & Target Peserta</h3>
                <Button type="button" variant="outline" size="sm" onClick={addJenjangTarget}>
                  <Plus className="w-4 h-4 mr-2" /> Tambah Jenjang
                </Button>
              </div>

              {jenjangTargets.map((item, index) => (
                <div key={item.id} className="grid gap-4 p-3 mb-4 border rounded-md md:grid-cols-3">
                  <Controller
                    control={form.control}
                    name={`jenjangTargets.${index}.jenjang`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jenjang</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={(value) => updateJenjangTarget(item.id, 'jenjang', value)}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih jenjang" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {jenjangOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.jenjangTargets?.[index]?.jenjang && (
                          <FormMessage>{errors.jenjangTargets[index]?.jenjang?.message}</FormMessage>
                        )}
                      </FormItem>
                    )}
                  />

                  <Controller
                    control={form.control}
                    name={`jenjangTargets.${index}.target_peserta`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Target Peserta</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            value={field.value}
                            onChange={(e) => updateJenjangTarget(item.id, 'target_peserta', e.target.value)}
                          />
                        </FormControl>
                        {errors.jenjangTargets?.[index]?.target_peserta && (
                          <FormMessage>{errors.jenjangTargets[index]?.target_peserta?.message}</FormMessage>
                        )}
                      </FormItem>
                    )}
                  />

                  <div className="flex items-end">
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => removeJenjangTarget(item.id)}
                      disabled={jenjangTargets.length <= 1}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
              {errors.jenjangTargets?.message && (
                <p className="mt-2 text-sm font-medium text-destructive">{errors.jenjangTargets.message}</p>
              )}
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Menyimpan...' : 'Simpan Kegiatan'}
            </Button>
          </div>
        </form>
      </Form>

      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Tambah Kegiatan</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menambahkan kegiatan ini? Pastikan semua data sudah benar.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelSubmit}>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={confirmSubmit} disabled={isSubmitting}>
              {isSubmitting ? 'Menyimpan...' : 'Ya, Tambahkan'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}









