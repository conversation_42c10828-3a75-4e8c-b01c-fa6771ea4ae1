import { prisma } from '@/lib/prisma';
import { logger } from './logger';
import { withErrorLogging } from './consoleInterceptor';

// Definisikan tipe untuk hasil operasi API dan ekspor agar bisa digunakan di file lain
export interface ApiResult<T> {
  status: number;
  data: T;
}

/**
 * Helper untuk mendapatkan form dengan validasi akses
 */
export async function getFormWithAccessCheck(
  formId: string,
  userId: string | null,
  includeQuestions: boolean = false
): Promise<ApiResult<any>> {
  try {
    const form = await prisma.form.findUnique({
      where: { id: formId },
      include: {
        questions: includeQuestions ? {
          orderBy: { order: 'asc' }
        } : false,
        pelatihan: {
          select: {
            id: true,
            nama: true
          }
        }
      }
    });

    // Form tidak ditemukan
    if (!form) {
      return { status: 404, data: { error: 'Form not found' } };
    }

    // Cek akses - hanya pemilik form yang bisa mengedit
    if (userId && form.userId !== userId) {
      return { status: 403, data: { error: 'You do not have permission to access this form' } };
    }

    return { status: 200, data: form };
  } catch (error) {
    logger.error('Error getting form', error instanceof Error ? error : new Error(String(error)));
    return { status: 500, data: { error: 'Failed to get form' } };
  }
}

/**
 * Helper untuk memperbarui form
 */
export async function updateForm(
  formId: string,
  userId: string,
  data: any
): Promise<ApiResult<any>> {
  try {
    // Cek akses terlebih dahulu
    const accessCheck = await getFormWithAccessCheck(formId, userId);
    
    if (accessCheck.status !== 200) {
      return accessCheck;
    }
    
    // Update form
    const updatedForm = await prisma.form.update({
      where: { id: formId },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
    
    return { status: 200, data: updatedForm };
  } catch (error) {
    logger.error('Error updating form', error instanceof Error ? error : new Error(String(error)));
    return { status: 500, data: { error: 'Failed to update form' } };
  }
}

/**
 * Helper untuk menambahkan pertanyaan ke form
 */
export async function addQuestionToForm(
  formId: string,
  userId: string,
  questionData: any
): Promise<ApiResult<any>> {
  try {
    // Cek akses terlebih dahulu
    const accessCheck = await getFormWithAccessCheck(formId, userId);
    
    if (accessCheck.status !== 200) {
      return accessCheck;
    }
    
    // Tambahkan pertanyaan
    const newQuestion = await prisma.question.create({
      data: {
        ...questionData,
        formId
      }
    });
    
    return { status: 201, data: newQuestion };
  } catch (error) {
    logger.error('Error adding question', error instanceof Error ? error : new Error(String(error)));
    return { status: 500, data: { error: 'Failed to add question' } };
  }
}

/**
 * Helper untuk mengupdate pertanyaan
 */
export async function updateQuestion(
  formId: string,
  questionId: string,
  userId: string,
  questionData: any
): Promise<ApiResult<any>> {
  logger.info('updateQuestion helper called', { 
    questionId, 
    questionType: questionData.type,
    questionDataPreview: JSON.stringify(questionData).substring(0, 100) + '...'
  });

  try {
    // Cek akses terlebih dahulu
    const accessCheck = await getFormWithAccessCheck(formId, userId);
    
    if (accessCheck.status !== 200) {
      return accessCheck;
    }
    
    // Update pertanyaan
    // Pastikan type adalah nilai enum yang valid jika disediakan
    if (questionData.type) {
      // Validasi type terhadap enum QuestionType di Prisma
      const validTypes = ['SHORT_TEXT', 'LONG_TEXT', 'SINGLE_CHOICE', 'MULTIPLE_CHOICE', 
                         'CHECKLIST', 'DROPDOWN', 'DATE', 'TIME', 'EMAIL', 'PHONE', 'NUMBER'];
      
      if (!validTypes.includes(questionData.type)) {
        return { 
          status: 400, 
          data: { error: `Invalid question type: ${questionData.type}` } 
        };
      }
    }
    
    const updatedQuestion = await prisma.question.update({
      where: {
        id: questionId,
      },
      data: {
        ...questionData,
        updatedAt: new Date()
      }
    });
    
    return { status: 200, data: updatedQuestion };
  } catch (error) {
    logger.error('Error updating question', error instanceof Error ? error : new Error(String(error)));
    return { status: 500, data: { error: 'Failed to update question' } };
  }
}

/**
 * Helper untuk menghapus pertanyaan
 */
export async function deleteQuestion(
  formId: string,
  questionId: string,
  userId: string
): Promise<ApiResult<any>> {
  try {
    // Cek akses terlebih dahulu
    const accessCheck = await getFormWithAccessCheck(formId, userId);
    
    if (accessCheck.status !== 200) {
      return accessCheck;
    }
    
    // Hapus pertanyaan
    await prisma.question.delete({
      where: {
        id: questionId,
      }
    });
    
    return { status: 200, data: { message: 'Question deleted successfully' } };
  } catch (error) {
    logger.error('Error deleting question', error instanceof Error ? error : new Error(String(error)));
    return { status: 500, data: { error: 'Failed to delete question' } };
  }
}

/**
 * Helper untuk mengubah urutan pertanyaan
 */
export const reorderQuestions = withErrorLogging(async (
  formId: string,
  userId: string,
  questions: Array<{ id: string, order: number }>
) => {
  // Cek akses terlebih dahulu
  const { status, data: form } = await getFormWithAccessCheck(formId, userId);
  
  if (status !== 200) {
    return { status, data: form };
  }
  
  // Update urutan pertanyaan
  try {
    await prisma.$transaction(
      questions.map(question => 
        prisma.question.update({
          where: { 
            id: question.id,
            formId // Pastikan pertanyaan milik form yang benar
          },
          data: { order: question.order }
        })
      )
    );
    
    return { status: 200, data: { success: true } };
  } catch (error) {
    logger.error('Error reordering questions', error instanceof Error ? error : new Error(String(error)));
    return { status: 500, data: { error: 'Failed to reorder questions' } };
  }
}, 'Error in reorderQuestions function');










