'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { submitAbsensi } from '../action';
import SignatureCanvas from '../../../../components/SignatureCanvas';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';

// Dynamic import untuk Map component karena Leaflet membutuhkan window object
const MapComponent = dynamic(
  () => import('../../../../components/MapPicker'),
  { 
    ssr: false,
    loading: () => <div className="h-[300px] bg-gray-100 animate-pulse flex items-center justify-center">Memuat peta...</div>
  }
);

// Import shadcn/ui components
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Button } from '../../../../components/ui/button';
import { formatIndonesiaDate } from '../../../../utils/dateUtils';


// Definisikan interface untuk data pelatihan
interface PelatihanData {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: string | Date;
  tgl_berakhir: string | Date;
  link_absensi: string;
}

export default function AbsensiPage() {
  const params = useParams<{ link: string }>();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [pelatihan, setPelatihan] = useState<PelatihanData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    nama: '',
    nip_nik: '',
    jabatan: '',
    unit_kerja: '',
    no_hp: '',
    tanda_tangan: '',
    latitude: '',
    longitude: '',
    alamat: ''
  });
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    async function verifyLink() {
      try {
        const response = await fetch(`/api/verify-absensi?link=${params.link}`);
        const data = await response.json();

        if (data.valid) {
          setPelatihan(data.pelatihan);
          setIsLoading(false);
        } else {
          setError(data.message);
          setIsLoading(false);
        }      } catch (_err) {
        setError('Terjadi kesalahan saat memverifikasi link');
        setIsLoading(false);
      }
    }

    if (params.link) {
      verifyLink();
    }
  }, [params.link]);

  const validatePhoneNumber = (phone: string): boolean => {
    const pattern = /^(\+62|62|0)8[1-9][0-9]{6,10}$/;
    return pattern.test(phone);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    // Real-time validation
    if (name === 'no_hp' && value) {
      if (!validatePhoneNumber(value)) {
        setFieldErrors(prev => ({ 
          ...prev, 
          [name]: 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx'
        }));
      } else {
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
  };

  const handleSignatureChange = (dataUrl: string) => {
    setFormData((prev) => ({ ...prev, tanda_tangan: dataUrl }));
    
    // Clear signature error when signature is provided
    if (dataUrl) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.tanda_tangan;
        return newErrors;
      });
    }
  };

  const handleLocationSelect = (lat: number, lng: number, address: string) => {
    setFormData((prev) => ({
      ...prev,
      latitude: lat.toString(),
      longitude: lng.toString(),
      alamat: address
    }));
    
    // Clear location error when location is provided
    if (lat && lng) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.location;
        return newErrors;
      });
    }
  };
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.nama) {
      errors.nama = 'Nama lengkap harus diisi';
    }
    
    if (!formData.unit_kerja) {
      errors.unit_kerja = 'Unit kerja harus diisi';
    }
    
    if (!formData.no_hp) {
      errors.no_hp = 'Nomor HP harus diisi';
    } else if (!validatePhoneNumber(formData.no_hp)) {
      errors.no_hp = 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx';
    }
    
    if (!formData.tanda_tangan) {
      errors.tanda_tangan = 'Tanda tangan harus diisi';
    }

    if (!formData.latitude || !formData.longitude) {
      errors.location = 'Lokasi harus ditentukan';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error("Mohon periksa kembali isian form");
      return;
    }

    setIsSubmitting(true);

    try {      // Import timezone conversion function for consistency
      const { prepareAbsensiForSubmission } = await import('@/utils/frontendTimezoneHelpers');
      
      // Process form data with timezone conversion
      const submissionData = {
        ...prepareAbsensiForSubmission(formData),
        pelatihanId: params.link as string
      };
      
      const result = await submitAbsensi(submissionData);
      
      if (result.success) {
        toast.success("Absensi berhasil disimpan");
        setTimeout(() => {
          router.push(`/public-pages/absensi/${params.link}/success`);
        }, 1500);
      } else {
        toast.error(`Gagal menyimpan absensi: ${result.message}`);
        setIsSubmitting(false);
      }    } catch (_err) {
      toast.error("Terjadi kesalahan saat mengirim data");
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen px-4 bg-gray-50">
        <div className="w-full max-w-md p-6 bg-white rounded-md shadow-md">
          <div className="w-10 h-10 mx-auto border-b-2 border-blue-600 rounded-full animate-spin"></div>
          <p className="mt-4 text-center text-gray-600">Memuat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen px-4 bg-gray-50">
        <div className="w-full max-w-md p-6 bg-white rounded-md shadow-md">
          <h1 className="mb-4 text-xl font-bold text-center text-red-600">Link Tidak Valid</h1>
          <div className="mb-4 text-red-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="mb-6 text-center text-gray-700">{error}</p>
          <div className="text-center">
            <Button 
              variant="outline" 
              onClick={() => router.push('/')}
              className="text-blue-600 border-blue-600 hover:bg-blue-50"
            >
              Kembali ke Beranda
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (    <div className="min-h-screen px-3 py-3 bg-gray-50 sm:px-4 sm:py-6">
      <div className="w-full max-w-2xl mx-auto">
        {/* Header dengan tinggi lebih kecil */}
        <div className="mb-3 overflow-hidden rounded-t-md">
          <div className="bg-blue-600">
            <h1 className="px-3 py-2 text-base font-bold text-white sm:px-4 sm:py-3 sm:text-lg">
              Form Absensi Kegiatan
            </h1>
            <p className="px-3 pb-2 text-xs text-white/90 sm:px-4 sm:pb-3 sm:text-sm">
              {pelatihan?.nama}
            </p>
          </div>
        </div>
        
        <div className="overflow-hidden bg-white shadow rounded-b-md">
          {/* Informasi pelatihan yang lebih ringkas */}
          <div className="p-4 mb-3 bg-blue-50">
            <h2 className="mb-2 text-sm font-semibold text-blue-800">Informasi Kegiatan</h2>
            
            <div className="space-y-1 text-xs">
              <div className="grid grid-cols-[100px_1fr] gap-1">
                <span className="font-medium">Nama Pelatihan:</span>
                <span>{pelatihan?.nama}</span>
              </div>
              
              <div className="grid grid-cols-[100px_1fr] gap-1">
                <span className="font-medium">Tempat:</span>
                <span>{pelatihan?.tempat}</span>
              </div>
              
              <div className="grid grid-cols-[100px_1fr] gap-1">
                <span className="font-medium">Tanggal:</span>
                <span>
                  {formatIndonesiaDate(pelatihan?.tgl_mulai)} - {formatIndonesiaDate(pelatihan?.tgl_berakhir)}
                </span>
              </div>
            </div>
          </div>
          
          <div className="p-4">
            
            <form onSubmit={handleSubmit} className="space-y-3">
              <div>
                <Label htmlFor="nama" className="text-xs font-medium">
                  Nama Lengkap <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="nama"
                  name="nama"
                  value={formData.nama}
                  onChange={handleChange}
                  required
                  placeholder="Masukkan nama lengkap Anda"
                  className="h-8 py-1 mt-1 text-sm"
                />
                {fieldErrors.nama && (
                  <p className="mt-0.5 text-xs text-red-600">{fieldErrors.nama}</p>
                )}
              </div>
              
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                <div>
                  <Label htmlFor="nip_nik" className="text-xs font-medium">NIP/NIK</Label>
                  <Input
                    id="nip_nik"
                    name="nip_nik"
                    value={formData.nip_nik}
                    onChange={handleChange}
                    placeholder="NIP/NIK jika ada"
                    className="h-8 py-1 mt-1 text-sm"
                  />
                </div>
                
                <div>
                  <Label htmlFor="jabatan" className="text-xs font-medium">Jabatan</Label>
                  <Input
                    id="jabatan"
                    name="jabatan"
                    value={formData.jabatan}
                    onChange={handleChange}
                    placeholder="Jabatan Anda"
                    className="h-8 py-1 mt-1 text-sm"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="unit_kerja" className="text-xs font-medium">
                  Unit Kerja <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="unit_kerja"
                  name="unit_kerja"
                  value={formData.unit_kerja}
                  onChange={handleChange}
                  required
                  placeholder="Contoh: BPMP Prov. Kaltim"
                  className="h-8 py-1 mt-1 text-sm"
                />
                {fieldErrors.unit_kerja && (
                  <p className="mt-0.5 text-xs text-red-600">{fieldErrors.unit_kerja}</p>
                )}
              </div>
                <div>
                <Label htmlFor="no_hp" className="text-xs font-medium">
                  Nomor HP <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="no_hp"
                  name="no_hp"
                  type="tel"
                  value={formData.no_hp}
                  onChange={handleChange}
                  required
                  placeholder="Contoh: 08123456789"
                  className="h-8 py-1 mt-1 text-sm"
                />
                {fieldErrors.no_hp && (
                  <p className="mt-0.5 text-xs text-red-600">{fieldErrors.no_hp}</p>
                )}
              </div>
                <div className="pt-1">
                <Label className="text-xs font-medium">
                  Lokasi <span className="text-red-500">*</span>
                </Label>
                <div className="mt-1 overflow-hidden border rounded-md">
                  <MapComponent
                    onLocationSelect={handleLocationSelect}
                  />
                </div>
                {fieldErrors.location && (
                  <p className="mt-0.5 text-xs text-red-600">{fieldErrors.location}</p>
                )}
              </div>
              
              <div className="pt-1">
                <Label className="text-xs font-medium">
                  Tanda Tangan <span className="text-red-500">*</span>
                </Label>
                <div className="mt-1 border rounded-md">
                  <SignatureCanvas
                    onSignatureChange={handleSignatureChange}
                  />
                </div>
               
                {fieldErrors.tanda_tangan && (
                  <p className="mt-0.5 text-xs text-red-600">{fieldErrors.tanda_tangan}</p>
                )}              </div>
              
              <div className="pt-3">
                <Button 
                  type="submit" 
                  className="w-full text-sm text-white bg-blue-600 hover:bg-blue-700 h-9"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Menyimpan...
                    </>
                  ) : 'Simpan Absensi'}
                </Button>
              </div>
            </form>
          </div>
          
         
        </div>
      </div>
    </div>
  );
}