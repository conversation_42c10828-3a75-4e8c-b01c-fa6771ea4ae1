// components/ToastContainer.tsx
'use client';

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { createPortal } from 'react-dom';
import Toast, { Toast as ToastType, ToastType as ToastVariant } from './Toast';

interface ToastContextType {
  showToast: (message: string, type: ToastVariant, duration?: number) => void;
  hideToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

interface ToastProviderProps {
  children: ReactNode;
}

export function ToastProvider({ children }: ToastProviderProps) {
  const [toasts, setToasts] = useState<ToastType[]>([]);
  const [isBrowser, setIsBrowser] = useState(false);

  // Pindahkan ke client side setelah render pertama
  useEffect(() => {
    setIsBrowser(true);
  }, []);

  // Fungsi untuk menyembunyikan toast
  const hideToast = useCallback((id: string) => {
    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));
  }, []);
  
  // Fungsi untuk menampilkan toast baru
  const showToast = useCallback((message: string, type: ToastVariant, duration = 5000) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast = { id, message, type, duration };
    
    setToasts((currentToasts) => [...currentToasts, newToast]);
    
    // Hapus toast secara otomatis setelah durasi tertentu
    if (duration !== Infinity) {
      setTimeout(() => {
        hideToast(id);
      }, duration);
    }
    
    return id;
  }, [hideToast]);
  
  // Buat portal untuk toast container
  const toastContainer = isBrowser
    ? createPortal(
        <div className="fixed top-0 right-0 z-50 w-full p-4 pointer-events-none sm:max-w-sm">
          <div className="flex flex-col items-end space-y-2">
            {toasts.map((toast) => (
              <div key={toast.id} className="w-full pointer-events-auto">
                <Toast toast={toast} onClose={() => hideToast(toast.id)} />
              </div>
            ))}
          </div>
        </div>,
        document.body
      )
    : null;

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      {toastContainer}
    </ToastContext.Provider>
  );
}