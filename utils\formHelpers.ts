import { randomBytes } from 'crypto';
import { formatIndonesiaDate, formatIndonesiaDateTime } from './dateUtils';

// Define question types explicitly based on your schema.prisma
type QuestionType = 
  | 'SHORT_TEXT'
  | 'LONG_TEXT'
  | 'SINGLE_CHOICE'
  | 'MULTIPLE_CHOICE'
  | 'DROPDOWN'
  | 'DATE'
  | 'TIME'
  | 'FILE_UPLOAD'
  | 'SCALE'
  | 'EMAIL'
  | 'PHONE'
  | 'NUMBER';

/**
 * Generate unique share link for forms
 */
export function generateFormShareLink() {
  return randomBytes(16).toString('hex');
}

/**
 * Format question type for display
 */
export function formatQuestionType(type: QuestionType): string {
  return type.replace(/_/g, ' ').toLowerCase();
}

/**
 * Check if a question type supports options
 */
export function questionTypeSupportsOptions(type: QuestionType): boolean {
  return [
    'MULTIPLE_CHOICE',
    'SINGLE_CHOICE',
    'DROPDOWN',
    'SCALE'
  ].includes(type);
}

/**
 * Get default options for question types that support options
 */
export function getDefaultOptionsForType(type: QuestionType): any {
  switch (type) {
    case 'MULTIPLE_CHOICE':
    case 'SINGLE_CHOICE':
    case 'DROPDOWN':
      return { options: ['Option 1', 'Option 2', 'Option 3'] };
    case 'SCALE':
      return { min: 1, max: 5, minLabel: 'Low', maxLabel: 'High' };
    default:
      return {};
  }
}

/**
 * Validate form response data against questions
 */
export function validateFormResponse(
  questions: any[], 
  formData: Record<string, any>
): { valid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  
  for (const question of questions) {
    const value = formData[question.id];
    
    // Check required fields
    if (question.isRequired && (!value || (Array.isArray(value) && value.length === 0))) {
      errors[question.id] = 'This field is required';
      continue;
    }
    
    // Skip validation for empty optional fields
    if (!value && !question.isRequired) {
      continue;
    }
    
    // Type-specific validation
    switch (question.type) {
      case 'EMAIL':
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          errors[question.id] = 'Please enter a valid email address';
        }
        break;
        
      case 'PHONE':
        if (value && !/^[0-9+\-\s()]{8,15}$/.test(value)) {
          errors[question.id] = 'Please enter a valid phone number';
        }
        break;
        
      case 'NUMBER':
        if (value && isNaN(Number(value))) {
          errors[question.id] = 'Please enter a valid number';
        }
        break;
        
      case 'DATE':
        if (value && isNaN(new Date(value).getTime())) {
          errors[question.id] = 'Please enter a valid date';
        }
        break;
    }
  }
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Format form response data for display or export
 */
export function formatFormResponses(
  form: { questions: any[] }, 
  responses: Array<{ submittedAt: Date, respondentName?: string, answers: any[] }>
) {
  // Create headers
  const headers = ['Respondent', 'Submitted At', ...form.questions.map(q => q.title)];
  
  // Format each response
  const rows = responses.map(response => {
    const row: Record<string, any> = {
      respondent: response.respondentName || 'Anonymous',
      submittedAt: formatDateTime(response.submittedAt)
    };
    
    // Add answer for each question
    form.questions.forEach(question => {
      const answer = response.answers.find(a => a.questionId === question.id);
      row[question.id] = formatAnswerValue(answer?.value, question.type);
    });
    
    return row;
  });
  
  return { headers, rows };
}

/**
 * Format answer value based on question type
 */
function formatAnswerValue(value: string | null, type: QuestionType): string {
  if (!value) return '-';
  
  switch (type) {
    case 'DATE':
      return formatIndonesiaDate(new Date(value));
    case 'MULTIPLE_CHOICE':
      try {
        return JSON.parse(value).join(', ');
      } catch (_e) {
        return value;
      }
    default:
      return value;
  }
}

/**
 * Format date for display
 */
function _formatDate(date: Date | string | null | undefined): string {
  return formatIndonesiaDate(date);
}

/**
 * Format date and time for display
 */
function formatDateTime(date: Date | string | null | undefined): string {
  return formatIndonesiaDateTime(date);
}

/**
 * Export form responses to CSV
 */
export function exportResponsesToCsv(
  form: { questions: any[] }, 
  responses: Array<{ submittedAt: Date, respondentName?: string, answers: any[] }>
): string {
  const { headers, rows } = formatFormResponses(form, responses);
  
  // Create CSV header row
  let csv = headers.join(',') + '\n';
  
  // Add data rows
  rows.forEach(row => {
    const csvRow = headers.map(header => {
      const value = header === 'Respondent' ? row.respondent : 
                    header === 'Submitted At' ? row.submittedAt :
                    row[form.questions.find(q => q.title === header)?.id || ''] || '-';
      
      // Escape commas and quotes
      return `"${String(value).replace(/"/g, '""')}"`;
    });
    
    csv += csvRow.join(',') + '\n';
  });
  
  return csv;
}

