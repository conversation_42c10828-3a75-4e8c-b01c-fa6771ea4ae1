// File: /app/api/verify-absensi-internal/route.ts
import { NextResponse, NextRequest } from 'next/server';
import { validateAbsensiInternalLink } from '../../../lib/validateLink';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const link = searchParams.get('link');

    if (!link) {
      return NextResponse.json(
        { valid: false, message: 'Link tidak valid' },
        { status: 400 }
      );
    }    const validation = await validateAbsensiInternalLink(link);

    return NextResponse.json(validation);
  } catch (_error) {
    return NextResponse.json(
      { valid: false, message: '<PERSON><PERSON><PERSON><PERSON> k<PERSON> server' },
      { status: 500 }
    );
  }
}
