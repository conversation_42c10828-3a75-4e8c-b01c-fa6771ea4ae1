import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { getCurrentUser } from '../../../../lib/auth';

// Interface untuk objek panitia dari hasil query
interface PanitiaPegawai {
  pegawaiId: string;
}

/**
 * API endpoint untuk memeriksa ketersediaan pegawai pada rentang tanggal tertentu
 * Digunakan untuk memfilter dropdown pegawai pada form panitia
 */
export async function GET(request: Request) {
  try {
    // Pastikan user terautentikasi
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthenticated' }, { status: 401 });
    }

    // Ambil parameter query
    const { searchParams } = new URL(request.url);
    const tglMulai = searchParams.get('tgl_mulai');
    const tglBerakhir = searchParams.get('tgl_berakhir');
    const excludePanitiaId = searchParams.get('exclude'); // ID panitia yang dikecualikan saat edit

    // Validasi parameter yang diperlukan
    if (!tglMulai || !tglBerakhir) {
      return NextResponse.json(
        { error: 'Tanggal mulai dan berakhir harus disediakan' },
        { status: 400 }
      );
    }

    // Convert string dates to Date objects
    const startDate = new Date(tglMulai);
    const endDate = new Date(tglBerakhir);

    // Validasi format tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Format tanggal tidak valid' },
        { status: 400 }
      );
    }

    // Buat kondisi pencarian yang akan mencari semua panitia yang memiliki tugas
    // yang tanggalnya overlap dengan rentang tanggal yang disediakan
    const whereClause: any = {
      pelatihan: {
        // Kondisi overlap tanggal:
        // 1. Tanggal mulai pelatihan <= tanggal akhir yang diminta DAN
        // 2. Tanggal berakhir pelatihan >= tanggal mulai yang diminta
        AND: [
          {
            tgl_mulai: {
              lte: endDate
            }
          },
          {
            tgl_berakhir: {
              gte: startDate
            }
          }
        ]
      }
    };

    // Jika ada panitia yang dikecualikan (untuk kasus edit), tambahkan ke kondisi
    if (excludePanitiaId) {
      whereClause.id = {
        not: excludePanitiaId
      };
    }

    // Ambil semua panitia yang memiliki tugas pada rentang tanggal tersebut
    const busyPanitia = await prisma.panitia.findMany({
      where: whereClause,
      select: {
        pegawaiId: true
      }
    }) as PanitiaPegawai[];

    // Ekstrak ID pegawai yang sibuk
    const busyPegawaiIds = busyPanitia.map(p => p.pegawaiId);

    // Kembalikan daftar ID pegawai yang sibuk
    return NextResponse.json({
      busyPegawaiIds
    });
  } catch (error) {
    console.error('Error checking panitia availability:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memeriksa ketersediaan panitia' },
      { status: 500 }
    );
  }
}