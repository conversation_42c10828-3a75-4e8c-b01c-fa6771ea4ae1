# 🚀 Photo Upload Fix - Deploy ke Server

## 📋 Cara Deploy

### 1. **Deploy Langsung (Recommended)**
```cmd
cd f:\online\zoom_rutin
execute-deployment.bat
```

Script ini akan:
- Upload script deployment ke server
- <PERSON><PERSON><PERSON><PERSON> fix secara otomatis
- Mengonfigurasi nginx dan file permissions
- Restart services yang diperlukan
- Test konektivitas

### 2. **Manual Deploy (Jika diperlukan)**
```cmd
# Upload script ke server
scp execute-deployment-server.sh root@*************:/tmp/

# SSH ke server dan jalankan
ssh root@*************
chmod +x /tmp/execute-deployment-server.sh
/tmp/execute-deployment-server.sh
```

## 🔧 Apa yang Akan Diperbaiki

### **File Permissions**
- Direktori uploads: `755` (rwxr-xr-x)
- File gambar: `644` (rw-r--r--)
- Ownership: `www-data:www-data`

### **Nginx Configuration**
- Disable caching untuk `/uploads/`
- Headers Cache-Control: no-cache
- Security headers untuk uploaded files

### **Directory Structure**
```
/var/www/vhosts/kegiatan.bpmpkaltim.id/public_html/uploads/
├── absensi/
│   └── photos/
├── biodata/
│   └── photos/
└── materi/
```

## 🧪 Test Setelah Deploy

### **Cek Upload API**
```
https://kegiatan.bpmpkaltim.id/api/absensi/upload-photo
Status yang diharapkan: 405 (Method Not Allowed untuk GET)
```

### **Cek Uploads Directory**
```
https://kegiatan.bpmpkaltim.id/uploads/
Status yang diharapkan: 403 (Forbidden) atau 200 (OK)
```

### **Test Upload Gambar**
1. Buka aplikasi web
2. Upload foto via form
3. Verifikasi foto langsung dapat diakses tanpa reload nginx

## 🆘 Troubleshooting

### **Jika masih 404:**
```bash
# SSH ke server
ssh root@*************

# Jalankan monitoring
bash /tmp/monitor-photo-upload.sh

# Cek nginx status
systemctl status nginx

# Restart nginx jika perlu
systemctl restart nginx
```

### **Cek File Permissions:**
```bash
ls -la /var/www/vhosts/kegiatan.bpmpkaltim.id/public_html/uploads/
```

### **Cek Nginx Config:**
```bash
nginx -t
systemctl reload nginx
```

## 📁 Files yang Digunakan

### **Local (Windows)**
- `execute-deployment.bat` - Script utama untuk deploy dari Windows
- `execute-deployment-server.sh` - Script yang akan dijalankan di server

### **Files yang Dibuat di Server**
- `/tmp/execute-deployment-server.sh` - Script deployment
- `/tmp/monitor-photo-upload.sh` - Script monitoring
- `/tmp/photo-upload-backup-*` - Backup directory
- Nginx config updated di `/etc/nginx/sites-available/`

### **Files yang Telah Dibersihkan**
✅ Semua file .sh yang tidak diperlukan telah dihapus
✅ File deployment duplikat telah dihapus
✅ File konfigurasi yang tidak terpakai telah dihapus

## ✅ Success Indicators

Fix berhasil jika:
- ✅ Upload API return 405 untuk GET request
- ✅ Uploads directory accessible (403 atau 200)
- ✅ File permissions correct (755/644)
- ✅ Nginx config valid
- ✅ Photos immediately accessible after upload

---

**Status: Ready for Deployment**  
**Execution Time: ~5-10 minutes**  
**Risk Level: Low (automated backup & rollback)**
