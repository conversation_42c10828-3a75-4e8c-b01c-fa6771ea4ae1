// /app/api/pelatihan/[id]/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { getCurrentUser } from '../../../../lib/auth';
import { logger } from '../../../../utils/logger';
import { validateCsrfToken } from '../../../../lib/csrf';
import { handleApiError } from '../../../../utils/apiErrorHandler';
import { randomUUID } from 'crypto';

// Helper function to generate unique link
async function generateUniqueLink() {
  const uniqueId = randomUUID().substring(0, 8);
  return uniqueId;
}

// Handler para PUT request (edit pelatihan)
export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  // Declare id variable at the top level of the function
  let id: string;
  try {
    // Extraer y validar el ID al inicio de la función para que esté disponible en todo el ámbito
    const params = await props.params;
    id = params.id;
    
    // Validar formato del ID
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, message: 'ID pelatihan tidak valid' },
        { status: 400 }
      );
    }
    
    try {
      // Get current user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json(
          { success: false, message: 'Unauthorized' },
          { status: 401 }
        );
      }
      
      // Parse request body
      let data;
      try {
        data = await request.json();
        logger.info('Received data for update', { id, dataPreview: JSON.stringify(data).substring(0, 100) + '...' });
      } catch (parseError) {
        logger.error('Error parsing request body', { error: parseError });
        return NextResponse.json(
          { success: false, message: 'Format data tidak valid' },
          { status: 400 }
        );
      }
      
      // Validasi CSRF token
      const csrfToken = request.headers.get('X-CSRF-Token');
      if (csrfToken) {
        const isValidCsrf = await validateCsrfToken(csrfToken);
        if (!isValidCsrf) {
          logger.warning('Invalid CSRF token', { token: csrfToken });
          return NextResponse.json(
            { success: false, message: 'Invalid CSRF token' },
            { status: 403 }
          );        }
      }
      
      // Cek apakah pelatihan ada
      const existingPelatihan = await prisma.pelatihan.findUnique({
        where: { id },
        select: { 
          userId: true,
          peserta_kegiatan: true,
          link_absensi_internal: true,
          link_absensi_eksternal: true
        }
      });

      if (!existingPelatihan) {
        logger.warning('Pelatihan not found', { id });
        return NextResponse.json(
          { success: false, message: 'Pelatihan tidak ditemukan' },
          { status: 404 }
        );
      }
      
      // Cek kepemilikan
      if (existingPelatihan.userId !== user.id) {
        logger.warning('Access denied', { userId: user.id, ownerId: existingPelatihan.userId });
        return NextResponse.json(
          { success: false, message: 'Anda tidak memiliki akses ke pelatihan ini' }, 
          { status: 403 }
        );
      }        // Ekstrak dan validasi data pelatihan
      const { nama, tempat, tgl_mulai, tgl_berakhir, jenjang, target_peserta, peserta_kegiatan, jenjangTargets } = data;
      
      // Check if peserta_kegiatan has changed and regenerate links if needed
      const pesertaKegiatanBerubah = existingPelatihan.peserta_kegiatan !== peserta_kegiatan?.toUpperCase();
      let link_absensi_internal = existingPelatihan.link_absensi_internal;
      let link_absensi_eksternal = existingPelatihan.link_absensi_eksternal;
      
      if (pesertaKegiatanBerubah) {
        logger.info('Peserta kegiatan changed, regenerating links', { 
          oldValue: existingPelatihan.peserta_kegiatan, 
          newValue: peserta_kegiatan?.toUpperCase() 
        });
        
        // Reset links based on new selection
        if (peserta_kegiatan === 'internal' || peserta_kegiatan === 'keduanya') {
          link_absensi_internal = await generateUniqueLink();
        } else {
          link_absensi_internal = null;
        }
        
        if (peserta_kegiatan === 'eksternal' || peserta_kegiatan === 'keduanya') {
          link_absensi_eksternal = await generateUniqueLink();
        } else {
          link_absensi_eksternal = null;
        }
      }
      
      // Validasi data dasar
      if (!nama || !tempat || !tgl_mulai || !tgl_berakhir) {
        return NextResponse.json(
          { success: false, message: 'Data pelatihan tidak lengkap' },
          { status: 400 }
        );
      }
      
      // Validasi jenjangTargets
      if (!jenjangTargets || !Array.isArray(jenjangTargets) || jenjangTargets.length === 0) {
        return NextResponse.json(
          { success: false, message: 'Minimal satu jenjang target harus diisi' },
          { status: 400 }
        );
      }
      
      // Format tanggal ke ISO string yang valid
      let formattedStartDate, formattedEndDate;
      try {
        formattedStartDate = new Date(tgl_mulai);
        formattedEndDate = new Date(tgl_berakhir);
        
        // Validasi tanggal
        if (isNaN(formattedStartDate.getTime()) || isNaN(formattedEndDate.getTime())) {
          throw new Error('Format tanggal tidak valid');
        }
        
        // Validasi tanggal mulai harus sebelum tanggal berakhir
        if (formattedStartDate > formattedEndDate) {
          return NextResponse.json(
            { success: false, message: 'Tanggal mulai harus sebelum tanggal berakhir' },
            { status: 400 }
          );
        }
      } catch (dateError) {
        logger.error('Error formatting dates', { error: dateError });
        return NextResponse.json(
          { success: false, message: 'Format tanggal tidak valid' },
          { status: 400 }
        );
      }
      
      // Mulai transaksi database untuk memastikan atomic operation
      try {
        // Gunakan prisma.$transaction untuk atomic operation
        await prisma.$transaction(async (tx) => {
          // 1. Update pelatihan
          const updatedPelatihan = await tx.pelatihan.update({
            where: { id },
            data: {
              nama,
              tempat,
              tgl_mulai: formattedStartDate,
              tgl_berakhir: formattedEndDate,
              jenjang,
              target_peserta,
              peserta_kegiatan: peserta_kegiatan?.toUpperCase() as 'INTERNAL' | 'EKSTERNAL' | 'KEDUANYA',
              link_absensi_internal,
              link_absensi_eksternal,
              updatedAt: new Date()
            }
          });
          
          // 2. Hapus jenjangTargets yang ada
          await tx.pelatihan_jenjang_target.deleteMany({
            where: { pelatihanId: id }
          });
          
          // 3. Buat jenjangTargets baru
          if (jenjangTargets && jenjangTargets.length > 0) {
            await tx.pelatihan_jenjang_target.createMany({
              data: jenjangTargets.map(jt => ({
                pelatihanId: id,
                jenjang: jt.jenjang,
                target_peserta: jt.target_peserta
              }))
            });
          }
          
          return updatedPelatihan;
        });
        
        logger.info('Transaction completed successfully', { id });
        
        // Fetch complete pelatihan data with jenjangTargets for the response
        const completeData = await prisma.pelatihan.findUnique({
          where: { id },
          include: {
            jenjangTargets: {
              select: {
                id: true,
                jenjang: true,
                target_peserta: true
              }
            }
          }
        });
        
        return NextResponse.json({ 
          success: true, 
          message: 'Pelatihan berhasil diperbarui',
          data: completeData
        });
        
      } catch (error) {
        return handleApiError(error, `/api/pelatihan/${id}`, 'PUT');
      }
    } catch (error) {
      return handleApiError(error, `/api/pelatihan/${id || 'unknown'}`, 'PUT');
    }
  } catch (error) {
    return handleApiError(error, `/api/pelatihan/unknown`, 'PUT');
  }
}

// Add GET handler to fetch pelatihan with jenjangTargets
export async function GET(request: Request, props: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await props.params;
    
   
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, message: 'ID pelatihan tidak valid' },
        { status: 400 }
      );
    }
    
    // Dapatkan user saat ini para verificación
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }

    // Fetch pelatihan con jenjangTargets
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { id },
      include: {
        jenjangTargets: {
          select: {
            id: true,
            jenjang: true,
            target_peserta: true
          }
        }
      }
    });

    if (!pelatihan) {
      return NextResponse.json(
        { success: false, message: 'Pelatihan tidak ditemukan' }, 
        { status: 404 }
      );
    }

   
    const isGM = user.role === 'GM1' || user.role === 'GM2' || user.role === 'GM3' || 
                 user.role === 'GM4' || user.role === 'GM5';
                 
    if ((isGM || user.role !== 'ADMIN') && pelatihan.userId !== user.id) {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses ke pelatihan ini' }, 
        { status: 403 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      data: pelatihan
    });
  } catch (error) {
    const { id } = await props.params;
    return handleApiError(error, `/api/pelatihan/${id}`, 'GET');
  }
}

// Handler para DELETE request (hapus pelatihan)
export async function DELETE(request: Request, props: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await props.params;
    
    // Validar formato del ID
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, message: 'ID pelatihan tidak valid' },
        { status: 400 }
      );
    }
    
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Cek apakah pelatihan ada
    const existingPelatihan = await prisma.pelatihan.findUnique({
      where: { id },
      select: { userId: true }
    });
    
    if (!existingPelatihan) {
      return NextResponse.json(
        { success: false, message: 'Pelatihan tidak ditemukan' },
        { status: 404 }
      );
    }
    
    // Cek kepemilikan
    if (existingPelatihan.userId !== user.id) {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses ke pelatihan ini' },
        { status: 403 }
      );
    }
    
    // Hapus pelatihan dan data terkait
    await prisma.$transaction(async (tx) => {
      // 1. Hapus jenjangTargets
      await tx.pelatihan_jenjang_target.deleteMany({
        where: { pelatihanId: id }
      });
      
      // 2. Hapus pelatihan
      await tx.pelatihan.delete({
        where: { id }
      });
    });
    
    return NextResponse.json({
      success: true,
      message: 'Pelatihan berhasil dihapus'
    });
  } catch (error) {
    const { id } = await props.params;
    return handleApiError(error, `/api/pelatihan/${id}`, 'DELETE');
  }
}
