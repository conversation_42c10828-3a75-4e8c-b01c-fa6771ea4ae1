'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { submitAbsensi } from '../../action';
import SignatureCanvas from '../../../../../components/SignatureCanvas';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';

// Dynamic import untuk Map component karena Leaflet membutuhkan window object
const MapComponent = dynamic(
  () => import('../../../../../components/MapPicker'),
  { 
    ssr: false,
    loading: () => <div className="h-[300px] bg-gray-100 animate-pulse flex items-center justify-center">Memuat peta...</div>
  }
);

// Import shadcn/ui components
import { Input } from '../../../../../components/ui/input';
import { Label } from '../../../../../components/ui/label';
import { Button } from '../../../../../components/ui/button';
import { formatIndonesiaDate } from '../../../../../utils/dateUtils';

// Definisikan interface untuk data pelatihan
interface PelatihanData {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: string | Date;
  tgl_berakhir: string | Date;
  link_absensi_eksternal: string;
}

export default function AbsensiEksternalPage() {
  const params = useParams<{ link: string }>();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [pelatihan, setPelatihan] = useState<PelatihanData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    nama: '',
    nip_nik: '',
    jabatan: '',
    unit_kerja: '',
    no_hp: '',
    tanda_tangan: '',
    latitude: '',
    longitude: '',
    alamat: ''
  });
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    async function verifyLink() {
      try {
        const response = await fetch(`/api/verify-absensi-eksternal?link=${params.link}`);
        const data = await response.json();

        if (data.valid) {
          setPelatihan(data.pelatihan);
          setIsLoading(false);
        } else {
          setError(data.message);        setIsLoading(false);
        }
      } catch (_err) {
        setError('Terjadi kesalahan saat memverifikasi link');
        setIsLoading(false);
      }
    }

    if (params.link) {
      verifyLink();
    }
  }, [params.link]);

  const validatePhoneNumber = (phone: string): boolean => {
    const pattern = /^(\+62|62|0)8[1-9][0-9]{6,10}$/;
    return pattern.test(phone);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    // Real-time validation
    if (name === 'no_hp' && value) {
      if (!validatePhoneNumber(value)) {
        setFieldErrors(prev => ({ 
          ...prev, 
          [name]: 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx'
        }));
      } else {
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
  };

  const handleSignatureChange = (dataUrl: string) => {
    setFormData((prev) => ({ ...prev, tanda_tangan: dataUrl }));
    
    // Clear signature error when signature is provided
    if (dataUrl) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.tanda_tangan;
        return newErrors;
      });
    }
  };

  const handleLocationSelect = (lat: number, lng: number, address: string) => {
    setFormData((prev) => ({
      ...prev,
      latitude: lat.toString(),
      longitude: lng.toString(),
      alamat: address
    }));
    
    // Clear location error when location is provided
    if (lat && lng) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.location;
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!formData.nama) {
      errors.nama = 'Nama lengkap harus diisi';
    }
    
    if (!formData.unit_kerja) {
      errors.unit_kerja = 'Unit kerja/instansi harus diisi';
    }
    
    if (!formData.no_hp) {
      errors.no_hp = 'Nomor HP harus diisi';
    } else if (!validatePhoneNumber(formData.no_hp)) {
      errors.no_hp = 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx';
    }
    
    if (!formData.tanda_tangan) {
      errors.tanda_tangan = 'Tanda tangan harus diisi';
    }
    
    if (!formData.latitude || !formData.longitude) {
      errors.location = 'Lokasi harus dipilih';
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      if (!pelatihan) return;
      
      const submitData = {
        ...formData,
        pelatihanId: pelatihan.id,
        is_internal: false // Flag untuk peserta eksternal
      };
      
      const result = await submitAbsensi(submitData);
        if (result.success) {
        toast.success(result.message);
        router.push(`/public-pages/absensi/${params.link}/success`);      } else {
        toast.error(result.message);
      }
    } catch (_error) {
      toast.error('Terjadi kesalahan saat menyimpan data');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-4 border-b-2 rounded-full animate-spin border-emerald-600"></div>
          <p className="text-gray-600">Memuat halaman absensi...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-pink-100">
        <div className="max-w-md p-6 mx-auto text-center">
          <div className="mb-4 text-6xl text-red-500">⚠️</div>
          <h1 className="mb-2 text-2xl font-bold text-red-800">Link Tidak Valid</h1>
          <p className="mb-4 text-red-600">{error}</p>
          {/* <Button 
            onClick={() => router.push('/')}
            className="bg-red-600 hover:bg-red-700"
          >
            Kembali ke Beranda
          </Button> */}
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen px-3 py-4 sm:px-4 sm:py-8 bg-gradient-to-br from-green-50 to-emerald-100">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="p-4 mb-4 bg-white rounded-lg shadow-lg sm:p-6 sm:mb-6">
          <div className="text-center">
            <h1 className="mb-2 text-xl font-bold sm:text-2xl lg:text-3xl text-emerald-800">
              Absensi Kegiatan
            </h1>
            <div className="p-3 mt-3 rounded-lg sm:p-4 sm:mt-4 bg-emerald-50">
              <h2 className="mb-2 text-base font-semibold sm:text-lg lg:text-xl text-emerald-700">
                {pelatihan?.nama}
              </h2>
              <div className="space-y-1 text-xs sm:text-sm text-emerald-600">
                <p><span className="font-medium">Tempat:</span> {pelatihan?.tempat}</p>
                <p><span className="font-medium">Tanggal:</span> {formatIndonesiaDate(pelatihan?.tgl_mulai)} - {formatIndonesiaDate(pelatihan?.tgl_berakhir)}</p>
                
              </div>
            </div>
          </div>
        </div>        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 bg-white rounded-lg shadow-lg sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
            {/* Nama Lengkap */}
            <div className="space-y-2">
              <Label htmlFor="nama" className="text-sm font-medium text-gray-700">
                Nama Lengkap *
              </Label>
              <Input
                id="nama"
                name="nama"
                value={formData.nama}
                onChange={handleChange}
                placeholder="Masukkan nama lengkap"
                className={`h-10 sm:h-auto ${fieldErrors.nama ? 'border-red-500' : ''}`}
              />
              {fieldErrors.nama && (
                <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.nama}</p>
              )}
            </div>

            {/* NIP/NIK */}
            <div className="space-y-2">
              <Label htmlFor="nip_nik" className="text-sm font-medium text-gray-700">
                NIP/NIK
              </Label>
              <Input
                id="nip_nik"
                name="nip_nik"
                value={formData.nip_nik}
                onChange={handleChange}
                placeholder="Masukkan NIP/NIK (opsional)"
                className="h-10 sm:h-auto"
              />
            </div>

            {/* Jabatan */}
            <div className="space-y-2">
              <Label htmlFor="jabatan" className="text-sm font-medium text-gray-700">
                Jabatan
              </Label>
              <Input
                id="jabatan"
                name="jabatan"
                value={formData.jabatan}
                onChange={handleChange}
                placeholder="Masukkan jabatan (opsional)"
                className="h-10 sm:h-auto"
              />
            </div>

            {/* Unit Kerja/Instansi */}
            <div className="space-y-2">
              <Label htmlFor="unit_kerja" className="text-sm font-medium text-gray-700">
                Unit Kerja/Instansi *
              </Label>
              <Input
                id="unit_kerja"
                name="unit_kerja"
                value={formData.unit_kerja}
                onChange={handleChange}
                placeholder="Masukkan unit kerja/instansi"
                className={`h-10 sm:h-auto ${fieldErrors.unit_kerja ? 'border-red-500' : ''}`}
              />
              {fieldErrors.unit_kerja && (
                <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.unit_kerja}</p>
              )}
            </div>

            {/* Nomor HP */}
            <div className="space-y-2 sm:col-span-2">
              <Label htmlFor="no_hp" className="text-sm font-medium text-gray-700">
                Nomor HP *
              </Label>
              <Input
                id="no_hp"
                name="no_hp"
                value={formData.no_hp}
                onChange={handleChange}
                placeholder="Contoh: 08123456789"
                className={`h-10 sm:h-auto ${fieldErrors.no_hp ? 'border-red-500' : ''}`}
              />
              {fieldErrors.no_hp && (
                <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.no_hp}</p>
              )}
            </div>
          </div>

          {/* Tanda Tangan */}
          <div className="mt-4 space-y-2 sm:mt-6">
            <Label className="text-sm font-medium text-gray-700">
              Tanda Tangan *
            </Label>
            <div className="p-2 border rounded-lg sm:p-4">
              <SignatureCanvas onSignatureChange={handleSignatureChange} />
            </div>
            {fieldErrors.tanda_tangan && (
              <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.tanda_tangan}</p>
            )}
          </div>

          {/* Lokasi */}
          <div className="mt-4 space-y-2 sm:mt-6">
            <Label className="text-sm font-medium text-gray-700">
              Pilih Lokasi Kehadiran *
            </Label>
            <div className="overflow-hidden border rounded-lg">
              <MapComponent onLocationSelect={handleLocationSelect} />
            </div>
            {fieldErrors.location && (
              <p className="text-xs text-red-500 sm:text-sm">{fieldErrors.location}</p>
            )}
            {formData.alamat && (
              <div className="p-3 mt-2 border border-green-200 rounded-lg bg-green-50">
                <p className="text-xs text-green-700 sm:text-sm">
                  <span className="font-medium">Lokasi terpilih:</span> {formData.alamat}
                </p>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-center mt-6 sm:mt-8">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full px-6 py-2 text-base font-medium text-white sm:px-8 sm:py-3 sm:text-lg bg-emerald-600 hover:bg-emerald-700 disabled:opacity-50 sm:w-auto"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-b-2 border-white rounded-full animate-spin"></div>
                  <span>Menyimpan...</span>
                </div>
              ) : (
                'Kirim Absensi'
              )}
            </Button>
          </div>        </form>
      </div>
    </div>
  );
}
