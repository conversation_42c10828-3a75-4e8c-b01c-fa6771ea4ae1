'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';

interface ImageUploaderProps {
  onImageUpload: (file: File | null, preview: string | null) => void;
  initialImage?: string | null;
  maxSize?: number; // in MB
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUpload,
  initialImage = null,
  maxSize = 5 // default max size is 5MB
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialImage);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setError(null);
    
    if (!file) {
      setPreviewUrl(null);
      onImageUpload(null, null);
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('File harus berupa gambar (jpg, png, gif, dll)');
      return;
    }

    // Check file size (convert maxSize from MB to bytes)
    if (file.size > maxSize * 1024 * 1024) {
      setError(`Ukuran file tidak boleh lebih dari ${maxSize}MB`);
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onloadstart = () => setIsLoading(true);
    reader.onload = () => {
      setIsLoading(false);
      const result = reader.result as string;
      setPreviewUrl(result);
      onImageUpload(file, result);
      
      // Show success message
      setError(null);
    };
    reader.onerror = () => {
      setIsLoading(false);
      setError('Gagal membaca file');
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    onImageUpload(null, null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-2">
      <div 
        className="flex flex-col items-center p-4 transition-colors border-2 border-gray-300 border-dashed rounded-md cursor-pointer bg-gray-50 hover:bg-gray-100"
        onClick={!previewUrl ? handleUploadClick : undefined}
      >
        {isLoading ? (
          <div className="flex flex-col items-center justify-center w-full h-40">
            <div className="w-8 h-8 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
            <p className="mt-2 text-sm text-gray-500">Memuat gambar...</p>
          </div>
        ) : previewUrl ? (
          <div className="relative">
            <div className="relative w-40 h-40 mx-auto overflow-hidden rounded-md">
              <Image 
                src={previewUrl} 
                alt="Preview" 
                layout="fill" 
                objectFit="cover" 
                className="transition-opacity duration-300"
              />
            </div>
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute top-0 right-0 flex items-center justify-center w-6 h-6 -mt-2 -mr-2 text-white transition-colors bg-red-500 rounded-full hover:bg-red-600"
              aria-label="Hapus gambar"
            >
              ×
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center w-full h-40 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="mt-2 text-sm text-gray-500">Klik untuk upload foto</p>
            <p className="text-xs text-gray-400">Format: JPG, PNG, GIF (Maks. {maxSize}MB)</p>
          </div>
        )}
      </div>
      
      {/* Hidden file input - no longer absolutely positioned */}
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
        aria-label="Upload gambar"
      />
      
      {error && <p className="text-sm text-red-600">{error}</p>}
      
      {previewUrl && (
        <div className="flex justify-center">
          <button
            type="button"
            onClick={handleUploadClick}
            className="px-3 py-1 text-xs text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
          >
            Ganti Foto
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
