import { ReactNode } from 'react';
import { getCurrentUser } from '../lib/auth';
import { unauthorized } from 'next/navigation';

interface AuthCheckProps {
  children: ReactNode;
  requiredRole?: string;
}

export default async function AuthCheck({ children, requiredRole }: AuthCheckProps) {
  const user = await getCurrentUser();
  
  if (!user) {
    unauthorized();
  }
  
  if (requiredRole && user.role !== requiredRole) {
    return (
      <div className="p-4 text-red-700 bg-red-100 rounded">
        You dont have permission to access this page.
      </div>
    );
  }
  
  return <>{children}</>;
}