#!/bin/bash

# Photo Upload Fix Script for VPS (Ubuntu 22.04)
# Run this script on your VPS after uploading the deployment files

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration - UPDATE THESE PATHS
DEPLOYMENT_PATH="/home/<USER>/deployment"  # Update this path
DOMAIN="kegiatan.bpmpkaltim.id"
APP_PORT="3000"

echo -e "${BLUE}"
echo "============================================"
echo "   Photo Upload Fix for VPS Deployment"
echo "============================================"
echo -e "${NC}"

# Check if we're in the right directory
if [ ! -f "server.js" ]; then
    log_error "server.js not found. Please run this script from your deployment directory."
    exit 1
fi

# Fix 1: Ensure upload directories exist with correct permissions
log_info "Creating and fixing upload directories..."
mkdir -p public/uploads/absensi/photos
mkdir -p public/uploads/biodata/photos  
mkdir -p public/uploads/materi

# Set proper ownership and permissions
sudo chown -R www-data:www-data public/uploads/
sudo chmod -R 755 public/uploads/

# Make directories writable by web server
find public/uploads/ -type d -exec chmod 755 {} \;
find public/uploads/ -type f -exec chmod 644 {} \;

log_success "Upload directories configured"

# Fix 2: Update nginx configuration
log_info "Updating nginx configuration..."

# Backup existing config
sudo cp /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-available/$DOMAIN.backup.$(date +%Y%m%d_%H%M%S)

# Create new nginx config
sudo tee /etc/nginx/sites-available/$DOMAIN > /dev/null << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL configuration (update paths as needed)
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Upload limit
    client_max_body_size 50M;
    
    # Root directory for static files - CRITICAL FIX
    root $DEPLOYMENT_PATH/public;
    
    # Serve uploaded files directly - MAIN FIX
    location /uploads/ {
        try_files \$uri \$uri/ =404;
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Serve Next.js static files
    location /_next/static/ {
        root $DEPLOYMENT_PATH;
        try_files \$uri \$uri/ =404;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
    
    # Serve other static assets
    location ~* \.(ico|css|js|gif|jpe?g|png|svg|woff|woff2|ttf|eot)$ {
        try_files \$uri \$uri/ =404;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
    
    # Proxy all other requests to Next.js
    location / {
        proxy_pass http://localhost:$APP_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Test nginx configuration
log_info "Testing nginx configuration..."
if sudo nginx -t; then
    log_success "Nginx configuration is valid"
    
    # Reload nginx
    sudo systemctl reload nginx
    log_success "Nginx reloaded"
else
    log_error "Nginx configuration test failed"
    log_warning "Restoring backup configuration..."
    sudo cp /etc/nginx/sites-available/$DOMAIN.backup.* /etc/nginx/sites-available/$DOMAIN
    exit 1
fi

# Fix 3: Restart application
log_info "Restarting Next.js application..."
if command -v pm2 &> /dev/null; then
    pm2 restart server.js 2>/dev/null || pm2 start server.js --name nextjs-app
    log_success "Application restarted with PM2"
else
    log_warning "PM2 not found. Please manually restart your application"
fi

# Fix 4: Verification
log_info "Running verification tests..."

# Test if directories exist
if [ -d "public/uploads/absensi/photos" ]; then
    log_success "Absensi photos directory exists"
else
    log_error "Absensi photos directory missing"
fi

# Test file permissions
if [ -w "public/uploads/absensi/photos" ]; then
    log_success "Upload directory is writable"
else
    log_error "Upload directory is not writable"
fi

# Test nginx syntax
if sudo nginx -t &>/dev/null; then
    log_success "Nginx configuration is valid"
else
    log_error "Nginx configuration has errors"
fi

echo ""
log_success "Photo upload fix completed!"
echo ""
echo -e "${YELLOW}Important notes:${NC}"
echo "1. Upload directories: $DEPLOYMENT_PATH/public/uploads/"
echo "2. URLs will access: https://$DOMAIN/uploads/"
echo "3. Nginx root set to: $DEPLOYMENT_PATH/public"
echo ""
echo -e "${YELLOW}Test the fix:${NC}"
echo "1. Go to absensi form and upload a photo"
echo "2. Check file is created in: public/uploads/absensi/photos/[pelatihan-id]/"
echo "3. Verify photo displays in detail view"
echo ""
echo -e "${YELLOW}If issues persist:${NC}"
echo "- Check nginx error logs: sudo tail -f /var/log/nginx/error.log"
echo "- Check application logs: pm2 logs"
echo "- Verify file permissions: ls -la public/uploads/"
