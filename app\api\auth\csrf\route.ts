import { NextResponse } from 'next/server';
import { generateCsrfToken } from '../../../../lib/csrf';

/**
 * API endpoint to generate and retrieve a CSRF token
 */
export async function GET() {
  try {
    // Generate a new CSRF token
    const csrfToken = await generateCsrfToken();
    
    // Return the token
    return NextResponse.json({ csrfToken });
  } catch (error) {
    console.error('Error generating CSRF token:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}