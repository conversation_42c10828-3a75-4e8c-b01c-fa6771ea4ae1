// File: /utils/helpers.ts
import { randomBytes } from 'crypto';
import { formatIndonesiaDate, formatIndonesiaDateTime } from './dateUtils';

// Generate link unik
export function generateUniqueLink(length = 20) {
  return randomBytes(length).toString('hex');
}

// Format tanggal ke string lokal Indonesia dengan penyesuaian zona waktu
export function formatDate(date: Date | string | null | undefined) {
  return formatIndonesiaDate(date);
}

// Format tanggal dan waktu ke string lokal Indonesia
export function formatDateTime(date: Date | string | null | undefined) {
  return formatIndonesiaDateTime(date);
}

/**
 * Checks if the user has permission to write/modify a resource
 * @param userId User ID attempting the operation
 * @param resourceOwnerId Owner ID of the resource
 * @param isAdmin Whether the user has admin privileges
 * @returns Boolean indicating if the user has permission
 */
export function hasWritePermission(
  userId: string | null | undefined,
  resourceOwnerId: string,
  isAdmin: boolean = false
): boolean {
  if (!userId) return false;
  return isAdmin || userId === resourceOwnerId;
}

/**
 * Get user access scope based on role
 * @param user User object with role
 * @returns Object containing where clause for database queries and isGM flag
 */
export function getUserAccessScope(user: { id: string; role: string }) {
  // Default where clause (no filter)
  let whereClause = {};
  
  // Check if user is a GM (role starts with 'GM')
  const isGM = user.role.startsWith('GM');
  
  // If user is not an admin, filter by userId
  if (user.role !== 'ADMIN') {
    whereClause = {
      userId: user.id
    };
  }
  
  return { isGM, whereClause };
}
