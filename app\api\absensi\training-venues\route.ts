import { NextRequest, NextResponse } from 'next/server';
import { getDefaultTrainingVenues } from '../../../../lib/geofencing';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const pelatihanId = searchParams.get('pelatihanId');

    // Get training venues from database, fallback to default venues
    let venues = getDefaultTrainingVenues();
    
    try {
      // If specific pelatihan ID provided, get its venue
      if (pelatihanId) {
        const pelatihan = await prisma.pelatihan.findUnique({
          where: { id: pelatihanId },
          include: { venue: true }
        });

        if (pelatihan?.venue) {
          venues = [{
            id: pelatihan.venue.id,
            name: pelatihan.venue.nama,
            latitude: pelatihan.venue.latitude,
            longitude: pelatihan.venue.longitude,
            radius: pelatihan.venue.radius,
            address: pelatihan.venue.alamat
          }];
        }
      } else {
        // Get all active venues
        const dbVenues = await prisma.training_venue.findMany({
          where: { is_active: true }
        });
        
        if (dbVenues.length > 0) {
          venues = dbVenues.map(venue => ({
            id: venue.id,
            name: venue.nama,
            latitude: venue.latitude,
            longitude: venue.longitude,
            radius: venue.radius,
            address: venue.alamat
          }));
        }
      }
    } catch (dbError) {
      console.warn('Failed to fetch training venues from database, using defaults:', dbError);
    }

    return NextResponse.json({
      success: true,
      venues
    });

  } catch (error) {
    console.error('Get Training Venues Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data venue' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
