/**
 * Logger khusus untuk middleware yang tidak menggunakan fetch API
 * File terpisah untuk menghindari circular dependencies
 */

type LogLevel = 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';

interface LogContext {
  path?: string;
  method?: string;
  userId?: string;
  [key: string]: any;
}

/**
 * Format log message untuk output konsisten
 */
function formatLogMessage(level: LogLevel, message: string, context?: LogContext): string {
  const timestamp = new Date().toISOString();
  const path = context?.path || 'unknown';
  const method = context?.method || '-';
  const userId = context?.userId || '-';
  
  return `[${timestamp}] [${level}] [${method} ${path}] [User: ${userId}] ${message}`;
}

/**
 * Logger untuk middleware yang hanya menggunakan console
 */
export const middlewareLogger = {
  info: (message: string, context: LogContext = {}) => {
    console.info(formatLogMessage('INFO', message, context));
  },
    
  warning: (message: string, context: LogContext = {}) => {
    console.warn(formatLogMessage('WARNING', message, context));
  },
  
  warn: (message: string, context: LogContext = {}) => {
    console.warn(formatLogMessage('WARNING', message, context));
  },
    
  error: (message: string, error?: Error | unknown, context: LogContext = {}) => {
    const errorMsg = error instanceof Error 
      ? `${message}: ${error.message}` 
      : message;
    console.error(formatLogMessage('ERROR', errorMsg, context));
    
    // Log stack trace jika tersedia
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
  },
    
  critical: (message: string, error?: Error | unknown, context: LogContext = {}) => {
    const errorMsg = error instanceof Error 
      ? `${message}: ${error.message}` 
      : message;
    console.error(formatLogMessage('CRITICAL', errorMsg, context));
    
    // Log stack trace jika tersedia
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
  },
};

export default middlewareLogger;
