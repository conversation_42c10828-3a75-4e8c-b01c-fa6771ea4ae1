# 🖱️ PANDUAN WINSCP - UPLOAD MUDAH DENGAN GUI

## 📥 Download dan Install WinSCP

1. **Download**: https://winscp.net/eng/download.php
2. **Install**: <PERSON><PERSON><PERSON> installer, ikuti wizard
3. **Buka WinSCP**

## 🔧 Setup Koneksi ke VPS

### Langkah 1: New Session
1. Klik **"New Site"** di WinSCP
2. **File protocol**: SFTP
3. **Host name**: `kegiatan.bpmpkaltim.id`
4. **Port number**: `22`
5. **User name**: `[username VPS Anda]`
6. **Password**: `[password VPS Anda]`

### Langkah 2: Advanced Settings (Opsional)
- Klik **"Advanced"**
- **Environment > Directories**
- **Remote directory**: `/home/<USER>/`
- **Local directory**: `f:\online\zoom_rutin`

### Langkah 3: Save & Connect
1. <PERSON><PERSON> **"Save"** → <PERSON><PERSON> nama: "VPS Kegiatan"
2. K<PERSON> **"Login"**
3. Jika muncul warning certificate → Klik **"Yes"**

## 📁 Upload Files

### Interface WinSCP
```
┌─────────────────┬─────────────────┐
│   LOCAL (PC)    │   REMOTE (VPS)  │
│                 │                 │
│ f:\online\      │ /home/<USER>/     │
│ zoom_rutin\     │                 │
│                 │                 │
│ ← Drag files here from left to right → │
└─────────────────┴─────────────────┘
```

### File yang Harus Diupload

#### 1. Upload deployment.tar.gz
- **Lokasi lokal**: Navigate ke `.next` folder
- **File**: `deployment.tar.gz` (34.9 MB)
- **Cara**: Drag dari panel kiri ke panel kanan
- **Target**: `/home/<USER>/`

#### 2. Upload fix-photo-upload-vps.sh
- **Lokasi lokal**: Root folder `zoom_rutin`
- **File**: `fix-photo-upload-vps.sh`
- **Cara**: Drag dari panel kiri ke panel kanan
- **Target**: `/home/<USER>/`

#### 3. Upload nginx-config.txt
- **Lokasi lokal**: Root folder `zoom_rutin`
- **File**: `nginx-config.txt`
- **Cara**: Drag dari panel kiri ke panel kanan
- **Target**: `/home/<USER>/`

## ✅ Verifikasi Upload

### Cek Panel Kanan (Remote)
Pastikan file berikut ada di `/home/<USER>/`:
- ✅ `deployment.tar.gz` (34.9 MB)
- ✅ `fix-photo-upload-vps.sh` (6.8 KB)
- ✅ `nginx-config.txt` (2.1 KB)

### Cek File Size
Klik kanan pada file → **Properties** → Pastikan ukuran file sama dengan lokal

## 🔍 Tips WinSCP

### 1. Monitor Progress
- **Upload progress** ditampilkan di bottom panel
- **Transfer queue** menampilkan antrian upload
- **Speed** dan **ETA** ditampilkan real-time

### 2. Resume Upload
- Jika upload terputus, WinSCP akan menawarkan **"Resume"**
- Pilih **"Resume"** untuk melanjutkan dari posisi terakhir

### 3. Verify Transfer
- **Options** → **Preferences** → **Transfer**
- Enable **"Calculate checksums"** untuk verifikasi otomatis

### 4. Keyboard Shortcuts
- **F5**: Refresh panel
- **F6**: Move file
- **F8**: Delete file
- **Ctrl+U**: Upload selected files

## 🚨 Troubleshooting WinSCP

### Error: "Connection refused"
```
✅ Solusi:
1. Cek hostname: kegiatan.bpmpkaltim.id
2. Cek port: 22
3. Cek username dan password
4. Test ping: ping kegiatan.bpmpkaltim.id
```

### Error: "Permission denied"
```
✅ Solusi:
1. Pastikan username dan password benar
2. Cek apakah user memiliki akses SSH
3. Pastikan direktori target writable
```

### Upload Slow/Stuck
```
✅ Solusi:
1. Cancel upload → Retry
2. Change transfer mode: Binary
3. Reduce connection count
4. Use "Conservative" transfer mode
```

## 🎯 Setelah Upload Selesai

### Buka Terminal di WinSCP
1. Tekan **Ctrl+P** atau **Commands → Open Terminal**
2. Jalankan commands berikut:

```bash
# Verifikasi file
ls -la /home/<USER>/

# Create deployment directory
mkdir -p /home/<USER>/deployment
cd /home/<USER>/deployment

# Extract deployment
tar -xzf ../deployment.tar.gz

# Make script executable
chmod +x /home/<USER>/fix-photo-upload-vps.sh

# Run fix script
sudo /home/<USER>/fix-photo-upload-vps.sh
```

## 📸 Screenshot Guide

### Tampilan WinSCP yang Benar
```
┌─ WinSCP ────────────────────────────────┐
│ Session: VPS Kegiatan                   │
├─────────────────┬───────────────────────┤
│ LOCAL           │ REMOTE                │
│ f:\online\      │ /home/<USER>/           │
│ zoom_rutin\     │                       │
│ ├─ .next\       │ ├─ deployment.tar.gz  │
│ │  └─ deploy... │ ├─ fix-photo-uplo...  │
│ ├─ fix-photo... │ └─ nginx-config.txt   │
│ └─ nginx-con... │                       │
└─────────────────┴───────────────────────┘
```

---
**Keunggulan WinSCP**:
- ✅ User-friendly GUI
- ✅ Drag & drop support
- ✅ Auto-resume upload
- ✅ Built-in terminal
- ✅ Progress monitoring
- ✅ Checksum verification
