// app/api/upload-local/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir, chmod } from 'fs/promises';
import { join } from 'path';
import { randomUUID } from 'crypto';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'uploads';

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File tidak ditemukan' },
        { status: 400 }
      );
    }

    // Validasi tipe file
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: 'Tipe file tidak didukung. Gunakan JPEG, PNG, atau WebP' },
        { status: 400 }
      );
    }

    // Validasi ukuran file (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, message: 'Ukuran file maksimal 5MB' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileName = `${randomUUID()}.webp`;
    
    // Create directory structure
    const baseDir = join(process.cwd(), 'public', 'uploads');
    const folderDir = join(baseDir, folder);
    
    try {
      await mkdir(baseDir, { recursive: true });
      await mkdir(folderDir, { recursive: true });
    } catch (error) {
      console.error('Error creating directories:', error);
    }

    // File path
    const filePath = join(folderDir, fileName);
    
    // Convert file to buffer and optimize
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Optimize image using sharp
    const optimizedImageBuffer = await sharp(buffer)
      .resize({
        width: 800,
        height: 800,
        fit: 'inside',
        withoutEnlargement: true
      })
      .webp({ quality: 85 })
      .toBuffer();

    // Write file
    await writeFile(filePath, optimizedImageBuffer);
    await chmod(filePath, 0o644);

    // Generate URLs
    const relativePath = `/uploads/${folder}/${fileName}`;
    const fullUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}${relativePath}`;

    return NextResponse.json({
      success: true,
      data: {
        public_id: `${folder}/${fileName.replace('.webp', '')}`,
        secure_url: fullUrl,
        url: fullUrl,
        format: 'webp',
        width: 800,
        height: 800,
        bytes: optimizedImageBuffer.length,
      },
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan saat mengupload file' },
      { status: 500 }
    );
  }
}
