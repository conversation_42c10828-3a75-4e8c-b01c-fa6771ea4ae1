import { z } from 'zod';

// Define the question types based on your schema
const questionTypes = [
  'SHORT_TEXT',
  'LONG_TEXT',
  'SINGLE_CHOICE',
  'MULTIPLE_CHOICE',
  'DROPDOWN',
  'DATE',
  'TIME',
  'FILE_UPLOAD',
  'SCALE',
  'EMAIL',
  'PHONE',
  'NUMBER'
] as const;

// Form Schema
export const FormSchema = z.object({
  title: z.string().min(1, "Judul form wajib diisi"),
  description: z.string().optional(),
  pelatihanId: z.string().min(1, "Pelatihan wajib dipilih"),
});

export type FormData = z.infer<typeof FormSchema>;

// Question Schema
export const QuestionSchema = z.object({
  title: z.string().min(1, "Judul pertanyaan wajib diisi"),
  description: z.string().optional(),
  type: z.enum(questionTypes),
  isRequired: z.boolean().default(false),
  order: z.number().int().min(0),
  options: z.array(z.object({
    value: z.string(),
    text: z.string()
  })).optional(),
  validationRules: z.object({
    minLength: z.number().optional(),
    maxLength: z.number().optional(),
    minValue: z.number().optional(),
    maxValue: z.number().optional(),
    pattern: z.string().optional(),
  }).optional(),
});

export type QuestionData = z.infer<typeof QuestionSchema>;

// Form Response Schema
export const FormResponseSchema = z.object({
  formId: z.string().min(1),
  respondentName: z.string().optional(),
  respondentEmail: z.string().email().optional(),
  answers: z.record(z.string(), z.any())
});

export type FormResponseData = z.infer<typeof FormResponseSchema>;











