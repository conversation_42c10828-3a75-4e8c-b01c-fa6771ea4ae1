import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

// GET /api/tugas-lokasi/[id] - Mendapatkan detail lokasi tugas
export async function GET(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();
    const { id } = await params;

    // Hanya admin yang boleh mengakses detail lokasi tugas
    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const location = await prisma.t_tugas.findUnique({
      where: { id },
    });

    if (!location) {
      return NextResponse.json({ error: 'Lokasi tidak ditemukan' }, { status: 404 });
    }

    return NextResponse.json(location);
  } catch (error) {
    console.error('Error getting location:', error);
    return NextResponse.json({ error: 'Gagal mendapatkan data lokasi' }, { status: 500 });
  }
}

// PUT /api/tugas-lokasi/[id] - Memperbarui lokasi tugas
export async function PUT(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();
    const { id } = await params;
    const { kab_kota } = await request.json();

    // Hanya admin yang boleh memperbarui lokasi tugas
    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Validasi input
    if (!kab_kota) {
      return NextResponse.json({ error: 'Nama kabupaten/kota harus diisi' }, { status: 400 });
    }

    // Cek apakah lokasi dengan nama yang sama sudah ada (selain lokasi yang sedang diupdate)
    const existingLocation = await prisma.t_tugas.findFirst({
      where: {
        kab_kota: {
          contains: kab_kota.toLowerCase(),
        },
        NOT: {
          id: id,
        },
      },
    });

    if (existingLocation) {
      return NextResponse.json(
        { error: 'Lokasi dengan nama yang sama sudah terdaftar' },
        { status: 409 }
      );
    }

    // Cek apakah lokasi yang diupdate ada
    const location = await prisma.t_tugas.findUnique({
      where: { id },
    });

    if (!location) {
      return NextResponse.json({ error: 'Lokasi tidak ditemukan' }, { status: 404 });
    }

    // Update lokasi
    const updatedLocation = await prisma.t_tugas.update({
      where: { id },
      data: {
        kab_kota,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedLocation);
  } catch (error) {
    console.error('Error updating location:', error);
    return NextResponse.json({ error: 'Gagal memperbarui data lokasi' }, { status: 500 });
  }
}

// DELETE /api/tugas-lokasi/[id] - Menghapus lokasi tugas
export async function DELETE(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const currentUser = await getCurrentUser();
    const { id } = await params;

    // Hanya admin yang boleh menghapus lokasi tugas
    if (!currentUser || currentUser.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Cek apakah lokasi digunakan oleh panitia
    const relatedPanitia = await prisma.panitia.findFirst({
      where: { lokasiId: id },
    });

    if (relatedPanitia) {
      return NextResponse.json(
        { error: 'Lokasi ini sedang digunakan oleh panitia dan tidak dapat dihapus' },
        { status: 400 }
      );
    }

    // Cek apakah lokasi yang dihapus ada
    const location = await prisma.t_tugas.findUnique({
      where: { id },
    });

    if (!location) {
      return NextResponse.json({ error: 'Lokasi tidak ditemukan' }, { status: 404 });
    }

    // Hapus lokasi
    await prisma.t_tugas.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Lokasi berhasil dihapus' });
  } catch (error) {
    console.error('Error deleting location:', error);
    return NextResponse.json({ error: 'Gagal menghapus data lokasi' }, { status: 500 });
  }
}
