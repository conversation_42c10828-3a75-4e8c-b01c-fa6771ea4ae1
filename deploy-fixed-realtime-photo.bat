@echo off
cls
color 0B
echo =========================================================
echo    DEPLOY CLOUDPANEL REALTIME PHOTO FIX (FIXED VERSION)
echo =========================================================
echo.

:: Set variables (ganti dengan detail VPS Anda)
set VPS_IP=YOUR_VPS_IP
set VPS_USER=root
set LOCAL_SCRIPT=cloudpanel-realtime-photo-fix-fixed.sh

:: Check if script exists
if not exist "%LOCAL_SCRIPT%" (
    echo [ERROR] Script %LOCAL_SCRIPT% not found!
    echo Make sure the script is in the same directory as this batch file.
    pause
    exit /b 1
)

echo [INFO] CloudPanel Real-time Photo Fix Deployer (Fixed Version)
echo [INFO] Target VPS: %VPS_IP%
echo [INFO] Script: %LOCAL_SCRIPT%
echo.
echo [FIXED] Syntax error sudah diperbaiki
echo [FIXED] Quote matching sudah benar
echo [FIXED] JPEG creation menggunakan echo -en
echo.

set /p CONFIRM="Continue with deployment? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo =========================================================
echo                    DEPLOYMENT STEPS
echo =========================================================
echo.

:: Step 1: Upload script
echo [STEP 1] Uploading fixed script to VPS...
scp -o StrictHostKeyChecking=no "%LOCAL_SCRIPT%" %VPS_USER%@%VPS_IP%:/tmp/cloudpanel-realtime-fix.sh
if errorlevel 1 (
    echo [ERROR] Failed to upload script
    echo.
    echo Manual upload method:
    echo 1. Open WinSCP or FileZilla
    echo 2. Connect to %VPS_IP%
    echo 3. Upload %LOCAL_SCRIPT% to /tmp/cloudpanel-realtime-fix.sh
    echo 4. Then run: chmod +x /tmp/cloudpanel-realtime-fix.sh ^&^& /tmp/cloudpanel-realtime-fix.sh
    pause
    exit /b 1
)
echo [SUCCESS] Fixed script uploaded successfully

:: Step 2: Make executable and run
echo.
echo [STEP 2] Making script executable and running...
ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "chmod +x /tmp/cloudpanel-realtime-fix.sh && /tmp/cloudpanel-realtime-fix.sh"
if errorlevel 1 (
    echo [ERROR] Failed to execute script
    echo.
    echo Manual execution method:
    echo 1. SSH to your VPS: ssh %VPS_USER%@%VPS_IP%
    echo 2. Run: chmod +x /tmp/cloudpanel-realtime-fix.sh
    echo 3. Run: /tmp/cloudpanel-realtime-fix.sh
    pause
    exit /b 1
)

echo.
echo =========================================================
echo                  DEPLOYMENT COMPLETED!
echo =========================================================
echo.
echo [SUCCESS] Real-time photo fix has been deployed (FIXED VERSION)
echo [SUCCESS] Syntax errors resolved
echo [SUCCESS] Photos should now display immediately without nginx reload
echo.
echo Next steps:
echo 1. Test photo upload functionality
echo 2. Monitor real-time service: systemctl status realtime-photo-monitor
echo 3. Check logs: tail -f /var/log/realtime-photo-monitor.log
echo.

:: Option to test immediately
set /p TEST="Test upload functionality now? (Y/N): "
if /i "%TEST%"=="Y" (
    echo.
    echo [INFO] Running real-time upload test...
    ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "curl -sSL https://raw.githubusercontent.com/user/repo/main/monitor-realtime-photo-status.sh | bash -s -- --test" 2>nul || (
        echo Running test manually...
        ssh -o StrictHostKeyChecking=no %VPS_USER%@%VPS_IP% "/tmp/cloudpanel-realtime-fix.sh --test"
    )
)

echo.
echo =========================================================
echo                    VERIFICATION COMMANDS
echo =========================================================
echo.
echo Check service status:
echo   ssh %VPS_USER%@%VPS_IP% "systemctl status realtime-photo-monitor"
echo.
echo View real-time logs:
echo   ssh %VPS_USER%@%VPS_IP% "tail -f /var/log/realtime-photo-monitor.log"
echo.
echo Check nginx configuration:
echo   ssh %VPS_USER%@%VPS_IP% "nginx -t"
echo.
echo Test photo access:
echo   ssh %VPS_USER%@%VPS_IP% "curl -I http://localhost/uploads/path/to/photo.jpg"
echo.
pause
