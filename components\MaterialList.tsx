'use client';

import { useState } from 'react';
import Link from 'next/link';
import SimplePagination from './SimplePagination';
import { formatIndonesiaDateTime } from '../utils/dateUtils';
import { csrfFetch } from '../utils/csrfClient';
import { toast } from 'sonner';

interface MaterialPelatihan {
  id: string;
  nama_file: string;
  path_file: string;
  size: number;
  mime_type: string;
  createdAt: Date;
}

interface MaterialListProps {
  materials: MaterialPelatihan[];
  itemsPerPage?: number;
  canUpload?: boolean;
  canDelete?: boolean;
  pelatihanId?: string;
  onMaterialDeleted?: (deletedMaterialId: string) => void;
}

export default function MaterialList({
  materials,
  itemsPerPage = 5,
  canUpload = false,
  canDelete = false,
  pelatihanId,
  onMaterialDeleted
}: MaterialListProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const totalPages = Math.ceil(materials.length / itemsPerPage);

  // Debug log to see what materials are being passed
  console.log('MaterialList received materials:', materials);
  
  // Calculate the current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = materials.slice(indexOfFirstItem, indexOfLastItem);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };
    // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (date: Date | string): string => {
    if (!date) return '-';
    const d = new Date(date);
    return formatIndonesiaDateTime(d);
  };

  // Delete material handler
  const handleDeleteMaterial = async (materialId: string, materialName: string) => {
    if (!pelatihanId) {
      toast.error('ID pelatihan tidak tersedia');
      return;
    }

    if (!confirm(`Apakah Anda yakin ingin menghapus materi "${materialName}"?`)) {
      return;
    }

    setIsDeleting(materialId);
    
    try {
      const response = await csrfFetch(`/api/pelatihan/${pelatihanId}/materi?materiId=${materialId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Gagal menghapus materi');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Gagal menghapus materi');
      }

      toast.success('Materi berhasil dihapus');
      
      // Call the callback to update parent component
      if (onMaterialDeleted) {
        onMaterialDeleted(materialId);
      }
    } catch (error) {
      console.error('Error deleting material:', error);
      toast.error(error instanceof Error ? error.message : 'Gagal menghapus materi');
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <>
      <div className="flex items-center justify-between mb-3 sm:mb-4">
        <h2 className="text-base font-medium sm:text-lg">
          Materi Kegiatan ({materials.length})
        </h2>
        {canUpload && pelatihanId && (
          <Link 
            href={`/dashboard/pelatihan/${pelatihanId}/upload-materi`}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Tambah Materi
          </Link>
        )}
      </div>
        {materials.length === 0 ? (
        <div className="p-4 text-center bg-gray-50 rounded-md">
          <p className="text-sm text-gray-500">Belum ada materi kegiatan yang diupload</p>
          {canUpload && pelatihanId && (
            <p className="mt-2 text-xs text-gray-400">
              Klik tombol "Tambah Materi" di atas untuk mengupload file PDF
            </p>
          )}
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Nama File</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Ukuran</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase sm:px-3">Tanggal Upload</th>
                  <th className="px-2 py-2 text-xs font-medium tracking-wider text-right text-gray-500 uppercase sm:px-3">Aksi</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentItems.map((material) => (
                  <tr key={material.id}>
                    <td className="px-2 sm:px-3 py-2 text-sm font-medium text-gray-900">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                        </svg>
                        <span className="truncate max-w-[120px] sm:max-w-full" title={material.nama_file}>
                          {material.nama_file}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-2 text-sm text-gray-500 sm:px-3">
                      {formatFileSize(material.size)}
                    </td>
                    <td className="px-2 py-2 text-sm text-gray-500 sm:px-3">
                      {formatDate(material.createdAt)}
                    </td>
                    <td className="px-2 py-2 text-sm font-medium text-right sm:px-3">
                      <div className="flex items-center justify-end space-x-2">
                        <a
                          href={material.path_file}
                          download={material.nama_file}
                          className="text-blue-600 hover:text-blue-900"
                          target="_blank"
                          rel="noopener noreferrer"
                          title={`Unduh ${material.nama_file}`}
                          onClick={() => {
                            // Add some debugging
                            console.log('Downloading file:', material.path_file);
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                          </svg>
                        </a>
                        {canDelete && (
                          <button
                            onClick={() => handleDeleteMaterial(material.id, material.nama_file)}
                            disabled={isDeleting === material.id}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Hapus"
                          >
                            {isDeleting === material.id ? (
                              <svg className="w-4 h-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            )}
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="pt-4">
            <SimplePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </>
      )}
    </>
  );
}
