import { PrismaClient, biodata } from '@prisma/client';
import { getUserAccessScope } from './helpers';

// Define return type for the getFilteredBiodata function
export interface FilteredBiodataResult {
  biodata: (biodata & {
    pelatihan: {
      id: string;
      nama: string;
    };
  })[];
  pelatihan: {
    id: string;
    nama: string;
  }[];
  totalPages: number;
  currentPage: number;
}

/**
 * Get filtered biodata with pagination and search functionality
 */
export async function getFilteredBiodata(
  prisma: PrismaClient,
  userId: string,
  page: number | string = 1,
  limit: number | string = 20,
  pelatihanId?: string | null,
  search?: string | null
): Promise<FilteredBiodataResult> {
  // Convert page and limit to numbers
  const pageNum = Number(page) || 1;
  const limitNum = Number(limit) || 20;
  const skip = (pageNum - 1) * limitNum;

  // Fetch user to get role information
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, role: true }
  });

  if (!user) {
    throw new Error('User not found');
  }

  // Get user's access scope based on role
  const { whereClause } = getUserAccessScope(user);

  // Get pelatihan IDs that the user has access to
  const userPelatihan = await prisma.pelatihan.findMany({
    where: whereClause,
    select: {
      id: true,
    },
  });

  // Extract just the IDs into an array
  const userPelatihanIds = userPelatihan.map(item => item.id);

  // Construct the where clause for biodata query
  const biodataWhereClause: {
    pelatihanId?: { in?: string[] } | string;
    OR?: Array<{ [key: string]: { contains: string; mode: 'insensitive' } }>;
  } = {
    pelatihanId: {
      in: userPelatihanIds,
    },
  };

  // Add pelatihan filter if provided
  if (pelatihanId) {
    biodataWhereClause.pelatihanId = pelatihanId;
  }

  // Add search filter if provided
  if (search) {
    biodataWhereClause.OR = [
      { nama: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { unit_kerja: { contains: search, mode: 'insensitive' } },
      { jabatan: { contains: search, mode: 'insensitive' } },
    ];
  }

  // Get total count for pagination
  const totalItems = await prisma.biodata.count({
    where: biodataWhereClause,
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / limitNum);  // Get biodata with limit and offset
  const biodata = await prisma.biodata.findMany({
    where: biodataWhereClause,
    skip,
    take: limitNum,
    orderBy: {
      createdAt: 'desc',
    },
    include: {
      pelatihan: {
        select: {
          id: true,
          nama: true,
        },
      },
    },
  });

  // Get pelatihan options for filter (only those user has access to)
  const pelatihan = await prisma.pelatihan.findMany({
    where: {
      id: {
        in: userPelatihanIds,
      },
    },
    select: {
      id: true,
      nama: true,
    },
    orderBy: {
      nama: 'asc',
    },
  });

  return {
    biodata,
    pelatihan,
    totalPages,
    currentPage: pageNum,
  };
}

/**
 * Check if user has access to view a specific biodata entry
 */
export async function checkBiodataAccessPermission(
  prisma: PrismaClient,
  userId: string,
  biodataId: string
): Promise<boolean> {
  // Fetch user to get role information
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, role: true }
  });

  if (!user) {
    throw new Error('User not found');
  }
  
  // Get user's access scope based on role
  const { whereClause } = getUserAccessScope(user);
  
  // Get pelatihan IDs that the user has access to
  const userPelatihan = await prisma.pelatihan.findMany({
    where: whereClause,
    select: {
      id: true,
    },
  });
  
  // Extract just the IDs into an array
  const userPelatihanIds = userPelatihan.map(item => item.id);
  
  // Get the biodata entry
  const biodata = await prisma.biodata.findUnique({
    where: { id: biodataId },
    select: { pelatihanId: true },
  });
  
  // Check if the biodata's pelatihan is in the user's allowed pelatihan list
  return !!biodata && userPelatihanIds.includes(biodata.pelatihanId);
}