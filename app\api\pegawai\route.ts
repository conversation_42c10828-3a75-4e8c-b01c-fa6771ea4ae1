import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

// GET: Mengambil daftar semua pegawai
export async function GET(_request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user || !['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const pegawai = await prisma.pegawai.findMany({
      orderBy: {
        nama: 'asc',
      },
    });

    return NextResponse.json(pegawai);
  } catch (error) {
    console.error('Error fetching pegawai:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

// POST: Membuat pegawai baru
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user || !['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const body = await request.json();
    const { nama, nip, p_gol, jabatan } = body;

    // Validasi input
    if (!nama || !nip || !p_gol || !jabatan) {
      return NextResponse.json({ error: 'Semua field harus diisi' }, { status: 400 });
    }

    // Periksa apakah NIP sudah ada
    const existingPegawai = await prisma.pegawai.findUnique({
      where: { nip },
    });

    if (existingPegawai) {
      return NextResponse.json({ error: 'NIP sudah terdaftar' }, { status: 400 });
    }

    // Buat pegawai baru
    const pegawai = await prisma.pegawai.create({
      data: {
        nama,
        nip,
        p_gol,
        jabatan,
      },
    });

    return NextResponse.json(pegawai, { status: 201 });
  } catch (error) {
    console.error('Error creating pegawai:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}