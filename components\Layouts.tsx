// File: /components/Layout.tsx
'use client';

import React, { ReactNode, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Dialog, Transition } from '@headlessui/react';

interface LayoutProps {
  children: ReactNode;
  user: {
    name: string;
    role: string;
  } | null;
}

export default function Layout({ children, user }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: 'HomeIcon' },
    { name: 'Kegiatan', href: '/dashboard/pelatihan', icon: 'AcademicCapIcon' },
    { name: 'Biodata Peserta', href: '/dashboard/biodata', icon: 'UserGroupIcon' },
    { name: 'Absensi', href: '/dashboard/absensi', icon: 'ClipboardCheckIcon' },
  ];

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen px-4 bg-gray-100 sm:px-6">
        <div className="w-full max-w-md p-4 bg-white rounded-lg shadow-md sm:p-6 md:p-8">
          <h1 className="mb-3 text-lg font-semibold sm:text-xl sm:mb-4">Anda belum login</h1>
          <p className="mb-4 text-sm sm:text-base">Silakan login untuk melanjutkan</p>
          <Link href="/login">
            <button className="w-full px-3 py-2 text-sm text-white transition-colors duration-200 bg-blue-600 rounded-md shadow-sm sm:px-4 sm:text-base hover:bg-blue-700 hover:shadow">
              Login
            </button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-100">
      {/* Mobile sidebar */}
      <Transition show={sidebarOpen} as={React.Fragment}>
        <Dialog
          as="div"
          className="fixed inset-0 z-40 flex md:hidden"
          onClose={setSidebarOpen}
        >
          <Transition.Child
            as={React.Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" />
          </Transition.Child>

          <Transition.Child
            as={React.Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <Dialog.Panel className="relative flex flex-col flex-1 w-full max-w-xs pt-5 pb-4 bg-white">
              <div className="absolute top-0 right-0 pt-2 -mr-12">
                <button
                  type="button"
                  className="flex items-center justify-center w-10 h-10 ml-1 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                  onClick={() => setSidebarOpen(false)}
                >
                  <span className="sr-only">Close sidebar</span>
                  <svg
                    className="w-6 h-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <div className="flex items-center flex-shrink-0 px-4">
                <h1 className="text-lg font-bold text-gray-900 sm:text-xl">Admin Panel</h1>
              </div>
              <div className="flex-1 h-0 mt-5 overflow-y-auto">
                <nav className="px-2 space-y-1">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`${
                        pathname === item.href
                          ? 'bg-gray-100 text-gray-900'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      } group flex items-center px-2 py-2 text-base font-medium rounded-md`}
                      onClick={() => setSidebarOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </Dialog>
      </Transition>

      {/* Static sidebar for desktop */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-56 lg:w-64">
          <div className="flex flex-col flex-1 h-0">
            <div className="flex items-center flex-shrink-0 px-4 bg-white border-b h-14 sm:h-16">
              <h1 className="text-base font-bold text-gray-900 truncate lg:text-xl">Admin Panel</h1>
            </div>
            <div className="flex flex-col flex-1 overflow-y-auto bg-white border-r">
              <nav className="flex-1 px-2 py-3 space-y-1 sm:py-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`${
                      pathname === item.href
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    } group flex items-center px-2 py-2 text-xs sm:text-sm font-medium rounded-md`}
                  >
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 w-0 overflow-hidden">
        <div className="relative z-10 flex flex-shrink-0 bg-white shadow h-14 sm:h-16">
          <button
            type="button"
            className="px-4 text-gray-500 border-r border-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <svg
              className="w-5 h-5 sm:h-6 sm:w-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
          <div className="flex justify-between flex-1 px-2 sm:px-4">
            <div className="flex items-center flex-1">
              {/* Search can be added here */}
            </div>
            <div className="flex items-center ml-2 sm:ml-4 md:ml-6">
              <div className="flex items-center">
                <span className="text-xs sm:text-sm font-medium text-gray-700 mr-2 truncate max-w-[120px] sm:max-w-full">
                  {user.name}
                </span>
                <form action="/api/auth/logout" method="post">
                  <button
                    type="submit"
                    className="px-2 py-1 text-xs text-red-600 transition-colors duration-200 sm:px-3 sm:text-sm hover:text-red-800"
                  >
                    Logout
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>

        <main className="relative flex-1 overflow-y-auto focus:outline-none">
          <div className="py-3 sm:py-4 md:py-6">
            <div className="px-2 mx-auto max-w-7xl sm:px-4 md:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}