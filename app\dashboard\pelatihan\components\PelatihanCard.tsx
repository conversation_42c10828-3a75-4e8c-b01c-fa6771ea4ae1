import Link from 'next/link';
import { formatDate } from '../../../../utils/helpers';
import DeleteButton from '../../../../components/DeleteButton';
import { deletePelatihan } from '../actions';

interface JenjangTarget {
  id: string;
  jenjang: string;
  target_peserta: number;
}

interface BiodataItem {
  id: string;
  jenjang: string | null;
}

interface AbsensiItem {
  id: string;
  nama: string;
  jenjang: string | null;
}

interface PelatihanCardProps {
  pelatihan: {
    id: string;
    nama: string;
    tempat: string;
    tgl_mulai: Date;
    tgl_berakhir: Date;
    link_registrasi: string;
    link_absensi: string;
    peserta_kegiatan: 'INTERNAL' | 'EKSTERNAL' | 'KEDUANYA' | null;
    link_absensi_internal: string | null;
    link_absensi_eksternal: string | null;
    _count: {
      biodata: number;
      absensi: number;
    };
    jenjangTargets?: JenjangTarget[];
    biodata?: BiodataItem[];
    absensi?: AbsensiItem[];
  };
}

export default function PelatihanCard({ pelatihan }: PelatihanCardProps) {
  return (
    <div className="p-4 space-y-3">
      <div className="font-medium text-gray-900">{pelatihan.nama}</div>
      
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div className="text-gray-500">Tempat:</div>
        <div>{pelatihan.tempat}</div>
        
        <div className="text-gray-500">Tanggal:</div>
        <div>{formatDate(pelatihan.tgl_mulai)} - {formatDate(pelatihan.tgl_berakhir)}</div>
        
        <div className="text-gray-500">Pendaftar:</div>
        <div>{pelatihan._count.biodata}</div>
        
        <div className="text-gray-500">Absensi:</div>
        <div>{pelatihan._count.absensi}</div>
      </div>
      
      {/* Jenjang Summary */}
      {pelatihan.jenjangTargets && pelatihan.jenjangTargets.length > 0 && (
        <div className="space-y-2">
          <div className="font-medium text-gray-700">Rekapitulasi per Jenjang:</div>
          <div className="divide-y divide-gray-100">
            {pelatihan.jenjangTargets.map((jenjang) => {
              // Count registered participants for this specific jenjang
              const pendaftarPerJenjang = pelatihan.biodata
                ? pelatihan.biodata.filter(b => b.jenjang === jenjang.jenjang).length
                : 0;
              
              return (
                <div key={jenjang.id} className="grid grid-cols-2 gap-2 py-2 text-sm">
                  <div className="text-gray-500">{jenjang.jenjang}:</div>
                  <div>
                    {pendaftarPerJenjang} / {jenjang.target_peserta}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
        <div className="space-y-2">
        <div className="text-gray-500">Link:</div>
        <div className="flex flex-col space-y-1">
          <Link
            href={`/public-pages/biodata/${pelatihan.link_registrasi}`}
            target='_blank'
            className='text-blue-600 hover:text-blue-900'
          >
            Link Biodata
          </Link>
          
          {/* Display attendance links based on participant type */}
          {pelatihan.peserta_kegiatan === 'INTERNAL' && pelatihan.link_absensi_internal && (
            <Link
              href={`/public-pages/absensi/internal/${pelatihan.link_absensi_internal}`}
              target='_blank'
              className='text-indigo-600 hover:text-indigo-900'
            >
              Link Absensi Internal
            </Link>
          )}
          
          {pelatihan.peserta_kegiatan === 'EKSTERNAL' && pelatihan.link_absensi_eksternal && (
            <Link
              href={`/public-pages/absensi/eksternal/${pelatihan.link_absensi_eksternal}`}
              target='_blank'
              className='text-emerald-600 hover:text-emerald-900'
            >
              Link Absensi Eksternal
            </Link>
          )}
          
          {pelatihan.peserta_kegiatan === 'KEDUANYA' && (
            <>
              {pelatihan.link_absensi_internal && (
                <Link
                  href={`/public-pages/absensi/internal/${pelatihan.link_absensi_internal}`}
                  target='_blank'
                  className='text-indigo-600 hover:text-indigo-900'
                >
                  Link Absensi Internal
                </Link>
              )}
              {pelatihan.link_absensi_eksternal && (
                <Link
                  href={`/public-pages/absensi/eksternal/${pelatihan.link_absensi_eksternal}`}
                  target='_blank'
                  className='text-emerald-600 hover:text-emerald-900'
                >
                  Link Absensi Eksternal
                </Link>
              )}
            </>
          )}
          
          {/* Fallback to old link if new system not set up */}
          {!pelatihan.peserta_kegiatan && pelatihan.link_absensi && (
            <Link
              href={`/public-pages/absensi/${pelatihan.link_absensi}`}
              target='_blank'
              className='text-green-600 hover:text-green-900'
            >
              Link Absensi
            </Link>
          )}
        </div>
      </div>
      
      <div className="flex justify-between pt-2 border-t border-gray-100">
        <Link
          href={`/dashboard/pelatihan/${pelatihan.id}`}
          className='px-3 py-1 text-sm text-blue-600 hover:text-blue-900'
        >
          Detail
        </Link>
        <Link
          href={`/dashboard/pelatihan/${pelatihan.id}/edit`}
          className='px-3 py-1 text-sm text-blue-600 hover:text-blue-900'
        >
          Edit
        </Link>
        <DeleteButton id={pelatihan.id} onDelete={deletePelatihan} className="px-3 py-1 text-sm" />
      </div>
    </div>
  );
}