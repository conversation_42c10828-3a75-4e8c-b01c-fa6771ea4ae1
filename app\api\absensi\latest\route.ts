import { NextResponse, NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const link = searchParams.get('link');

    if (!link) {
      return NextResponse.json(
        { error: 'Link parameter is required' },
        { status: 400 }
      );
    }

    // First, find the pelatihan by the link
    const pelatihan = await prisma.pelatihan.findFirst({
      where: {
        OR: [
          { link_absensi: link },
          { link_absensi_internal: link }
        ]
      },
      select: {
        id: true,
        nama: true,
        tempat: true,
        tgl_mulai: true,
        tgl_berakhir: true
      }
    });

    if (!pelatihan) {
      return NextResponse.json(
        { error: 'Pelatihan not found for this link' },
        { status: 404 }
      );
    }

    // Get the latest absensi for this pelatihan
    const latestAbsensi = await prisma.absensi.findFirst({
      where: {
        pelatihanId: pelatihan.id
      },
      orderBy: {
        waktu: 'desc'
      },
      include: {
        pelatihan: {
          select: {
            nama: true,
            tempat: true
          }
        }
      }
    });

    if (!latestAbsensi) {
      return NextResponse.json(
        { error: 'No absensi found for this training' },
        { status: 404 }
      );
    }

    return NextResponse.json(latestAbsensi);
  } catch (error) {
    console.error('Error fetching latest absensi:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
