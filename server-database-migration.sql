-- Database Migration Script: Server Schema Sync
-- Date: June 6, 2025
-- Purpose: Sync server database (kegiatandb) with local database (pelatihandb)
-- Apply these changes to the production server database

-- ========================================
-- 1. CREATE MISSING TRAINING_VENUE TABLE (IF NOT EXISTS)
-- ========================================

CREATE TABLE IF NOT EXISTS `training_venue` (
  `id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nama` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `alamat` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` double NOT NULL,
  `longitude` double NOT NULL,
  `radius` int NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `training_venue_is_active_idx` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- 2. PERFORMANCE INDEXES STATUS CHECK
-- ========================================

-- ✅ ALL ERROR_LOG INDEXES ALREADY EXIST (CONFIRMED)
-- Based on your SHOW INDEX output, these indexes are already present:
-- - error_log_level_idx ✅
-- - error_log_userId_idx ✅  
-- - error_log_createdAt_idx ✅
-- No action needed for error_log table.

-- ========================================
-- 3. ADD MISSING UNIQUE CONSTRAINTS (SAFE MODE)
-- ========================================

-- Add unique constraints to biodata table to prevent duplicates
-- Check if constraints already exist before adding

-- Add email unique constraint if it doesn't exist
SET @constraint_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                         WHERE TABLE_SCHEMA = DATABASE() 
                         AND TABLE_NAME = 'biodata' 
                         AND INDEX_NAME = 'Biodata_email_key');

SET @sql = IF(@constraint_exists = 0, 
             'ALTER TABLE `biodata` ADD UNIQUE KEY `Biodata_email_key` (`email`)',
             'SELECT "Unique constraint Biodata_email_key already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add nip unique constraint if it doesn't exist
SET @constraint_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                         WHERE TABLE_SCHEMA = DATABASE() 
                         AND TABLE_NAME = 'biodata' 
                         AND INDEX_NAME = 'Biodata_nip_key');

SET @sql = IF(@constraint_exists = 0, 
             'ALTER TABLE `biodata` ADD UNIQUE KEY `Biodata_nip_key` (`nip`)',
             'SELECT "Unique constraint Biodata_nip_key already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 4. INSERT DEFAULT TRAINING VENUES (OPTIONAL)
-- ========================================

-- Insert some default training venues if needed
-- Uncomment and modify as needed:

/*
INSERT INTO `training_venue` (`id`, `nama`, `alamat`, `latitude`, `longitude`, `radius`, `is_active`, `createdAt`, `updatedAt`) VALUES
('tv_001', 'Ruang Pelatihan Utama', 'Jl. Contoh No. 123, Samarinda', -0.502106, 117.153709, 100, 1, NOW(3), NOW(3)),
('tv_002', 'Aula Serbaguna', 'Jl. Contoh No. 456, Samarinda', -0.495447, 117.145351, 150, 1, NOW(3), NOW(3));
*/

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Run these queries after migration to verify success:

-- 1. Check if training_venue table was created successfully
-- SELECT COUNT(*) as venue_count FROM training_venue;

-- 2. Check if indexes were added to error_log
-- SHOW INDEX FROM error_log;

-- 3. Check if unique constraints were added to biodata
-- SHOW INDEX FROM biodata;

-- 4. Verify table structure matches local database
-- DESCRIBE training_venue;

-- ========================================
-- ROLLBACK SCRIPT (IF NEEDED)
-- ========================================

-- If you need to rollback these changes, run:
/*
-- Remove unique constraints from biodata
ALTER TABLE `biodata` 
  DROP KEY `Biodata_email_key`,
  DROP KEY `Biodata_nip_key`;

-- Remove indexes from error_log
ALTER TABLE `error_log` 
  DROP KEY `error_log_level_idx`,
  DROP KEY `error_log_userId_idx`,
  DROP KEY `error_log_createdAt_idx`;

-- Drop training_venue table
DROP TABLE IF EXISTS `training_venue`;
*/
