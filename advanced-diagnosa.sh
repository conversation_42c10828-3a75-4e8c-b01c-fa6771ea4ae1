#!/bin/bash

# Script Advanced Diagnosa - Mencari lokasi aplikasi dan konfigurasi
# Jalankan ketika diagnosa pertama tidak menemukan struktur yang diharapkan

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}"
echo "============================================"
echo "   PENCARIAN APLIKASI & KONFIGURASI VPS"
echo "============================================"
echo -e "${NC}"

log_info "Mencari lokasi aplikasi Next.js di VPS..."
echo ""

# 1. Cari folder yang mengandung Next.js app
log_info "=== MENCARI APLIKASI NEXT.JS ===="

POSSIBLE_APPS=$(find /home /var/www /opt /root -name "server.js" -o -name "package.json" -o -name ".next" 2>/dev/null | head -20 || true)

if [ -n "$POSSIBLE_APPS" ]; then
    log_success "Kemungkinan lokasi aplikasi ditemukan:"
    echo "$POSSIBLE_APPS" | while read -r app_path; do
        dir_path=$(dirname "$app_path")
        echo "   - $dir_path"
        
        # Cek apakah ada folder public di lokasi ini
        if [ -d "$dir_path/public" ]; then
            echo "     ✅ Folder public ditemukan"
            
            # Cek folder uploads
            if [ -d "$dir_path/public/uploads" ]; then
                echo "     ✅ Folder uploads ditemukan"
                
                # Hitung jumlah foto
                photo_count=$(find "$dir_path/public/uploads" -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l || echo "0")
                echo "     📸 $photo_count foto ditemukan"
                
                # Cek duplikasi public/public
                if [ -d "$dir_path/public/public" ]; then
                    echo "     ⚠️  DUPLIKASI: public/public/ ditemukan!"
                    dup_photo_count=$(find "$dir_path/public/public" -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l || echo "0")
                    echo "     📸 $dup_photo_count foto di duplikasi"
                fi
            else
                echo "     ❌ Folder uploads tidak ditemukan"
            fi
        else
            echo "     ❌ Folder public tidak ditemukan"
        fi
        echo ""
    done
else
    log_error "Tidak ada aplikasi Next.js yang ditemukan"
    echo "   Kemungkinan:"
    echo "   1. Aplikasi belum di-deploy"
    echo "   2. Lokasi aplikasi di direktori yang tidak umum"
    echo "   3. Permission issue untuk akses folder"
fi

echo ""
log_info "=== MENCARI KONFIGURASI NGINX ===="

# Cari file konfigurasi nginx di berbagai lokasi
NGINX_CONFIGS=$(find /etc -name "*.conf" -path "*/nginx/*" 2>/dev/null | head -10 || true)

if [ -n "$NGINX_CONFIGS" ]; then
    log_success "File konfigurasi nginx ditemukan:"
    echo "$NGINX_CONFIGS" | while read -r config_file; do
        echo "   - $config_file"
        
        # Cek apakah ada konfigurasi untuk domain kegiatan.bpmpkaltim.id
        if grep -q "kegiatan.bpmpkaltim.id" "$config_file" 2>/dev/null; then
            echo "     ✅ Mengandung domain kegiatan.bpmpkaltim.id"
            
            # Cek root directory
            root_dir=$(grep -E "^\s*root\s+" "$config_file" | head -1 | awk '{print $2}' | sed 's/;//' || true)
            if [ -n "$root_dir" ]; then
                echo "     📁 Root directory: $root_dir"
            fi
            
            # Cek proxy pass ke Next.js
            if grep -q "proxy_pass.*3000" "$config_file" 2>/dev/null; then
                echo "     🔄 Proxy ke Next.js ditemukan"
            fi
        fi
    done
else
    log_warning "File konfigurasi nginx tidak ditemukan di /etc/nginx/"
    
    # Cari di lokasi alternatif
    ALT_CONFIGS=$(find /usr/local /opt -name "nginx.conf" 2>/dev/null || true)
    if [ -n "$ALT_CONFIGS" ]; then
        log_info "Konfigurasi nginx di lokasi alternatif:"
        echo "$ALT_CONFIGS"
    fi
fi

echo ""
log_info "=== MENCARI PROSES YANG BERJALAN ===="

# Cek proses Node.js/PM2
NODE_PROCESSES=$(ps aux | grep -E "(node|pm2)" | grep -v grep || true)
if [ -n "$NODE_PROCESSES" ]; then
    log_success "Proses Node.js/PM2 ditemukan:"
    echo "$NODE_PROCESSES" | while read -r process; do
        echo "   $process"
    done
else
    log_warning "Tidak ada proses Node.js/PM2 yang berjalan"
fi

echo ""
# Cek proses nginx
NGINX_PROCESS=$(ps aux | grep nginx | grep -v grep || true)
if [ -n "$NGINX_PROCESS" ]; then
    log_success "Nginx berjalan:"
    echo "$NGINX_PROCESS"
else
    log_error "Nginx tidak berjalan!"
fi

echo ""
log_info "=== MENCARI FILE FOTO SPECIFIC ===="

# Cari file foto dari URL yang bermasalah
SAMPLE_PHOTO="absensi-2025-06-07T08-48-47-545Z-db7abc29-1921-46dd-8fa8-d1a6f83ec1a0.jpg"
log_info "Mencari file: $SAMPLE_PHOTO"

PHOTO_LOCATIONS=$(find /home /var/www /opt -name "$SAMPLE_PHOTO" 2>/dev/null || true)
if [ -n "$PHOTO_LOCATIONS" ]; then
    log_success "File foto ditemukan di:"
    echo "$PHOTO_LOCATIONS" | while read -r photo_path; do
        echo "   - $photo_path"
        
        # Analisis path
        if [[ "$photo_path" == *"public/public"* ]]; then
            log_warning "   ⚠️  File di lokasi duplikasi!"
        elif [[ "$photo_path" == *"public/uploads"* ]]; then
            log_success "   ✅ File di lokasi yang benar"
        fi
        
        # Cek permissions
        permissions=$(ls -la "$photo_path" | awk '{print $1, $3, $4}')
        echo "   📋 Permissions: $permissions"
    done
else
    log_error "File foto tidak ditemukan di seluruh sistem"
    echo "   Kemungkinan file belum pernah diupload atau sudah dihapus"
fi

echo ""
log_info "=== CEK PORT DAN NETWORK ===="

# Cek port yang digunakan
PORTS=$(netstat -tlnp 2>/dev/null | grep -E ":(80|443|3000)" || true)
if [ -n "$PORTS" ]; then
    log_success "Port yang aktif:"
    echo "$PORTS"
else
    log_warning "Tidak ada port web yang terdeteksi aktif"
fi

echo ""
log_info "=== REKOMENDASI BERDASARKAN TEMUAN ===="

echo "Berdasarkan pencarian di atas, lakukan hal berikut:"
echo ""

# Berikan rekomendasi berdasarkan temuan
if [ -n "$POSSIBLE_APPS" ]; then
    log_success "LANGKAH 1: Update script diagnosa dengan path yang benar"
    echo "   Edit file diagnosa-photo-upload.sh"
    echo "   Ganti APP_DIR dengan salah satu path di atas yang memiliki folder public"
    echo ""
fi

if [ -n "$NGINX_CONFIGS" ]; then
    log_success "LANGKAH 2: Update konfigurasi nginx"
    echo "   Edit file yang mengandung domain kegiatan.bpmpkaltim.id"
    echo "   Tambahkan konfigurasi untuk serve static files"
    echo ""
fi

if [ -n "$PHOTO_LOCATIONS" ]; then
    log_success "LANGKAH 3: Perbaiki lokasi file"
    echo "   Jika file ada di public/public/, pindahkan ke public/uploads/"
    echo "   Set permission yang benar (www-data:www-data 755)"
    echo ""
fi

log_info "LANGKAH 4: Test akses setelah perbaikan"
echo "   curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/915b8bfd-14ad-48d0-805f-6509f9540dd3/$SAMPLE_PHOTO"

echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   PENCARIAN SELESAI"
echo "============================================"
echo -e "${NC}"
