-- Quick Database Status Check & Minimal Migration
-- Run these queries to see what actually needs to be done

-- ========================================
-- STATUS CHECK QUERIES
-- ========================================

-- 1. Check if training_venue table exists
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'training_venue')
    THEN '✅ training_venue table EXISTS'
    ELSE '❌ training_venue table MISSING - NEEDS CREATION'
  END as training_venue_status;

-- 2. Check biodata unique constraints
SELECT 
  CASE 
    WHEN COUNT(CASE WHEN INDEX_NAME = 'Biodata_email_key' THEN 1 END) > 0
    THEN '✅ email unique constraint EXISTS'
    ELSE '❌ email unique constraint MISSING'
  END as email_constraint_status,
  CASE 
    WHEN COUNT(CASE WHEN INDEX_NAME = 'Biodata_nip_key' THEN 1 END) > 0
    THEN '✅ nip unique constraint EXISTS'
    ELSE '❌ nip unique constraint MISSING'
  END as nip_constraint_status
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'biodata' 
AND INDEX_NAME IN ('Biodata_email_key', 'Biodata_nip_key');

-- ========================================
-- MINIMAL MIGRATION SCRIPT
-- ========================================

-- Based on your feedback, error_log indexes already exist ✅
-- Only run the commands below if the status check shows they're missing:

-- Create training_venue table (only if missing):
CREATE TABLE IF NOT EXISTS `training_venue` (
  `id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `nama` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `alamat` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `latitude` double NOT NULL,
  `longitude` double NOT NULL,
  `radius` int NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `training_venue_is_active_idx` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add biodata unique constraints (only if missing - check status first):
-- Run these only if the status check shows they're missing:

-- For email constraint (run only if status shows MISSING):
-- ALTER TABLE `biodata` ADD UNIQUE KEY `Biodata_email_key` (`email`);

-- For nip constraint (run only if status shows MISSING):
-- ALTER TABLE `biodata` ADD UNIQUE KEY `Biodata_nip_key` (`nip`);

-- ========================================
-- VERIFICATION
-- ========================================
-- Run these after migration to confirm success:

SELECT COUNT(*) as training_venue_count FROM training_venue;
SHOW INDEX FROM biodata;
DESCRIBE training_venue;
