import { Metadata } from 'next';
import { getCurrentUser } from '../../../../lib/auth';
import { redirect } from 'next/navigation';
import PelatihanForm from '../form';

export const metadata: Metadata = {
  title: 'Tambah Pelatihan - Dashboard',
};

// Mark the page as dynamic
export const dynamic = 'force-dynamic';

export default async function AddPelatihanPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/login');
  }

  // Redirect if user is <PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON> (view-only roles)
  const isViewOnly = user.role === 'KEPALA' || user.role === 'KASUBAG';
  if (isViewOnly) {
    redirect('/dashboard/pelatihan');
  }

  return (
    <div className="container px-4 py-8 mx-auto">
      <h1 className="mb-6 text-2xl font-bold">Tambah Kegiatan Baru</h1>
      <PelatihanForm />
    </div>
  );
}
