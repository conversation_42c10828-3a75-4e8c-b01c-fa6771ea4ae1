import { NextRequest, NextResponse } from 'next/server';
import { AntiGpsSpoofing, type LocationReading } from '../../../../lib/anti-gps-spoofing';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { location, deviceInfo, locationHistory } = body;

    // Validate input
    if (!location || typeof location.latitude !== 'number' || typeof location.longitude !== 'number') {
      return NextResponse.json({
        success: false,
        error: 'Invalid location data'
      }, { status: 400 });
    }

    // Create location reading
    const locationReading: LocationReading = {
      latitude: location.latitude,
      longitude: location.longitude,
      accuracy: location.accuracy || 100,
      timestamp: location.timestamp || Date.now(),
      speed: location.speed,
      heading: location.heading,
      altitude: location.altitude
    };

    // Initialize anti-spoofing analyzer
    const antiSpoofing = new AntiGpsSpoofing(15);

    // Add history if provided
    if (locationHistory && Array.isArray(locationHistory)) {
      locationHistory.forEach((reading: LocationReading) => {
        antiSpoofing.analyzeLocation(reading);
      });
    }

    // Analyze current location
    const analysis = antiSpoofing.analyzeLocation(locationReading);

    // Additional server-side validations
    const serverValidations = await performServerSideValidations(locationReading, deviceInfo);

    // Combine client and server analysis
    const finalRiskScore = Math.min(analysis.riskScore + serverValidations.additionalRisk, 100);
    const finalWarnings = [...analysis.warnings, ...serverValidations.warnings];

    // Log suspicious activities
    if (finalRiskScore > 70) {
      console.warn('Suspicious GPS detected:', {
        location: locationReading,
        analysis,
        serverValidations,
        deviceInfo,
        timestamp: new Date().toISOString()
      });
    }

    // Determine if GPS is acceptable for internal attendance
    const isAcceptable = finalRiskScore < 70 && analysis.isGenuine;

    return NextResponse.json({
      success: true,
      validation: {
        isValid: isAcceptable,
        isGenuine: analysis.isGenuine,
        riskLevel: analysis.riskLevel,
        riskScore: finalRiskScore,
        warnings: finalWarnings,
        recommendations: analysis.recommendations,
        detectionFlags: analysis.detectionFlags,
        serverValidations: serverValidations.flags,
        timestamp: Date.now()
      }
    });

  } catch (error) {
    console.error('Error validating internal GPS:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error during GPS validation'
    }, { status: 500 });
  }
}

/**
 * Perform additional server-side validations
 */
async function performServerSideValidations(
  location: LocationReading, 
  deviceInfo: any
): Promise<{
  additionalRisk: number;
  warnings: string[];
  flags: {
    locationServiceConsistent: boolean;
    deviceInfoValid: boolean;
    requestPatternNormal: boolean;
  };
}> {
  const warnings: string[] = [];
  let additionalRisk = 0;
  
  const flags = {
    locationServiceConsistent: true,
    deviceInfoValid: true,
    requestPatternNormal: true
  };

  // 1. Device Info Validation
  if (deviceInfo) {
    // Check for suspicious user agents (common in fake GPS tools)
    const suspiciousAgents = [
      'FakeGPS',
      'MockLocation',
      'LocationSpoofer',
      'GPSEmulator'
    ];

    if (deviceInfo.userAgent) {
      const userAgent = deviceInfo.userAgent.toLowerCase();
      const hasSuspiciousAgent = suspiciousAgents.some(agent => 
        userAgent.includes(agent.toLowerCase())
      );

      if (hasSuspiciousAgent) {
        flags.deviceInfoValid = false;
        additionalRisk += 30;
        warnings.push('Device menjalankan aplikasi pemalsuan GPS');
      }
    }

    // Check platform consistency
    if (deviceInfo.platform && deviceInfo.userAgent) {
      const platform = deviceInfo.platform.toLowerCase();
      const userAgent = deviceInfo.userAgent.toLowerCase();
      
      // Basic platform-userAgent consistency check
      if (platform.includes('win') && !userAgent.includes('windows')) {
        flags.deviceInfoValid = false;
        additionalRisk += 15;
        warnings.push('Informasi platform tidak konsisten');
      }
    }
  }

  // 2. Location Service Consistency
  // Check if location accuracy is suspiciously perfect
  if (location.accuracy < 2) {
    flags.locationServiceConsistent = false;
    additionalRisk += 20;
    warnings.push('GPS accuracy terlalu sempurna (kemungkinan palsu)');
  }

  // Check for coordinates in suspicious ranges (e.g., exact lat/lng)
  const latStr = location.latitude.toString();
  const lngStr = location.longitude.toString();
  
  if (latStr.endsWith('.0') && lngStr.endsWith('.0')) {
    flags.locationServiceConsistent = false;
    additionalRisk += 25;
    warnings.push('Koordinat GPS terlalu bulat (kemungkinan manual input)');
  }

  // 3. Geospatial Validation
  // Check if coordinates are in impossible locations (e.g., middle of ocean)
  const geospatialCheck = await validateGeospatialLocation(location);
  if (!geospatialCheck.isValid) {
    flags.locationServiceConsistent = false;
    additionalRisk += geospatialCheck.riskIncrease;
    warnings.push(geospatialCheck.warning);
  }

  // 4. Speed and Movement Validation
  if (location.speed !== undefined) {
    // Speed reported as 0 when moving is suspicious
    if (location.speed === 0 && location.accuracy < 10) {
      additionalRisk += 10;
      warnings.push('Kecepatan tidak wajar untuk akurasi GPS yang tinggi');
    }

    // Extremely high reported speed
    if (location.speed > 150) { // > 540 km/h
      flags.locationServiceConsistent = false;
      additionalRisk += 35;
      warnings.push(`Kecepatan GPS tidak mungkin: ${(location.speed * 3.6).toFixed(1)} km/h`);
    }
  }

  return {
    additionalRisk,
    warnings,
    flags
  };
}

/**
 * Validate if coordinates are in a realistic location
 */
async function validateGeospatialLocation(location: LocationReading): Promise<{
  isValid: boolean;
  riskIncrease: number;
  warning: string;
}> {
  const { latitude, longitude } = location;

  // Check if coordinates are within Earth's valid range
  if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
    return {
      isValid: false,
      riskIncrease: 50,
      warning: 'Koordinat GPS di luar jangkauan valid Bumi'
    };
  }

  // Check for null island (0,0) which is suspicious for real location
  if (latitude === 0 && longitude === 0) {
    return {
      isValid: false,
      riskIncrease: 40,
      warning: 'Koordinat GPS menunjukkan Null Island (kemungkinan error/palsu)'
    };
  }

  // Check for coordinates in the middle of oceans (basic check)
  // For Indonesia, valid latitude range approximately -11 to 6, longitude 95 to 141
  const inIndonesiaRange = latitude >= -11 && latitude <= 6 && 
                          longitude >= 95 && longitude <= 141;

  if (!inIndonesiaRange) {
    // Not necessarily invalid, but increase risk slightly for locations far from Indonesia
    return {
      isValid: true,
      riskIncrease: 5,
      warning: 'Lokasi di luar wilayah Indonesia'
    };
  }

  return {
    isValid: true,
    riskIncrease: 0,
    warning: ''
  };
}

export async function GET() {
  return NextResponse.json({
    message: 'Internal GPS Validation API',
    version: '1.0.0',
    features: [
      'Anti-GPS Spoofing Detection',
      'Movement Pattern Analysis',
      'Device Consistency Validation',
      'Geospatial Location Verification'
    ]
  });
}
