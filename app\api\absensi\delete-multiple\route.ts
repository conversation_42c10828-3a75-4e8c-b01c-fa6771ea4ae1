import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get IDs from request body
    const { ids } = await req.json();
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Invalid request: ids array is required' },
        { status: 400 }
      );
    }

    // Delete the absensi records
    const result = await prisma.absensi.deleteMany({
      where: {
        id: {
          in: ids
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} absensi berhasil dihapus`,
      count: result.count
    });
  } catch (error) {
    console.error('Error deleting absensi:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menghapus data' },
      { status: 500 }
    );
  }
}
