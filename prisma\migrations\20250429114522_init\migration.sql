-- CreateTable
CREATE TABLE `absensi` (
    `id` VARCHAR(191) NOT NULL,
    `pelatihanId` VARCHAR(191) NOT NULL,
    `nama` VARCHAR(191) NOT NULL,
    `nip_nik` VARCHAR(191) NULL,
    `jabatan` VARCHAR(191) NULL,
    `unit_kerja` VARCHAR(191) NOT NULL,
    `no_hp` VARCHAR(191) NOT NULL,
    `waktu` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `tanda_tangan` TEXT NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `jenjang` ENUM('PAUD', 'SD', 'SMP', 'SMA', 'UMUM', 'DINAS', 'BPMP', 'LAINNYA') NULL,

    INDEX `Absensi_pelatihanId_idx`(`pelatihanId`),
    <PERSON><PERSON>AR<PERSON>Y (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `biodata` (
    `id` VARCHAR(191) NOT NULL,
    `nama` VARCHAR(191) NOT NULL,
    `tempat_lahir` VARCHAR(191) NOT NULL,
    `tanggal_lahir` DATETIME(3) NOT NULL,
    `pendidikan` VARCHAR(191) NOT NULL,
    `jenis_kelamin` VARCHAR(191) NOT NULL,
    `nip` VARCHAR(191) NULL,
    `pangkat_golongan` VARCHAR(191) NULL,
    `jabatan` VARCHAR(191) NOT NULL,
    `unit_kerja` VARCHAR(191) NOT NULL,
    `alamat_unit_kerja` VARCHAR(191) NOT NULL,
    `npwp` VARCHAR(191) NULL,
    `email` VARCHAR(191) NOT NULL,
    `no_hp` VARCHAR(191) NOT NULL,
    `tanda_tangan` TEXT NOT NULL,
    `pelatihanId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `jenjang` ENUM('PAUD', 'SD', 'SMP', 'SMA', 'UMUM', 'DINAS', 'BPMP', 'LAINNYA') NULL,

    UNIQUE INDEX `Biodata_nip_key`(`nip`),
    UNIQUE INDEX `Biodata_email_key`(`email`),
    INDEX `Biodata_pelatihanId_idx`(`pelatihanId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `pelatihan` (
    `id` VARCHAR(191) NOT NULL,
    `nama` VARCHAR(191) NOT NULL,
    `tempat` VARCHAR(191) NOT NULL,
    `tgl_mulai` DATETIME(3) NOT NULL,
    `tgl_berakhir` DATETIME(3) NOT NULL,
    `link_registrasi` VARCHAR(191) NOT NULL,
    `link_absensi` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `jenjang` ENUM('PAUD', 'SD', 'SMP', 'SMA', 'UMUM', 'DINAS', 'BPMP', 'LAINNYA') NOT NULL,
    `target_peserta` INTEGER NOT NULL,
    `userId` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `Pelatihan_link_registrasi_key`(`link_registrasi`),
    UNIQUE INDEX `Pelatihan_link_absensi_key`(`link_absensi`),
    INDEX `Pelatihan_userId_idx`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `pelatihan_jenjang_target` (
    `id` VARCHAR(191) NOT NULL,
    `pelatihanId` VARCHAR(191) NOT NULL,
    `jenjang` ENUM('PAUD', 'SD', 'SMP', 'SMA', 'UMUM', 'DINAS', 'BPMP', 'LAINNYA') NOT NULL,
    `target_peserta` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `pelatihan_jenjang_target_pelatihanId_idx`(`pelatihanId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `email` VARCHAR(191) NOT NULL,
    `password` VARCHAR(191) NOT NULL,
    `role` ENUM('ADMIN', 'USER', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5', 'KEPALA', 'KASUBAG') NOT NULL DEFAULT 'USER',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `User_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `error_log` (
    `id` VARCHAR(191) NOT NULL,
    `level` ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    `message` TEXT NOT NULL,
    `stack` TEXT NULL,
    `path` VARCHAR(191) NULL,
    `method` VARCHAR(191) NULL,
    `userId` VARCHAR(191) NULL,
    `userAgent` TEXT NULL,
    `ip` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `resolved` BOOLEAN NOT NULL DEFAULT false,

    INDEX `error_log_level_idx`(`level`),
    INDEX `error_log_userId_idx`(`userId`),
    INDEX `error_log_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `materi_pelatihan` (
    `id` VARCHAR(191) NOT NULL,
    `pelatihanId` VARCHAR(191) NOT NULL,
    `nama_file` VARCHAR(191) NOT NULL,
    `path_file` VARCHAR(191) NOT NULL,
    `size` INTEGER NOT NULL,
    `mime_type` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `materi_pelatihan_pelatihanId_idx`(`pelatihanId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `pegawai` (
    `id` VARCHAR(191) NOT NULL,
    `nama` VARCHAR(191) NOT NULL,
    `nip` VARCHAR(191) NOT NULL,
    `p_gol` VARCHAR(191) NOT NULL,
    `jabatan` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `pegawai_nip_key`(`nip`),
    INDEX `pegawai_nip_idx`(`nip`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `t_tugas` (
    `id` VARCHAR(191) NOT NULL,
    `kab_kota` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `panitia` (
    `id` VARCHAR(191) NOT NULL,
    `pelatihanId` VARCHAR(191) NOT NULL,
    `pegawaiId` VARCHAR(191) NOT NULL,
    `jabatan` ENUM('PENGARAH', 'PENANGGUNG_JAWAB', 'KETUA', 'ANGGOTA', 'MODERATOR', 'HOST') NOT NULL,
    `lokasiId` VARCHAR(191) NOT NULL,
    `no_surat` VARCHAR(191) NULL,
    `keterangan` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `panitia_pelatihanId_idx`(`pelatihanId`),
    INDEX `panitia_pegawaiId_idx`(`pegawaiId`),
    INDEX `panitia_lokasiId_idx`(`lokasiId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `absensi` ADD CONSTRAINT `absensi_pelatihanId_fkey` FOREIGN KEY (`pelatihanId`) REFERENCES `pelatihan`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `biodata` ADD CONSTRAINT `biodata_pelatihanId_fkey` FOREIGN KEY (`pelatihanId`) REFERENCES `pelatihan`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `pelatihan` ADD CONSTRAINT `Pelatihan_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `pelatihan_jenjang_target` ADD CONSTRAINT `pelatihan_jenjang_target_pelatihanId_fkey` FOREIGN KEY (`pelatihanId`) REFERENCES `pelatihan`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `materi_pelatihan` ADD CONSTRAINT `materi_pelatihan_pelatihanId_fkey` FOREIGN KEY (`pelatihanId`) REFERENCES `pelatihan`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `panitia` ADD CONSTRAINT `panitia_lokasiId_fkey` FOREIGN KEY (`lokasiId`) REFERENCES `t_tugas`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `panitia` ADD CONSTRAINT `panitia_pegawaiId_fkey` FOREIGN KEY (`pegawaiId`) REFERENCES `pegawai`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `panitia` ADD CONSTRAINT `panitia_pelatihanId_fkey` FOREIGN KEY (`pelatihanId`) REFERENCES `pelatihan`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
