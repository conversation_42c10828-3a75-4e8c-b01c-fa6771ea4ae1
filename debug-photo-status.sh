#!/bin/bash

# Debug Helper - Manual Check untuk Photo Upload Issue
# Gunakan script ini untuk mengecek status setelah emergency fix

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${GREEN}"
echo "======================================"
echo "   DEBUG PHOTO UPLOAD STATUS"
echo "======================================"
echo -e "${NC}"

echo -e "${BLUE}1. MENGECEK STRUKTUR FOLDER:${NC}"
echo ""

# Cari folder uploads
echo "Mencari folder uploads..."
find /home /var/www /opt /usr/local -type d -name "uploads" 2>/dev/null | head -10

echo ""
echo "Mencari folder public dengan uploads..."
find /home /var/www /opt /usr/local -path "*/public/uploads" -type d 2>/dev/null | head -10

echo ""
echo -e "${BLUE}2. MENGECEK FILE FOTO:${NC}"
echo ""

# Hitung foto
TOTAL_PHOTOS=0
for upload_dir in $(find /home /var/www /opt /usr/local -path "*/public/uploads" -type d 2>/dev/null); do
    photos=$(find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l)
    if [ $photos -gt 0 ]; then
        echo "📁 $upload_dir: $photos foto"
        TOTAL_PHOTOS=$((TOTAL_PHOTOS + photos))
        
        # Show sample files
        echo "   Sample files:"
        find "$upload_dir" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | head -3 | sed 's/^/   /'
    fi
done

echo ""
echo "📊 Total foto ditemukan: $TOTAL_PHOTOS"

echo ""
echo -e "${BLUE}3. MENGECEK NGINX CONFIG:${NC}"
echo ""

# Cek nginx configs
for config in /etc/nginx/sites-available/* /etc/nginx/conf.d/* /etc/nginx/nginx.conf; do
    if [ -f "$config" ]; then
        if grep -q "location /uploads/" "$config" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} $config - Ada konfigurasi uploads"
            echo "   Detail:"
            grep -A 5 "location /uploads/" "$config" | sed 's/^/   /'
        else
            echo -e "${YELLOW}⚠${NC} $config - Tidak ada konfigurasi uploads"
        fi
    fi
done

echo ""
echo -e "${BLUE}4. MENGECEK STATUS SERVICES:${NC}"
echo ""

# Nginx status
if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✓${NC} Nginx: Running"
else
    echo -e "${RED}✗${NC} Nginx: Not running"
fi

# PM2 status
if command -v pm2 >/dev/null 2>&1; then
    echo -e "${GREEN}✓${NC} PM2 tersedia"
    pm2 list 2>/dev/null | grep -E "(name|status)" || echo "   Tidak ada process PM2"
else
    echo -e "${YELLOW}⚠${NC} PM2: Tidak terinstall"
fi

echo ""
echo -e "${BLUE}5. TEST AKSES URL:${NC}"
echo ""

# Test beberapa URL pattern yang umum
TEST_URLS=(
    "https://kegiatan.bpmpkaltim.id/uploads/test.txt"
    "http://localhost/uploads/test.txt"
    "https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/test.jpg"
)

for url in "${TEST_URLS[@]}"; do
    if command -v curl >/dev/null 2>&1; then
        status=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 5 2>/dev/null || echo "000")
        if [ "$status" = "404" ]; then
            echo -e "${YELLOW}404${NC} $url (Normal jika file tidak ada)"
        elif [ "$status" = "200" ]; then
            echo -e "${GREEN}200${NC} $url (OK)"
        elif [ "$status" = "000" ]; then
            echo -e "${RED}ERR${NC} $url (Connection failed)"
        else
            echo -e "${BLUE}$status${NC} $url"
        fi
    else
        echo -e "${YELLOW}⚠${NC} Curl tidak tersedia untuk test $url"
    fi
done

echo ""
echo -e "${BLUE}6. PERMISSIONS CHECK:${NC}"
echo ""

for upload_dir in $(find /home /var/www /opt /usr/local -path "*/public/uploads" -type d 2>/dev/null | head -3); do
    echo "📁 $upload_dir"
    ls -la "$upload_dir" | head -5 | sed 's/^/   /'
    echo ""
done

echo ""
echo -e "${BLUE}7. DISK SPACE:${NC}"
echo ""
df -h | grep -E "(Filesystem|/dev/)" | head -3

echo ""
echo -e "${GREEN}"
echo "======================================"
echo "   QUICK ACTIONS"
echo "======================================"
echo -e "${NC}"

echo "Jika masih ada masalah:"
echo ""
echo "🔧 Fix permissions:"
echo "   sudo chown -R www-data:www-data /path/to/uploads"
echo "   sudo chmod -R 755 /path/to/uploads"
echo ""
echo "🔧 Restart services:"
echo "   sudo systemctl reload nginx"
echo "   pm2 restart all"
echo ""
echo "🔧 Manual test URL:"
echo "   curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/[filename]"
echo ""
echo "🔧 Check logs:"
echo "   tail -f /var/log/nginx/error.log"
echo "   tail -f /var/log/nginx/access.log"
echo "   pm2 logs"
