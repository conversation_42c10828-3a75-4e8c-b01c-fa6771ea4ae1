// app/api/upload-imagekit/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { uploadToImageKit } from '@/lib/imagekit';
import { randomUUID } from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'biodata';

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'File tidak ditemukan' },
        { status: 400 }
      );
    }

    // Validasi tipe file
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: 'Tipe file tidak didukung. Gunakan JPEG, PNG, atau WebP' },
        { status: 400 }
      );
    }

    // Validasi ukuran file (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, message: 'Ukuran file maksimal 5MB' },
        { status: 400 }
      );
    }

    // Convert file ke buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${randomUUID()}.${fileExtension}`;

    // Upload ke ImageKit
    const result = await uploadToImageKit(buffer, fileName, folder);

    return NextResponse.json({
      success: true,
      data: {
        public_id: result.fileId, // Use fileId as public_id for compatibility
        secure_url: result.url,
        url: result.url,
        format: fileExtension,
        width: result.width,
        height: result.height,
        bytes: result.size,
        thumbnail: result.thumbnail,
        filePath: result.filePath
      },
    });

  } catch (error) {
    console.error('ImageKit upload error:', error);
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan saat mengupload ke ImageKit' },
      { status: 500 }
    );
  }
}
