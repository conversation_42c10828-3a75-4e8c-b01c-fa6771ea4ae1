@echo off
setlocal enabledelayedexpansion

:: POST-DEPLOYMENT VERIFICATION TEST
:: Comprehensive test to verify photo upload fix is working

title Post-Deployment Photo Upload Verification

echo.
echo ===============================================
echo    POST-DEPLOYMENT VERIFICATION TEST
echo    Testing Photo Upload Auto-Refresh Fix
echo ===============================================
echo.

set "VPS_HOST=*************"
set "DOMAIN=kegiatan.bpmpkaltim.id"

echo [36m🧪 TESTING PLAN:[0m
echo   1. Test API endpoint accessibility
echo   2. Test static file serving configuration  
echo   3. Test file permissions on uploaded files
echo   4. Test nginx configuration
echo   5. Simulate photo upload workflow
echo.

set /p run_test="Run comprehensive verification test? (y/n): "

if /i not "%run_test%"=="y" (
    echo [33m[INFO][0m Test cancelled
    pause
    exit /b 0
)

echo.
echo [35m[ACTION][0m Starting verification tests...

:: Test 1: API Endpoint Accessibility
echo.
echo [36m[TEST 1][0m Testing API endpoint accessibility...

echo [36m[INFO][0m Testing /api/absensi/upload-photo...
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/api/absensi/upload-photo > temp1.txt
set /p status1=<temp1.txt
del temp1.txt

echo [36m[INFO][0m Testing /api/biodata/upload-photo...
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/api/biodata/upload-photo > temp2.txt
set /p status2=<temp2.txt
del temp2.txt

if "%status1%"=="405" if "%status2%"=="405" (
    echo [32m[PASS][0m API endpoints are accessible
    set "test1=PASS"
) else (
    echo [33m[WARNING][0m API endpoints status: %status1%, %status2%
    set "test1=WARNING"
)

:: Test 2: Static File Serving
echo.
echo [36m[TEST 2][0m Testing static file serving...

echo [36m[INFO][0m Testing /uploads/ directory...
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/uploads/ > temp3.txt
set /p status3=<temp3.txt
del temp3.txt

if "%status3%"=="403" (
    echo [32m[PASS][0m Static directory properly configured (403 expected)
    set "test2=PASS"
) else if "%status3%"=="200" (
    echo [32m[PASS][0m Static directory accessible
    set "test2=PASS"
) else (
    echo [31m[FAIL][0m Static directory not accessible (status: %status3%)
    set "test2=FAIL"
)

:: Test 3: VPS File Permissions
echo.
echo [36m[TEST 3][0m Testing VPS file permissions...

ssh root@%VPS_HOST% "ls -la /var/www/vhosts/kegiatan.bpmpkaltim.id/public_html/uploads/ 2>/dev/null | head -5" > temp_perms.txt

if %errorlevel%==0 (
    echo [36m[INFO][0m Current uploads directory permissions:
    type temp_perms.txt
    echo [32m[PASS][0m File permissions check completed
    set "test3=PASS"
) else (
    echo [33m[WARNING][0m Could not check file permissions
    set "test3=WARNING"
)

del temp_perms.txt 2>nul

:: Test 4: Nginx Configuration
echo.
echo [36m[TEST 4][0m Testing nginx configuration...

ssh root@%VPS_HOST% "nginx -t 2>&1" > temp_nginx.txt

findstr /i "successful" temp_nginx.txt >nul
if %errorlevel%==0 (
    echo [32m[PASS][0m Nginx configuration is valid
    set "test4=PASS"
) else (
    echo [33m[WARNING][0m Nginx configuration issues detected:
    type temp_nginx.txt
    set "test4=WARNING"
)

del temp_nginx.txt

:: Test 5: Application Status
echo.
echo [36m[TEST 5][0m Testing application status...

ssh root@%VPS_HOST% "pm2 status | grep -E 'online|stopped'" > temp_pm2.txt

findstr /i "online" temp_pm2.txt >nul
if %errorlevel%==0 (
    echo [32m[PASS][0m Application is running
    echo [36m[INFO][0m PM2 Status:
    type temp_pm2.txt
    set "test5=PASS"
) else (
    echo [31m[FAIL][0m Application is not running properly
    type temp_pm2.txt
    set "test5=FAIL"
)

del temp_pm2.txt

:: Summary
echo.
echo ===============================================
echo    VERIFICATION TEST RESULTS
echo ===============================================
echo.

echo [36m📊 TEST SUMMARY:[0m
echo   Test 1 - API Endpoints: %test1%
echo   Test 2 - Static Files: %test2%  
echo   Test 3 - File Permissions: %test3%
echo   Test 4 - Nginx Config: %test4%
echo   Test 5 - Application Status: %test5%
echo.

:: Count passed tests
set "passed=0"
if "%test1%"=="PASS" set /a passed+=1
if "%test2%"=="PASS" set /a passed+=1  
if "%test3%"=="PASS" set /a passed+=1
if "%test4%"=="PASS" set /a passed+=1
if "%test5%"=="PASS" set /a passed+=1

echo [36m🎯 OVERALL RESULT:[0m
if %passed% geq 4 (
    echo [32m✅ DEPLOYMENT SUCCESSFUL[0m (%passed%/5 tests passed)
    echo.
    echo [32m[SUCCESS][0m Photo upload auto-refresh fix is working!
    echo [36m[INFO][0m Photos should now be immediately accessible after upload
) else if %passed% geq 3 (
    echo [33m⚠️  DEPLOYMENT MOSTLY SUCCESSFUL[0m (%passed%/5 tests passed)
    echo.
    echo [33m[WARNING][0m Some issues detected but core functionality should work
    echo [36m[INFO][0m Consider running troubleshoot-autorefresh.bat
) else (
    echo [31m❌ DEPLOYMENT NEEDS ATTENTION[0m (%passed%/5 tests passed)
    echo.
    echo [31m[ERROR][0m Multiple issues detected
    echo [36m[INFO][0m Run troubleshoot-autorefresh.bat to diagnose problems
)

echo.
echo [36m🔧 QUICK ACTIONS:[0m
echo   - Test photo upload: Visit https://%DOMAIN%
echo   - Monitor status: Run monitor-autorefresh.bat
echo   - Troubleshoot: Run troubleshoot-autorefresh.bat
echo.

echo Test completed at: %date% %time%
pause
