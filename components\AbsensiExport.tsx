'use client';

import React, { useState } from 'react';
import { Trash2, Eye } from 'lucide-react';
import { formatIndonesiaDateTime } from '@/utils/dateUtils';
import { toast } from 'sonner';
import Link from 'next/link';

interface AbsensiData {
  id: string;
  nama: string;
  unit_kerja: string;
  tanda_tangan: string;
  waktu: string | Date;
  pelatihan: {
    id: string;
    nama: string;
    tempat: string;
    tgl_mulai: string | Date;
    tgl_berakhir: string | Date;
  };
}

interface Props {
  absensiList: AbsensiData[];
  filterPelatihan?: string;
  filterDate?: string;
  enableDelete?: boolean;
}

export default function AbsensiExport({ absensiList, filterPelatihan, filterDate, enableDelete }: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);
  // Filter data berdasarkan pelatihan dan tanggal
  let filteredData = absensiList;
  if (filterPelatihan) {
    filteredData = filteredData.filter(item => item.pelatihan.id === filterPelatihan);
  }
  if (filterDate) {
    filteredData = filteredData.filter(item => {
      // Konversi ke tanggal lokal Indonesia untuk konsistensi dengan filter server
      const waktu = new Date(item.waktu);
      const itemDate = waktu.toLocaleDateString('en-CA', {
        timeZone: 'Asia/Makassar',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }); // Format: YYYY-MM-DD
      return itemDate === filterDate;
    });
  }
  // Sort by nama
  filteredData = filteredData.slice().sort((a, b) => a.nama.localeCompare(b.nama, 'id', { sensitivity: 'base' }));

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedIds(new Set());
    } else {
      const allIds = filteredData.map(item => item.id);
      setSelectedIds(new Set(allIds));
    }
    setSelectAll(!selectAll);
  };

  // Handle individual item selection
  const handleSelectItem = (id: string) => {
    const newSelectedIds = new Set(selectedIds);
    if (newSelectedIds.has(id)) {
      newSelectedIds.delete(id);
    } else {
      newSelectedIds.add(id);
    }
    setSelectedIds(newSelectedIds);
    setSelectAll(newSelectedIds.size === filteredData.length && filteredData.length > 0);
  };
  // Handle export function
  const handleExport = async () => {
    setIsLoading(true);
    try {
      // Ambil nama pelatihan dari data terfilter
      let pelatihanName = filteredData[0]?.pelatihan?.nama || 'Daftar_Hadir';
      pelatihanName = pelatihanName.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_').replace(/^_+|_+$/g, '');
      let fileName = pelatihanName || 'Daftar_Hadir';
      // Tambahkan tanggal filter jika ada
      if (filterDate) {
        fileName += `_${filterDate}`;
      }
      
      // Kirim permintaan dengan data filter daripada IDs jika tidak ada yang dipilih
      const response = await fetch('/api/absensi/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(
          selectedIds.size > 0 
            ? { ids: Array.from(selectedIds) } 
            : { 
                pelatihanId: filterPelatihan || undefined,
                tanggal: filterDate || undefined
              }
        )
      });
      if (!response.ok) {
        throw new Error('Failed to export data');
      }
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fileName}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Terjadi kesalahan saat mengekspor data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fungsi untuk memicu toast konfirmasi
  const confirmDeleteAbsensi = (id: string) => {
    setPendingDeleteId(id);
    toast(
      <div>
        <div className="mb-1 font-medium">Konfirmasi Hapus</div>
        <div>Yakin ingin menghapus data absensi ini?</div>
        <div className="flex gap-2 mt-3">
          <button
            className="px-3 py-1 text-white bg-red-600 rounded hover:bg-red-700"
            onClick={async () => {
              await handleDeleteAbsensi(id);
              setPendingDeleteId(null);
              toast.dismiss();
            }}
          >
            Ya, hapus
          </button>
          <button
            className="px-3 py-1 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            onClick={() => {
              setPendingDeleteId(null);
              toast.dismiss();
            }}
          >
            Batal
          </button>
        </div>
      </div>,
      { duration: 6000 }
    );
  };
  // Perbaiki handler hapus agar tidak pakai confirm()
  const handleDeleteAbsensi = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/absensi/delete-multiple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: [id] })
      });
      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.message || 'Gagal menghapus data');
      toast.success('Data absensi berhasil dihapus');
      setTimeout(() => window.location.reload(), 1200);
    } catch (e: any) {
      toast.error(e?.message || 'Gagal menghapus data');
    } finally {
      setIsLoading(false);
    }
  };

  // Fungsi untuk bulk delete
  const confirmBulkDelete = () => {
    if (selectedIds.size === 0) {
      toast.error('Pilih setidaknya satu item untuk dihapus');
      return;
    }
    
    toast(
      <div>
        <div className="mb-1 font-medium">Konfirmasi Hapus Massal</div>
        <div>Yakin ingin menghapus {selectedIds.size} data absensi yang dipilih?</div>
        <div className="flex gap-2 mt-3">
          <button
            className="px-3 py-1 text-white bg-red-600 rounded hover:bg-red-700"
            onClick={async () => {
              await handleBulkDelete();
              toast.dismiss();
            }}
          >
            Ya, hapus
          </button>
          <button
            className="px-3 py-1 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            onClick={() => {
              toast.dismiss();
            }}
          >
            Batal
          </button>
        </div>
      </div>,
      { duration: 6000 }
    );
  };

  // Handler untuk bulk delete
  const handleBulkDelete = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/absensi/delete-multiple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: Array.from(selectedIds) })
      });
      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.message || 'Gagal menghapus data');
      toast.success(`${selectedIds.size} data absensi berhasil dihapus`);
      setSelectedIds(new Set());
      setSelectAll(false);
      setTimeout(() => window.location.reload(), 1200);
    } catch (e: any) {
      toast.error(e?.message || 'Gagal menghapus data');
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="p-4 mb-6 bg-white rounded-lg shadow">
      <h2 className="mb-4 text-lg font-medium">Export Daftar Hadir</h2>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <button
            onClick={handleExport}
            disabled={filteredData.length === 0 || isLoading}
            className="flex items-center px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <svg className="w-4 h-4 mr-2 -ml-1 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Memproses...
              </>
            ) : (
              'Export ke PDF'
            )}
          </button>
          
          {selectedIds.size > 0 && enableDelete && (
            <button
              onClick={confirmBulkDelete}
              disabled={isLoading}
              className="flex items-center px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Hapus {selectedIds.size} Terpilih
            </button>
          )}
        </div>
      </div>      <div className="mb-2 text-sm text-gray-500">
        {selectedIds.size > 0 ? (
          `Data yang akan diekspor: ${selectedIds.size} data terpilih`
        ) : (
          <>
            <span>Data yang ditampilkan: {filteredData.length} dari {absensiList.length} absensi</span>
            <div className="mt-1 italic">
              <span className="text-xs text-blue-600">
                * Saat export tanpa memilih data, semua data yang sesuai dengan filter (termasuk semua halaman) akan diekspor
              </span>
            </div>
          </>
        )}
      </div>
      {filteredData.length > 0 && (        <div className="mt-4 overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="w-12 px-3 py-3 text-center">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="text-blue-600 border-gray-300 rounded shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  />
                </th>
                <th scope="col" className="w-16 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  No
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Nama
                </th>                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Unit Kerja
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Kegiatan
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Waktu
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredData.map((item, index) => (
                <tr key={item.id} className={selectedIds.has(item.id) ? "bg-blue-50" : ""}>
                  <td className="px-3 py-4 text-center">
                    <input
                      type="checkbox"
                      checked={selectedIds.has(item.id)}
                      onChange={() => handleSelectItem(item.id)}
                      className="text-blue-600 border-gray-300 rounded shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                    />
                  </td>
                  <td className="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap">
                    {index + 1}
                  </td>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                    {item.nama}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                    {item.unit_kerja}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                    {item.pelatihan.nama}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                    {formatIndonesiaDateTime(new Date(item.waktu))}
                  </td>
                  <td className="px-6 py-4 text-sm text-center">
                    <div className="flex items-center justify-center gap-2">
                      <Link 
                        href={`/dashboard/absensi/${item.id}`}
                        className="flex items-center justify-center text-blue-600 hover:text-blue-900"
                        title="Lihat Detail"
                      >
                        <Eye size={18} />
                      </Link>
                      {enableDelete && (
                        <button
                          onClick={() => confirmDeleteAbsensi(item.id)}
                          className="flex items-center justify-center text-red-600 hover:text-red-900"
                          disabled={isLoading || pendingDeleteId === item.id}
                          title="Hapus"
                        >
                          <Trash2 size={18} />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
