# ImageKit Private Key Setup Guide

## Quick Setup Steps

### 1. Get Your ImageKit Private Key

1. **Login to ImageKit Dashboard**
   - Go to [ImageKit.io](https://imagekit.io)
   - Login to your account

2. **Navigate to API Keys**
   - Click on "Developer" in the sidebar
   - Select "API Keys"

3. **Copy Private Key**
   - Find the "Private Key" section
   - Click "Show" or "Copy" button
   - The key looks like: `private_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

### 2. Update Environment Variables

Open your `.env` file and replace:

```bash
# FROM:
IMAGEKIT_PRIVATE_KEY="private_key_here"

# TO:
IMAGEKIT_PRIVATE_KEY="private_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

### 3. Restart Development Server

```bash
npm run dev
```

## Testing the Upload

1. Visit: `http://localhost:3000/public-pages/biodata/[your-link]`
2. Try uploading a photo
3. Check the toast message to see which service was used:
   - "✅ Photo uploaded via ImageKit" (Primary)
   - "⚠️ Photo uploaded via Cloudinary backup" (Secondary)
   - "🔄 Photo uploaded to local storage" (Fallback)

## Troubleshooting

### If ImageKit fails:
- Check private key is correct
- Verify public key and URL endpoint
- System will automatically fall back to Cloudinary

### If Cloudinary fails:
- Update Cloudinary credentials in `.env`
- System will automatically fall back to local storage

### If everything fails:
- Check internet connection
- Verify file size (should be < 10MB)
- Check file format (JPG, PNG, GIF, WebP supported)

## Current Credentials Status

```bash
✅ IMAGEKIT_PUBLIC_KEY="public_R4MXJeyKQd/HU3B7soouEn9jzjY="
✅ IMAGEKIT_URL_ENDPOINT="https://ik.imagekit.io/6t2w09e6k" 
⚠️ IMAGEKIT_PRIVATE_KEY="private_key_here" # NEEDS UPDATE
```

Once you update the private key, the system will be fully functional!
