import { NextRequest, NextResponse } from 'next/server';
import { decrypt } from './lib/session';
import { middlewareLogger } from './utils/middlewareLogger';

// Daftar path yang memerlukan autentikasi
const protectedPaths = ['/dashboard'];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Jika path cocok dengan /dashboard/*, maka butuh autentikasi
  const isPathProtected = protectedPaths.some((path) => pathname.startsWith(path));
  
  // Jika bukan protected path, lanjutkan request
  if (!isPathProtected) {
    return NextResponse.next();
  }
  
  // Cek token dari cookie
  const sessionCookie = request.cookies.get('session')?.value;
  middlewareLogger.info('Memeriksa session untuk path', { 
    path: pathname, 
    hasSession: !!sessionCookie
  });
  
  if (!sessionCookie) {
    middlewareLogger.warning('Tidak ada session, redirect ke login', { 
      path: pathname
    });
    // Jika tidak ada session, redirect ke halaman login
    return NextResponse.redirect(new URL('/login', request.url));
  }

  try {
    // Verifikasi session token
    const session = await decrypt(sessionCookie);
    
    if (session && session.userId) {
      middlewareLogger.info('Session valid, melanjutkan ke dashboard', { 
        userId: String(session.userId)
      });
      return NextResponse.next();
    } else {
      middlewareLogger.warning('Session tidak valid (format tidak sesuai)', { 
        path: pathname
      });
    }
  } catch (error) {
    // Log error dengan lebih detail
    if (error instanceof Error) {
      middlewareLogger.error(
        `Session tidak valid: ${error.message}`, 
        error, 
        { path: pathname, errorCode: (error as any).code }
      );
    } else {
      middlewareLogger.error('Session tidak valid (unknown error)', new Error('Unknown error'), {
        path: pathname
      });
    }
  }
  
  // Hapus cookie session yang tidak valid
  const response = NextResponse.redirect(new URL('/login', request.url));
  response.cookies.delete('session');
  
  // Jika session invalid, redirect ke login
  return response;
}

export const config = {
  matcher: [
    '/dashboard/:path*',
  ],
};
