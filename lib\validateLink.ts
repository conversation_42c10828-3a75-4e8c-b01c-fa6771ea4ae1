import { prisma } from './prisma';
import { logger } from '@/utils/logger';
import { getIndonesiaLocalDate } from '@/utils/dateUtils';

export async function validateRegistrationLink(link: string) {
  try {
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { link_registrasi: link },
      include: {
        jenjangTargets: {
          select: {
            id: true,
            jenjang: true,
            target_peserta: true,
          },
        },
      }
    });
    
    if (!pelatihan) {
      return { valid: false, message: 'Link registrasi tidak valid.' };
    }
    
    const now = new Date();
    const tglBerakhir = new Date(pelatihan.tgl_berakhir);
    
    // Set end time to 21:00:00 of the last day of training
    const endOfLastDay = new Date(tglBerakhir);
    endOfLastDay.setHours(21, 0, 0, 0); // 21:00:00
   
    // Check if current time is after the deadline
    if (now > endOfLastDay) {
      return {
        valid: false,
        message: `Pendaftaran untuk kegiatan "${pelatihan.nama}" sudah ditutup karena melewati batas waktu. Kegiatan ini sudah berakhir.`
      };
    }
    
    return { valid: true, pelatihan };
  } catch (error) {
    logger.error('Error validating registration link:', error);
    return { valid: false, message: 'Terjadi kesalahan saat memvalidasi link.' };
  }
}

export async function validateAbsensiLink(link: string) {
  try {
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { link_absensi: link }
    });
    if (!pelatihan) {
      return { valid: false, message: 'Link absensi tidak valid.' };
    }
    
    // Get current time in Indonesia timezone
    const now = new Date();
    const nowInIndonesia = getIndonesiaLocalDate(now);
    if (!nowInIndonesia) {
      return { valid: false, message: 'Terjadi kesalahan dengan waktu sistem.' };
    }
    
    // Convert training dates to Indonesia timezone
    const tglMulai = getIndonesiaLocalDate(pelatihan.tgl_mulai);
    const tglBerakhir = getIndonesiaLocalDate(pelatihan.tgl_berakhir);
    
    if (!tglMulai || !tglBerakhir) {
      return { valid: false, message: 'Terjadi kesalahan dengan tanggal pelatihan.' };
    }
    
    // Create start time at 7:00 AM WITA on the training start day
    const startTime = new Date(
      tglMulai.getFullYear(),
      tglMulai.getMonth(),
      tglMulai.getDate(),
      7, 0, 0, 0
    );
    
    // Create end time at 21:00 WITA on the last day of training
    const endTime = new Date(
      tglBerakhir.getFullYear(),
      tglBerakhir.getMonth(),
      tglBerakhir.getDate(),
      21, 0, 0, 0 // 21:00:00
    );
    
    // Debug logging
    logger.info('Time validation check for absensi link', { 
      nowInIndonesia: nowInIndonesia.toISOString(),
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString()
    });
   
    // Absensi hanya bisa dilakukan mulai jam 7 pagi WITA pada hari pelatihan dimulai
    if (nowInIndonesia < startTime) {
      return { valid: false, message: `Absensi "${pelatihan.nama}" belum dibuka. Absensi akan dibuka pada pukul 07:00 WITA pada tanggal mulai pelatihan.` };
    }
   
    if (nowInIndonesia > endTime) {
      return {
        valid: false,
        message: `Absensi untuk kegiatan "${pelatihan.nama}" sudah ditutup karena melewati batas waktu. Kegiatan ini sudah berakhir.`
      };
    }
      return { valid: true, pelatihan };
  } catch (error) {
    logger.error('Error validating absensi link:', error);
    return { valid: false, message: 'Terjadi kesalahan saat memvalidasi link.' };
  }
}

export async function validateAbsensiInternalLink(link: string) {
  try {
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { link_absensi_internal: link }
    });
    if (!pelatihan) {
      return { valid: false, message: 'Link absensi internal tidak valid.' };
    }
    
    // Get current time in Indonesia timezone
    const now = new Date();
    const nowInIndonesia = getIndonesiaLocalDate(now);
    if (!nowInIndonesia) {
      return { valid: false, message: 'Terjadi kesalahan dengan waktu sistem.' };
    }
    
    // Convert training dates to Indonesia timezone
    const tglMulai = getIndonesiaLocalDate(pelatihan.tgl_mulai);
    const tglBerakhir = getIndonesiaLocalDate(pelatihan.tgl_berakhir);
    
    if (!tglMulai || !tglBerakhir) {
      return { valid: false, message: 'Terjadi kesalahan dengan tanggal pelatihan.' };
    }
    
    // Create start time at 7:00 AM WITA on the training start day
    const startTime = new Date(
      tglMulai.getFullYear(),
      tglMulai.getMonth(),
      tglMulai.getDate(),
      7, 0, 0, 0
    );
    
    // Create end time at 21:00 WITA on the last day of training
    const endTime = new Date(
      tglBerakhir.getFullYear(),
      tglBerakhir.getMonth(),
      tglBerakhir.getDate(),
      21, 0, 0, 0 // 21:00:00
    );
    
    // Debug logging
    logger.info('Time validation check for internal absensi link', { 
      nowInIndonesia: nowInIndonesia.toISOString(),
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString()
    });
   
    // Absensi hanya bisa dilakukan mulai jam 7 pagi WITA pada hari pelatihan dimulai
    if (nowInIndonesia < startTime) {
      return { valid: false, message: `Absensi internal "${pelatihan.nama}" belum dibuka. Absensi akan dibuka pada pukul 07:00 WITA pada tanggal mulai pelatihan.` };
    }
   
    if (nowInIndonesia > endTime) {
      return {
        valid: false,
        message: `Absensi  untuk kegiatan "${pelatihan.nama}" sudah ditutup karena melewati batas waktu. Kegiatan ini sudah berakhir.`
      };
    }
    
    return { valid: true, pelatihan };
  } catch (error) {
    logger.error('Error validating internal absensi link:', error);
    return { valid: false, message: 'Terjadi kesalahan saat memvalidasi link.' };
  }
}

export async function validateAbsensiEksternalLink(link: string) {
  try {
    const pelatihan = await prisma.pelatihan.findUnique({
      where: { link_absensi_eksternal: link }
    });
    if (!pelatihan) {
      return { valid: false, message: 'Link absensi eksternal tidak valid.' };
    }
    
    // Get current time in Indonesia timezone
    const now = new Date();
    const nowInIndonesia = getIndonesiaLocalDate(now);
    if (!nowInIndonesia) {
      return { valid: false, message: 'Terjadi kesalahan dengan waktu sistem.' };
    }
    
    // Convert training dates to Indonesia timezone
    const tglMulai = getIndonesiaLocalDate(pelatihan.tgl_mulai);
    const tglBerakhir = getIndonesiaLocalDate(pelatihan.tgl_berakhir);
    
    if (!tglMulai || !tglBerakhir) {
      return { valid: false, message: 'Terjadi kesalahan dengan tanggal pelatihan.' };
    }
    
    // Create start time at 7:00 AM WITA on the training start day
    const startTime = new Date(
      tglMulai.getFullYear(),
      tglMulai.getMonth(),
      tglMulai.getDate(),
      7, 0, 0, 0
    );
    
    // Create end time at 21:00 WITA on the last day of training
    const endTime = new Date(
      tglBerakhir.getFullYear(),
      tglBerakhir.getMonth(),
      tglBerakhir.getDate(),
      21, 0, 0, 0 // 21:00:00
    );
    
    // Debug logging
    logger.info('Time validation check for external absensi link', { 
      nowInIndonesia: nowInIndonesia.toISOString(),
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString()
    });
   
    // Absensi hanya bisa dilakukan mulai jam 7 pagi WITA pada hari pelatihan dimulai
    if (nowInIndonesia < startTime) {
      return { valid: false, message: `Absensi eksternal "${pelatihan.nama}" belum dibuka. Absensi akan dibuka pada pukul 07:00 WITA pada tanggal mulai pelatihan.` };
    }
   
    if (nowInIndonesia > endTime) {
      return {
        valid: false,
        message: `Absensi  untuk kegiatan "${pelatihan.nama}" sudah ditutup karena melewati batas waktu. Kegiatan ini sudah berakhir.`
      };
    }
    
    return { valid: true, pelatihan };
  } catch (error) {
    logger.error('Error validating external absensi link:', error);
    return { valid: false, message: 'Terjadi kesalahan saat memvalidasi link.' };
  }
}
