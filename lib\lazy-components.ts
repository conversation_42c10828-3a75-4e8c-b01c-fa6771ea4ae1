import dynamic from 'next/dynamic';

// Simple lazy loading components for optimization
export const LazyPhotoCapture = dynamic(() => import('../components/PhotoCapture'), {
  ssr: false,
});

export const LazyMapPicker = dynamic(() => import('../components/SimpleMapPicker'), {
  ssr: false,
});

// Preload function for critical components
export const preloadCriticalComponents = () => {
  if (typeof window !== 'undefined') {
    requestIdleCallback(() => {
      import('../components/PhotoCapture');
      import('../components/SimpleMapPicker');
    });
  }
};
