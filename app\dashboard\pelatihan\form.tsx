'use client';

import { useRouter } from 'next/navigation';
import usePelatihanForm from './hooks/usePelatihanForm';
import JenjangTargetInput from './components/JenjangTargetInput';
import MaterialUploaderAlt, { MaterialFile } from '@/components/MaterialUploaderAlt';
import { useState, useEffect } from 'react';
import { logger } from '@/utils/logger';

interface JenjangTarget {
  id: string;
  jenjang: string;
  target_peserta: number;
}

interface Pelatihan {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: string;
  tgl_berakhir: string;
  jenjang: string;
  target_peserta: number;
  jenjangTargets?: JenjangTarget[];
  link_registrasi: string;
  link_absensi: string;
  createdAt?: Date;
  updatedAt?: Date;
  userId?: string;
}

interface PelatihanFormProps {
  pelatihan?: Pelatihan;
}

export default function PelatihanForm({ pelatihan }: PelatihanFormProps) {
  const router = useRouter();
  const [materiFiles, setMateriFiles] = useState<MaterialFile[]>([]);
    const {
    form,
    isSubmitting,
    formError,
    clientReady,
    handleChange,
    handleJenjangTargetChange,
    addJenjangTarget,
    removeJenjangTarget,
    submitFormManually,
    jenjangTargets
  } = usePelatihanForm({ pelatihan });
  
  // Watch form values untuk reactive updates
  const formValues = form.watch();
  
  // Fetch materi files jika sedang edit pelatihan
  useEffect(() => {
    if (pelatihan?.id && clientReady) {
      fetchMateriFiles(pelatihan.id);
    }
  }, [pelatihan?.id, clientReady]);
  
  // Fungsi untuk fetch materi files dari API
  const fetchMateriFiles = async (pelatihanId: string) => {
    try {
      const response = await fetch(`/api/pelatihan/${pelatihanId}/materi`);
      const result = await response.json();
      
      if (result.success && result.data) {
        setMateriFiles(result.data);
      }
    } catch (error) {
      logger.error('Error fetching materi files', error instanceof Error ? error : new Error(String(error)));
    }
  };
  
  // Handler untuk update state materiFiles ketika ada perubahan
  const handleMateriFilesChange = (files: MaterialFile[]) => {
    setMateriFiles(files);
  };    // Implementasi baru untuk fungsi onSubmit yang lebih bersih
  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
      try {
      // Dapatkan nilai dari form
      const values = form.getValues();
      
      // Import timezone conversion function
      const { convertFormDateToIndonesia } = await import('@/utils/dateUtils');
      
      // Pastikan target_peserta adalah angka (bukan string)
      // Dan konversi tanggal ke timezone Indonesia sebelum submit
      const processedValues = {
        ...values,
        // Konversi tanggal ke timezone Indonesia sebelum dikirim ke backend
        tgl_mulai: convertFormDateToIndonesia(values.tgl_mulai).toISOString().split('T')[0],
        tgl_berakhir: convertFormDateToIndonesia(values.tgl_berakhir).toISOString().split('T')[0],        jenjangTargets: values.jenjangTargets.map(jt => ({
          ...jt,
          target_peserta: Number(jt.target_peserta) // Konversi ke number
        }))
      };
      
      // Validasi manual untuk tanggal
      const tglMulai = new Date(processedValues.tgl_mulai);
      const tglBerakhir = new Date(processedValues.tgl_berakhir);
        if (tglMulai > tglBerakhir) {
        form.setError('tgl_berakhir', {
          type: 'manual', 
          message: 'Tanggal berakhir tidak boleh sebelum tanggal mulai' 
        });
        return;
      }        // Validasi manual jenjang dan target
      if (!processedValues.jenjangTargets || processedValues.jenjangTargets.length === 0) {
        return;
      }
      
      
      // Set nilai form yang sudah diproses kembali ke form
      processedValues.jenjangTargets.forEach((jt, index) => {
        form.setValue(`jenjangTargets.${index}.target_peserta`, Number(jt.target_peserta));
      });
        // Update form values dengan tanggal yang sudah dikonversi
      form.setValue('tgl_mulai', processedValues.tgl_mulai);
      form.setValue('tgl_berakhir', processedValues.tgl_berakhir);
      
      // Jalankan submit form secara langsung
      if (typeof submitFormManually === 'function') {
        await submitFormManually();
      }    } catch (error) {
      console.error("Error dalam onSubmit:", error);
    }
  };
  
  // Tampilkan loading spinner selama menunggu client-side hydration
  if (!clientReady) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="w-8 h-8 border-t-2 border-b-2 border-gray-900 rounded-full animate-spin"></div>      </div>
    );
  }

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      {formError && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">
          {formError}
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Nama Pelatihan */}
        <div>
          <label htmlFor="nama" className="block text-sm font-medium text-gray-700">
            Nama Kegiatan
          </label>
          <input
            type="text"
            id="nama"
            name="nama"
            value={formValues.nama}
            onChange={handleChange}
            required
            className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>

        {/* Tempat */}
        <div>
          <label htmlFor="tempat" className="block text-sm font-medium text-gray-700">
            Tempat
          </label>
          <input
            type="text"
            id="tempat"
            name="tempat"
            value={formValues.tempat}
            onChange={handleChange}
            required
            className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>

        {/* Tanggal Mulai */}
        <div>
          <label htmlFor="tgl_mulai" className="block text-sm font-medium text-gray-700">
            Tanggal Mulai
          </label>
          <input
            type="date"
            id="tgl_mulai"
            name="tgl_mulai"
            value={formValues.tgl_mulai}
            onChange={handleChange}
            required
            className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>

        {/* Tanggal Berakhir */}
        <div>
          <label htmlFor="tgl_berakhir" className="block text-sm font-medium text-gray-700">
            Tanggal Berakhir
          </label>
          <input
            type="date"
            id="tgl_berakhir"
            name="tgl_berakhir"
            value={formValues.tgl_berakhir}
            onChange={handleChange}
            required
            className="block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>
      </div>

      {/* Pilihan Peserta Kegiatan */}
      <div className="mt-4">
        <label className="block mb-1 text-sm font-medium text-gray-700">Peserta Kegiatan</label>
        <div className="flex flex-col gap-2 sm:flex-row">
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="peserta_kegiatan"
              value="internal"
              checked={formValues.peserta_kegiatan === 'internal'}
              onChange={handleChange}
              className="text-green-600 form-radio"
            />
            <span className="ml-2">Internal</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="peserta_kegiatan"
              value="eksternal"
              checked={formValues.peserta_kegiatan === 'eksternal'}
              onChange={handleChange}
              className="text-green-600 form-radio"
            />
            <span className="ml-2">Eksternal</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="peserta_kegiatan"
              value="keduanya"
              checked={formValues.peserta_kegiatan === 'keduanya'}
              onChange={handleChange}
              className="text-green-600 form-radio"
            />
            <span className="ml-2">Keduanya</span>
          </label>
        </div>
      </div>

      {/* Jenjang and Target Peserta section */}
      <div className="mt-8">
        <h3 className="text-lg font-medium text-gray-900">Jenjang dan Target Peserta</h3>
        <p className="text-sm text-gray-500">Tambahkan jenjang pendidikan dan jumlah target peserta</p>
        
        {jenjangTargets.map((jenjangTarget, index) => (
          <JenjangTargetInput
            key={jenjangTarget.id}
            id={jenjangTarget.id}
            jenjang={jenjangTarget.jenjang}
            target_peserta={jenjangTarget.target_peserta}
            isRequired={index === 0}
            canRemove={jenjangTargets.length > 1}
            onChangeJenjang={(id, value) => handleJenjangTargetChange(id, 'jenjang', value)}
            onChangeTarget={(id, value) => handleJenjangTargetChange(id, 'target_peserta', value)}
            onRemove={removeJenjangTarget}
          />
        ))}
        
        {/* Add More Jenjang Button */}
        <div className="flex justify-center mt-4">
          <button
            type="button"
            onClick={addJenjangTarget}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Tambah Jenjang
          </button>
        </div>
      </div>
      
      {/* Material Uploader - only show if we have a pelatihan ID */}
      {pelatihan?.id ? (
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900">Materi Kegiatan</h3>
          <p className="mb-4 text-sm text-gray-500">Unggah materi kegiatan dalam bentuk file PDF (maksimal 2MB per file)</p>
          
          <MaterialUploaderAlt 
            pelatihanId={pelatihan.id}
            initialFiles={materiFiles}
            onChange={handleMateriFilesChange}
          />
        </div>
      ) : (
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900">Materi Pelatihan</h3>
          <p className="mb-4 text-sm text-gray-500">
            Anda dapat mengunggah materi kegiatan setelah data kegiatan telah disimpan
          </p>
        </div>
      )}

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={() => router.back()}
          className="px-4 py-2 mr-4 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Batal
        </button>        <button          type="submit"
          disabled={isSubmitting}
          className={`inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
            isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isSubmitting ? (
            <>
              <svg className="w-5 h-5 mr-3 -ml-1 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Menyimpan...
            </>
          ) : (
            'Simpan Kegiatan'
          )}
        </button>
      </div>
    </form>
  );
}
