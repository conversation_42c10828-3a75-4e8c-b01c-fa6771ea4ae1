# 🚨 SOLUSI CEPAT - PHOTO UPLOAD 404 ERROR

## 🎯 MASALAH TERIDENTIFIKASI

**URL yang tidak bisa diakses:**
```
https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/915b8bfd-14ad-48d0-805f-6509f9540dd3/absensi-2025-06-07T08-48-47-545Z-db7abc29-1921-46dd-8fa8-d1a6f83ec1a0.jpg
```

**Penyebab:**
- File tersimpan di: `public/public/uploads/absensi/photos/` (duplikasi folder)
- URL mencari di: `/uploads/absensi/photos/` (tanpa prefix public)
- Nginx tidak dapat menemukan file karena mismatch path

## 🛠️ SOLUSI CEPAT (5 MENIT)

### **Opsi 1: Upload Manual + Jalankan Script**

#### Step 1: Upload Files ke VPS
```cmd
cd f:\online\zoom_rutin
scp diagnosa-photo-upload.sh <EMAIL>:/home/<USER>/
scp fix-photo-final.sh <EMAIL>:/home/<USER>/
```

#### Step 2: SSH ke VPS dan Jalankan Diagnosa
```bash
ssh <EMAIL>
chmod +x /home/<USER>/diagnosa-photo-upload.sh
chmod +x /home/<USER>/fix-photo-final.sh

# Edit path aplikasi di script
nano /home/<USER>/fix-photo-final.sh
# Ganti: APP_DIR="/home/<USER>/deployment" 
# Dengan path yang sesuai di VPS Anda

# Jalankan diagnosa
./diagnosa-photo-upload.sh
```

#### Step 3: Jalankan Perbaikan
```bash
sudo ./fix-photo-final.sh
```

### **Opsi 2: Manual Fix (Jika Anda Tahu Path Aplikasi)**

```bash
# SSH ke VPS
ssh <EMAIL>

# Ganti dengan path aplikasi Anda
APP_DIR="/path/to/your/nextjs/app"

# 1. Cek masalah duplikasi
ls -la $APP_DIR/public/

# 2. Jika ada folder public/public, pindahkan isinya
if [ -d "$APP_DIR/public/public/uploads" ]; then
    mkdir -p $APP_DIR/public/uploads
    cp -r $APP_DIR/public/public/uploads/* $APP_DIR/public/uploads/
    rm -rf $APP_DIR/public/public
fi

# 3. Set permissions
sudo chown -R www-data:www-data $APP_DIR/public/uploads
sudo chmod -R 755 $APP_DIR/public/uploads

# 4. Update nginx config
sudo nano /etc/nginx/sites-available/default
```

**Tambahkan ke nginx config:**
```nginx
# Static file serving for uploads
location /uploads/ {
    root /path/to/your/nextjs/app/public;
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri $uri/ =404;
}
```

```bash
# 5. Test dan reload nginx
sudo nginx -t
sudo systemctl reload nginx
```

## 🧪 VERIFIKASI HASIL

### Test 1: Cek File Ada
```bash
# Ganti dengan path aplikasi Anda
find /path/to/your/app/public/uploads -name "*.jpg" | head -5
```

### Test 2: Test Direct URL
```bash
curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/915b8bfd-14ad-48d0-805f-6509f9540dd3/absensi-2025-06-07T08-48-47-545Z-db7abc29-1921-46dd-8fa8-d1a6f83ec1a0.jpg
```
**Expected result:** `HTTP/1.1 200 OK`

### Test 3: Upload Foto Baru
1. Buka: https://kegiatan.bpmpkaltim.id/public-pages/absensi/internal/[link]
2. Ambil foto
3. Submit form
4. Cek admin panel → Detail absensi
5. **Expected:** Foto muncul tanpa error

## 🚨 TROUBLESHOOTING

### Jika masih 404:
```bash
# Cek nginx logs
sudo tail -f /var/log/nginx/error.log

# Cek aplikasi logs
pm2 logs

# Cek permissions
ls -la /path/to/your/app/public/uploads/
```

### Jika nginx error:
```bash
# Test config
sudo nginx -t

# Restart nginx
sudo systemctl restart nginx
```

### Jika upload gagal:
```bash
# Cek disk space
df -h

# Cek permissions
ls -la /path/to/your/app/public/uploads/
```

## ⏱️ ESTIMASI WAKTU

- **Manual fix**: 5-10 menit
- **Script otomatis**: 3-5 menit
- **Total downtime**: < 2 menit

## 📞 BANTUAN DARURAT

Jika masih bermasalah, jalankan commands ini untuk restore:
```bash
# Restore nginx config (jika ada backup)
sudo cp /etc/nginx/sites-available/default.backup /etc/nginx/sites-available/default
sudo systemctl reload nginx

# Restart aplikasi
pm2 restart all
```

---
**Prioritas:** Gunakan **Script Otomatis** (fix-photo-final.sh) untuk hasil terbaik.
