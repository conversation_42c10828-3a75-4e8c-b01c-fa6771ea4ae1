{"name": "pelatihan-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"prisma:generate": "prisma generate", "dev": "next dev", "build": "next build", "build:analyze": "cross-env ANALYZE=true next build", "start": "NODE_ENV=production node .next/standalone/server.js", "lint": "next lint", "fix-deployment": "node fix-deployment-paths.mjs"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "tsx prisma/seed.ts"}, "compilerOptions": {"moduleResolution": "node"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.6.0", "@headlessui/react": "^2.2.2", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.9.0", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@types/pdfmake": "^0.2.11", "bcryptjs": "^3.0.2", "blob-stream": "^0.1.3", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "imagekit": "^6.0.0", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.501.0", "multer": "^2.0.1", "next": "^15.3.1", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "pdfkit": "^0.16.0", "pdfmake": "^0.2.18", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-qr-code": "^2.0.15", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "web-vitals": "^5.0.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.14", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/blob-stream": "^0.1.33", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.18", "@types/node": "^20.17.24", "@types/pdfkit": "^0.13.9", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.2", "nock": "^14.0.1", "postcss": "^8.5.3", "prisma": "^6.9.0", "tailwindcss": "^4.0.14", "ts-node": "^10.9.2", "tsx": "^4.19.3", "tw-animate-css": "^1.2.8", "typescript": "^5.8.2"}}