'use client';

import { Plus, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';

type QuestionTypeOptionsProps = {
  question: {
    id: string;
    questionType: string;
    options?: any;
    validationRules?: any;
  };
  onChange: (field: string, value: any) => void;
};

export function QuestionTypeOptions({ question, onChange }: QuestionTypeOptionsProps) {
  // Initialize options if they don't exist
  const options = question.options || {};
  const validationRules = question.validationRules || {};

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = { ...options };
    if (!newOptions.choices) {
      newOptions.choices = [];
    }

    newOptions.choices[index] = value;
    onChange('options', newOptions);
  };

  const handleAddOption = () => {
    const newOptions = { ...options };
    if (!newOptions.choices) {
      newOptions.choices = [];
    }

    newOptions.choices.push('');
    onChange('options', newOptions);
  };

  const handleRemoveOption = (index: number) => {
    const newOptions = { ...options };
    if (!newOptions.choices) {
      return;
    }

    newOptions.choices.splice(index, 1);
    onChange('options', newOptions);
  };

  const handleValidationChange = (field: string, value: any) => {
    const newValidationRules = { ...validationRules, [field]: value };
    onChange('validationRules', newValidationRules);
  };

  // Render different options based on question type
  switch (question.questionType) {
    case 'SHORT_TEXT':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Input placeholder="Teks pendek" disabled />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="min-length">Panjang Minimum</Label>
              <Input
                id="min-length"
                type="number"
                min="0"
                value={validationRules.minLength || ''}
                onChange={(e) => handleValidationChange('minLength', e.target.value ? parseInt(e.target.value) : '')}
                placeholder="0"
              />
            </div>
            <div>
              <Label htmlFor="max-length">Panjang Maksimum</Label>
              <Input
                id="max-length"
                type="number"
                min="0"
                value={validationRules.maxLength || ''}
                onChange={(e) => handleValidationChange('maxLength', e.target.value ? parseInt(e.target.value) : '')}
                placeholder="255"
              />
            </div>
          </div>
        </div>
      );

    case 'LONG_TEXT':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Textarea placeholder="Teks panjang" disabled />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="min-length">Panjang Minimum</Label>
              <Input
                id="min-length"
                type="number"
                min="0"
                value={validationRules.minLength || ''}
                onChange={(e) => handleValidationChange('minLength', e.target.value ? parseInt(e.target.value) : '')}
                placeholder="0"
              />
            </div>
            <div>
              <Label htmlFor="max-length">Panjang Maksimum</Label>
              <Input
                id="max-length"
                type="number"
                min="0"
                value={validationRules.maxLength || ''}
                onChange={(e) => handleValidationChange('maxLength', e.target.value ? parseInt(e.target.value) : '')}
                placeholder="1000"
              />
            </div>
          </div>
        </div>
      );

    case 'SINGLE_CHOICE':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pilihan</Label>
            <div className="mt-2 space-y-2">
              {options.choices && options.choices.length > 0 ? (
                <RadioGroup value="option-1" disabled>
                  {options.choices.map((choice: string, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="flex items-center flex-1 space-x-2">
                        <RadioGroupItem value={`option-${index + 1}`} id={`option-${index + 1}`} />
                        <Input
                          value={choice}
                          onChange={(e) => handleOptionChange(index, e.target.value)}
                          placeholder={`Pilihan ${index + 1}`}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveOption(index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </RadioGroup>
              ) : (
                <p className="text-sm text-gray-500">Belum ada pilihan. Tambahkan pilihan pertama Anda.</p>
              )}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddOption}
                className="mt-2"
              >
                <Plus className="w-4 h-4 mr-1" />
                Tambah Pilihan
              </Button>
            </div>
          </div>
        </div>
      );

    case 'MULTIPLE_CHOICE':
    case 'CHECKLIST':
      return (
        <div className="space-y-4">
          <div>
            <Label>{question.questionType === 'CHECKLIST' ? 'Item Ceklist' : 'Pilihan'}</Label>
            <div className="mt-2 space-y-2">
              {options.choices && options.choices.length > 0 ? (
                <div className="space-y-2">
                  {options.choices.map((choice: string, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="flex items-center flex-1 space-x-2">
                        <Checkbox id={`option-${index + 1}`} disabled />
                        <Input
                          value={choice}
                          onChange={(e) => handleOptionChange(index, e.target.value)}
                          placeholder={question.questionType === 'CHECKLIST' ? `Item ${index + 1}` : `Pilihan ${index + 1}`}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveOption(index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">Belum ada {question.questionType === 'CHECKLIST' ? 'item' : 'pilihan'}. Tambahkan {question.questionType === 'CHECKLIST' ? 'item' : 'pilihan'} pertama Anda.</p>
              )}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddOption}
                className="mt-2"
              >
                <Plus className="w-4 h-4 mr-1" />
                Tambah {question.questionType === 'CHECKLIST' ? 'Item' : 'Pilihan'}
              </Button>
            </div>
          </div>
        </div>
      );

    case 'DROPDOWN':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pilihan Dropdown</Label>
            <div className="mt-2 space-y-2">
              {options.choices && options.choices.length > 0 ? (
                <div className="space-y-2">
                  {options.choices.map((choice: string, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="flex items-center flex-1 space-x-2">
                        <span className="text-sm text-gray-500">{index + 1}.</span>
                        <Input
                          value={choice}
                          onChange={(e) => handleOptionChange(index, e.target.value)}
                          placeholder={`Pilihan ${index + 1}`}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveOption(index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">Belum ada pilihan. Tambahkan pilihan pertama Anda.</p>
              )}
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddOption}
                className="mt-2"
              >
                <Plus className="w-4 h-4 mr-1" />
                Tambah Pilihan
              </Button>
            </div>
          </div>
        </div>
      );

    case 'NUMBER':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Input type="number" placeholder="0" disabled />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="min-value">Nilai Minimum</Label>
              <Input
                id="min-value"
                type="number"
                value={validationRules.min || ''}
                onChange={(e) => handleValidationChange('min', e.target.value ? parseFloat(e.target.value) : '')}
                placeholder="0"
              />
            </div>
            <div>
              <Label htmlFor="max-value">Nilai Maksimum</Label>
              <Input
                id="max-value"
                type="number"
                value={validationRules.max || ''}
                onChange={(e) => handleValidationChange('max', e.target.value ? parseFloat(e.target.value) : '')}
                placeholder="100"
              />
            </div>
          </div>
        </div>
      );

    case 'DATE':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Input type="date" disabled />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="min-date">Tanggal Minimum</Label>
              <Input
                id="min-date"
                type="date"
                value={validationRules.minDate || ''}
                onChange={(e) => handleValidationChange('minDate', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="max-date">Tanggal Maksimum</Label>
              <Input
                id="max-date"
                type="date"
                value={validationRules.maxDate || ''}
                onChange={(e) => handleValidationChange('maxDate', e.target.value)}
              />
            </div>
          </div>
        </div>
      );

    case 'TIME':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Input type="time" disabled />
          </div>
        </div>
      );

    case 'EMAIL':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Input type="email" placeholder="<EMAIL>" disabled />
          </div>
        </div>
      );

    case 'PHONE':
      return (
        <div className="space-y-4">
          <div>
            <Label>Pratinjau</Label>
            <Input type="tel" placeholder="+62 812 3456 7890" disabled />
          </div>
        </div>
      );

    default:
      return null;
  }
}
