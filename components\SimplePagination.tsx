'use client';

import React from 'react';

interface SimplePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalCount?: number;
  pageSize?: number;
}

/**
 * Komponen paginasi sederhana untuk client-side pagination
 */
export default function SimplePagination({
  currentPage,
  totalPages,
  onPageChange,
  totalCount,
  pageSize
}: SimplePaginationProps) {
  // Calculate totalPages if totalCount and pageSize are provided
  let calculatedTotalPages = totalPages;
  if (totalCount !== undefined && pageSize !== undefined) {
    calculatedTotalPages = Math.ceil(totalCount / pageSize);
  }

  if (calculatedTotalPages <= 1) return null;

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= calculatedTotalPages) {
      onPageChange(page);
    }
  };

  // Tampilkan maksimal 5 halaman dengan ellipsis jika diperlukan
  const maxPagesToShow = 5;
  let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
  const endPage = Math.min(calculatedTotalPages, startPage + maxPagesToShow - 1);
  
  // Sesuaikan jika di akhir rentang
  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }
  
  const pageNumbers: (number | string)[] = [];
  
  // Tambahkan halaman pertama jika belum termasuk
  if (startPage > 1) {
    pageNumbers.push(1);
    if (startPage > 2) {
      pageNumbers.push('...');
    }
  }
  
  // Tambahkan nomor halaman yang terlihat
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }
  
  // Tambahkan halaman terakhir jika belum termasuk
  if (endPage < calculatedTotalPages) {
    if (endPage < calculatedTotalPages - 1) {
      pageNumbers.push('...');
    }
    pageNumbers.push(calculatedTotalPages);
  }

  return (
    <div className="flex items-center justify-center mt-4">
      <nav className="flex items-center space-x-2" aria-label="Pagination">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded-md ${
            currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          aria-label="Halaman sebelumnya"
        >
          Prev
        </button>
        
        {pageNumbers.map((pageNum, index) => {
          if (pageNum === '...') {
            return (
              <span 
                key={`ellipsis-${index}`} 
                className="px-2" 
                aria-hidden="true"
              >
                ...
              </span>
            );
          }
          
          const page = pageNum as number;
          return (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 rounded-md ${
                page === currentPage
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              aria-current={page === currentPage ? 'page' : undefined}
              aria-label={`Halaman ${page}`}
            >
              {page}
            </button>
          );
        })}
        
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === calculatedTotalPages}
          className={`px-3 py-1 rounded-md ${
            currentPage === calculatedTotalPages
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          aria-label="Halaman berikutnya"
        >
          Next
        </button>
      </nav>
    </div>
  );
}