/**
 * Utilitas untuk menangani tanggal dengan konsisten di seluruh aplikasi
 * Menggunakan zona waktu Indonesia (WITA = UTC+8)
 */

// Konstanta zona waktu Indonesia
export const INDONESIA_TIMEZONE = 'Asia/Makassar'; // WITA
export const INDONESIA_TIMEZONE_OFFSET = 8; // UTC+8 hours

/**
 * Mendapatkan tanggal lokal Indonesia dari tanggal UTC
 * Tidak menambah offset manual, gunakan Date asli dan format dengan timeZone Asia/Makassar
 */
export function getIndonesiaLocalDate(date: Date | string | null | undefined): Date | null {
  if (!date) return null;
  const utcDate = new Date(date);
  if (isNaN(utcDate.getTime())) {
    return null;
  }
  // Return Date object apa adanya, formatting dilakukan saat render
  return utcDate;
}

/**
 * Format tanggal ke string lokal Indonesia
 */
export function formatIndonesiaDate(date: Date | string | null | undefined): string {
  const localDate = getIndonesiaLocalDate(date);
  if (!localDate) return '-';
  
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  };
  
  return localDate.toLocaleDateString('id-ID', options);
}

/**
 * Format tanggal lengkap dengan hari ke string lokal Indonesia
 */
export function formatIndonesiaFullDate(date: Date | string | null | undefined): string {
  const localDate = getIndonesiaLocalDate(date);
  if (!localDate) return '-';
  
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  };
  
  return localDate.toLocaleDateString('id-ID', options);
}

/**
 * Format tanggal dan waktu ke string lokal Indonesia
 */
export function formatIndonesiaDateTime(date: Date | string | null | undefined): string {
  if (!date) return '-';
  const d = new Date(date);
  if (isNaN(d.getTime())) return '-';
  // Format dengan zona waktu Asia/Makassar
  return d.toLocaleString('id-ID', {
    timeZone: 'Asia/Makassar',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
}

/**
 * Format tanggal singkat (DD/MM/YYYY)
 */
export function formatIndonesiaShortDate(date: Date | string | null | undefined): string {
  const localDate = getIndonesiaLocalDate(date);
  if (!localDate) return '-';
  
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  };
  
  return localDate.toLocaleDateString('id-ID', options);
}

/**
 * Mendapatkan tanggal UTC dari tanggal lokal
 * Berguna untuk menyimpan tanggal ke database
 */
export function getUTCDateFromLocal(localDate: Date): Date {
  return new Date(localDate.getTime() - (INDONESIA_TIMEZONE_OFFSET * 60 * 60 * 1000));
}

/**
 * Normalisasi tanggal ke UTC midnight
 * Berguna untuk perbandingan tanggal tanpa waktu
 */
export function normalizeToUTCMidnight(date: Date | string): Date {
  const d = new Date(date);
  return new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
}

/**
 * Mendapatkan rentang satu hari penuh dalam UTC
 * Berguna untuk query database berdasarkan tanggal
 */
export function getUTCDayRange(date: Date | string): { start: Date, end: Date } {
  const d = new Date(date);
  
  const start = new Date(Date.UTC(
    d.getFullYear(),
    d.getMonth(),
    d.getDate(),
    0, 0, 0, 0
  ));
  
  const end = new Date(Date.UTC(
    d.getFullYear(),
    d.getMonth(),
    d.getDate(),
    23, 59, 59, 999
  ));
  
  return { start, end };
}

/**
 * Mendapatkan rentang satu hari penuh dalam timezone Indonesia
 * Berguna untuk query database berdasarkan tanggal dalam timezone lokal
 */
export function getIndonesiaDayRange(date: Date | string): { start: Date, end: Date } {
  // Pastikan input date diinterpretasi sebagai tanggal di Asia/Makassar
  const d = typeof date === 'string' ? new Date(date) : date;

  // Ambil komponen tanggal di Asia/Makassar
  const witaDate = new Date(d.toLocaleString('en-US', { timeZone: 'Asia/Makassar' }));
  const year = witaDate.getFullYear();
  const month = witaDate.getMonth();
  const day = witaDate.getDate();

  // Buat jam 00:00:00 dan 23:59:59 di Asia/Makassar
  const startWITA = new Date(Date.UTC(year, month, day, 0, 0, 0));
  const endWITA = new Date(Date.UTC(year, month, day, 23, 59, 59, 999));

  // Koreksi offset Asia/Makassar (UTC+8)
  const start = new Date(startWITA.getTime() - (8 * 60 * 60 * 1000));
  const end = new Date(endWITA.getTime() - (8 * 60 * 60 * 1000));

  return { start, end };
}

/**
 * Konversi waktu frontend ke timezone Asia/Makassar sebelum dikirim ke backend
 * Berguna untuk input form tanggal/waktu yang perlu dikonversi dari timezone lokal browser
 */
export function convertToIndonesiaTimezone(date: Date | string): Date {
  const inputDate = typeof date === 'string' ? new Date(date) : date;
  
  // Dapatkan waktu dalam format Asia/Makassar 
  const witaString = inputDate.toLocaleString("en-US", {
    timeZone: "Asia/Makassar",
    hour12: false,
    year: 'numeric',
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  
  // Parse komponen tanggal dan waktu
  const [datePart, timePart] = witaString.split(', ');
  const [month, day, year] = datePart.split('/');
  const [hour, minute, second] = timePart.split(':');
  
  // Buat Date object dalam UTC yang merepresentasikan waktu WITA
  // WITA = UTC+8, jadi untuk mendapatkan UTC kita kurangi 8 jam
  const utcTime = new Date(Date.UTC(
    parseInt(year),
    parseInt(month) - 1, // Month is 0-indexed
    parseInt(day),
    parseInt(hour) - 8, // Convert WITA to UTC (subtract 8 hours)
    parseInt(minute),
    parseInt(second)
  ));
  
  return utcTime;
}

/**
 * Format tanggal input form untuk dikirim ke backend dengan timezone Indonesia
 * Berguna untuk input type="date" dan type="datetime-local"
 */
export function formatDateForBackend(date: Date | string): string {
  const indonesiaDate = convertToIndonesiaTimezone(date);
  return indonesiaDate.toISOString();
}

/**
 * Konversi waktu dari input form ke timezone Indonesia sebelum submit
 * Berguna untuk semua input waktu di frontend sebelum dikirim ke API
 */
export function prepareDateTimeForSubmission(dateTimeValue: string | Date): Date {
  if (!dateTimeValue) return new Date();
  
  const inputDate = new Date(dateTimeValue);
  
  // Jika browser user dalam timezone berbeda, konversi ke Asia/Makassar
  const offsetDiff = inputDate.getTimezoneOffset() + (INDONESIA_TIMEZONE_OFFSET * 60);
  const indonesiaTime = new Date(inputDate.getTime() + (offsetDiff * 60 * 1000));
  
  return indonesiaTime;
}

/**
 * Helper untuk konversi form date input ke format yang tepat untuk backend
 * Menggunakan timezone Asia/Makassar
 */
export function convertFormDateToIndonesia(formDate: string): Date {
  if (!formDate) return new Date();
  
  // Parse tanggal dari form (format YYYY-MM-DD atau YYYY-MM-DDTHH:mm)
  const date = new Date(formDate);
  
  // Konversi ke timezone Indonesia
  return convertToIndonesiaTimezone(date);
}
