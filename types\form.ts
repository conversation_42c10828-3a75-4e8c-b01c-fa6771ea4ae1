export interface Form {
  id: string;
  title: string;
  description?: string | null;
  userId: string;
  pelatihanId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  isPublished: boolean;
  isDeleted: boolean;
}

export interface Question {
  id: string;
  formId: string;
  questionText: string;
  questionType: string;
  isRequired: boolean;
  order: number;
  options?: any;
  validationRules?: any;
}

export interface FormWithQuestions extends Form {
  questions: Question[];
}