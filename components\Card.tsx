'use client';

import { ReactNode } from 'react';

interface CardProps {
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  className?: string;
  noPadding?: boolean;
  headerAction?: ReactNode;
  isLoading?: boolean;
  bordered?: boolean;
  hoverable?: boolean;
}

export default function Card({ 
  title, 
  children, 
  footer, 
  className = '', 
  noPadding = false,
  headerAction,
  isLoading = false,
  bordered = true,
  hoverable = false
}: CardProps) {
  const borderClass = bordered ? 'border border-gray-200' : '';
  const hoverClass = hoverable ? 'transition-all duration-300 hover:shadow-md' : '';
  
  return (
    <div 
      className={`bg-white rounded-lg shadow-sm ${borderClass} ${hoverClass} overflow-hidden ${className}`}
      aria-busy={isLoading}
    >
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-70">
          <div className="w-8 h-8 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
        </div>
      )}
      
      {title && (
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 sm:px-6">
          <h3 className="text-base font-medium text-gray-900 sm:text-lg">{title}</h3>
          {headerAction && <div>{headerAction}</div>}
        </div>
      )}
      
      <div className={`relative ${noPadding ? '' : 'px-4 py-4 sm:px-6 sm:py-5'}`}>
        {children}
      </div>
      
      {footer && (
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 sm:px-6">
          {footer}
        </div>
      )}
    </div>
  );
}