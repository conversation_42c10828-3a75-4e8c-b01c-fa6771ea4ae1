import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import PDFDocument from 'pdfkit';
import { formatIndonesiaFullDate, getIndonesiaDayRange } from '@/utils/dateUtils';

// Define types based on what Prisma actually returns
type Pelatihan = {
  id: string;
  nama: string;
  tempat: string;
  jenjang: string | null;
  createdAt?: Date;
  updatedAt?: Date;
};

type Absensi = {
  id: string;
  pelatihanId: string;
  nama: string;
  nip_nik: string | null;
  jabatan: string | null;
  unit_kerja: string;
  no_hp: string;
  waktu: Date;
  tanda_tangan: string;
  jenjang: string | null;
  createdAt: Date;
  updatedAt?: Date;
};

// Type for Absensi with Pelatihan relation
type AbsensiWithPelatihan = Absensi & {
  pelatihan: Pelatihan;
};

// Define a type for the absensi query
type _AbsensiQuery = {
  include: {
    pelatihan: boolean;
  };
  orderBy: {
    waktu: 'asc' | 'desc';
  };
  where?: {
    pelatihanId?: string;
    waktu?: {
      gte?: Date;
      lte?: Date;
    };
    id?: {
      in?: string[];
    };
    [key: string]: unknown;
  };
};

export async function POST(req: NextRequest) {
  try {
    const { pelatihanId, tanggal, ids } = await req.json();
    // Buat query dasar
    const query: _AbsensiQuery = {
      include: {
        pelatihan: true,
      },
      orderBy: {
        waktu: 'asc',
      },
      where: {},
    };

    // Jika ada array ids, gunakan filter by id
    if (ids && Array.isArray(ids) && ids.length > 0) {
      query.where = {
        ...query.where,
        id: { in: ids },
      };
    } else {
      // Filter berdasarkan pelatihan jika ada
      if (pelatihanId) {
        query.where = {
          ...query.where,
          pelatihanId,
        };
      }
      // Filter berdasarkan tanggal jika ada
      if (tanggal) {
        const parsedDate = new Date(tanggal);
        const { start, end } = getIndonesiaDayRange(parsedDate);
        query.where = {
          ...query.where,
          waktu: {
            gte: start,
            lte: end,
          },
        };
      }
    }
    // Ambil data absensi
    const absensiData = await prisma.absensi.findMany(query) as AbsensiWithPelatihan[];
    
    if (absensiData.length === 0) {
      return NextResponse.json(
        { error: 'Tidak ada data absensi yang ditemukan' },
        { status: 404 }
      );
    }
    
    // Get pelatihan info from first absensi record
    const pelatihanInfo = absensiData[0].pelatihan;
      // Get date from first absensi record for header
    const tanggalPelatihan = new Date(absensiData[0].waktu);
    const formattedDate = formatIndonesiaFullDate(tanggalPelatihan);
    
    try {
      // Generate PDF
      const pdfBuffer = await generatePDF(absensiData, pelatihanInfo, formattedDate);

      // Create a sanitized filename based on pelatihan name and date
      const sanitizedPelatihanName = pelatihanInfo.nama
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '_');
      const dateForFilename = tanggal
        ? new Date(tanggal).toISOString().split('T')[0]
        : new Date(absensiData[0].waktu).toISOString().split('T')[0];
      // Remove any leading/trailing underscores
      const cleanName = sanitizedPelatihanName.replace(/^_+|_+$/g, '');
      const filename = `${cleanName}_${dateForFilename}.pdf`;

      // Set response header
      const headers = new Headers();
      headers.set('Content-Type', 'application/pdf');
      headers.set('Content-Disposition', `attachment; filename="${filename}"`);

      return new NextResponse(pdfBuffer, {
        status: 200,
        headers,
      });
    } catch (pdfError) {
      console.error('PDF generation error:', pdfError);
      return NextResponse.json(
        { error: `Terjadi kesalahan saat membuat PDF: ${pdfError instanceof Error ? pdfError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Terjadi kesalahan saat menghasilkan PDF' },
      { status: 500 }
    );
  }
}

// Fungsi untuk memformat tanggal ke format Indonesia dengan penyesuaian zona waktu
function _formatTanggal(date: Date): string {
  return formatIndonesiaFullDate(date);
}

async function generatePDF(
  absensiData: AbsensiWithPelatihan[], 
  pelatihanInfo: AbsensiWithPelatihan['pelatihan'], 
  tanggal: string
): Promise<Buffer> {
  return new Promise<Buffer>((resolve, reject) => {
    try {
      const doc = new PDFDocument({
        size: 'A4',
        margin: 50,
        bufferPages: true,
        info: {
          Title: 'Daftar Hadir',
          Author: 'Sistem Absensi',
          Subject: 'Daftar Hadir Pelatihan',
          Keywords: 'daftar hadir, absensi, pelatihan',
          Creator: 'Sistem Absensi Pelatihan',
          Producer: 'PDFKit'
        }
      });
      
      // Collect PDF data into buffer
      const chunks: Buffer[] = [];
      doc.on('data', (chunk: Buffer) => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
      
      // Add header with pelatihan info
      doc.font('Helvetica-Bold').fontSize(16);
      doc.text('DAFTAR HADIR', { align: 'center' });
      doc.moveDown();

      doc.font('Helvetica').fontSize(12);
      doc.text(`Nama Kegiatan : ${pelatihanInfo.nama || 'N/A'}`, { align: 'left' });
      doc.text(`Tempat        : ${pelatihanInfo.tempat || 'N/A'}`, { align: 'left' });
      doc.text(`Tanggal       : ${tanggal || 'N/A'}`, { align: 'left' });
      // doc.text(`Jenjang       : ${pelatihanInfo.jenjang || 'N/A'}`, { align: 'left' });
      doc.moveDown(2);
      
      // Calculate table dimensions
      const tableLeft = 50;
      const tableWidth = doc.page.width - 100;
      const colWidths = {
        no: 30,
        nama: 150,
        instansi: 150,
        tandatangan: 170
      };
      
      // Calculate how many items can fit on a page
      const rowHeight = 60; // Tinggi baris diperbesar dari 40 ke 60
      const tableTop = 200; // Adjusted based on header content
      const pageHeight = doc.page.height - 100; // Leave margin at bottom
      
      // Process all data with pagination
      let currentPage = 1;
      let currentY = tableTop;

      // Draw table headers on first page
      drawTableHeader(doc, tableLeft, tableTop, colWidths, tableWidth);
      currentY += 40; // Move down after header

      // Process each attendance record
      for (let i = 0; i < absensiData.length; i++) {
        // Check if we need a new page
        if (currentY + rowHeight > pageHeight) {
          doc.addPage();
          currentPage++;
          
          // Add page number at top right
          doc.font('Helvetica').fontSize(10);
          doc.text(`Halaman ${currentPage}`, doc.page.width - 100, 50, { align: 'right' });
          
          // Add consistent header on all continuation pages
          doc.font('Helvetica-Bold').fontSize(14);
          doc.text('DAFTAR HADIR', 50, 50, { align: 'center', width: doc.page.width - 100 });
          
          doc.font('Helvetica').fontSize(12);
          doc.text(`${pelatihanInfo.nama || 'N/A'} (lanjutan)`, 50, 70, { align: 'center', width: doc.page.width - 100 });
          
          // Start table with sufficient space below header
          currentY = 120; // Fixed position for all continuation pages
          
          // Draw table header on new page
          drawTableHeader(doc, tableLeft, currentY, colWidths, tableWidth);
          currentY += 40; // Move down after header
        }
        
        // Draw the attendance record
        const item = absensiData[i];
        
        // Draw row background and borders
        doc.rect(tableLeft, currentY, tableWidth, rowHeight).stroke('#000000');
        
        // Draw vertical lines
        const namaX = tableLeft + colWidths.no;
        const instansiX = namaX + colWidths.nama;
        const tandatanganX = instansiX + colWidths.instansi;
        
        doc.moveTo(namaX, currentY).lineTo(namaX, currentY + rowHeight).stroke();
        doc.moveTo(instansiX, currentY).lineTo(instansiX, currentY + rowHeight).stroke();
        doc.moveTo(tandatanganX, currentY).lineTo(tandatanganX, currentY + rowHeight).stroke();
        
        // Add content
        doc.font('Helvetica').fontSize(10);
        
        // Number
        doc.text((i + 1).toString(), tableLeft + 5, currentY + 15, { width: colWidths.no, align: 'center' });
        
        // Name
        doc.text(item.nama || 'N/A', namaX + 5, currentY + 15, { width: colWidths.nama - 10, align: 'left' });
        
        // Institution/Unit
        doc.text(item.unit_kerja || 'N/A', instansiX + 5, currentY + 15, { width: colWidths.instansi - 10, align: 'left' });
        
        // Add signature if available
        if (item.tanda_tangan) {
          try {
            let base64Data = item.tanda_tangan;
            // Make sure the base64 data has the correct prefix
            if (!base64Data.startsWith('data:image')) {
              base64Data = `data:image/png;base64,${base64Data}`;
            }
            // Perbesar ukuran gambar tanda tangan dan tambahkan background putih agar lebih terang
            const signatureX = tandatanganX + (colWidths.tandatangan / 2);
            // Gambar background putih
            doc.save();
            doc.rect(signatureX - 60, currentY + 10, 120, 40).fill('#fff');
            doc.restore();
            // Gambar tanda tangan di atas background putih
            doc.image(base64Data, signatureX - 60, currentY + 10, {
              fit: [120, 40],
              align: 'center',
              valign: 'center'
            });
          } catch (err) {
            console.error('Error adding signature:', err);
            // Draw a placeholder instead of failing
            doc.text('(Signature Error)', tandatanganX + 5, currentY + 15, { 
              width: colWidths.tandatangan - 10, 
              align: 'center' 
            });
          }
        }
        
        currentY += rowHeight;
      }
      
      // Add page numbers to first page
      doc.switchToPage(0);
      doc.font('Helvetica').fontSize(10);
      doc.text(`Halaman 1 dari ${currentPage}`, doc.page.width - 100, 50, { align: 'right' });
      
      // Finalize PDF
      doc.end();
      
    } catch (error) {
      console.error('PDF generation error details:', error);
      reject(error);
    }
  });
}
// Helper function to draw table header
function drawTableHeader(doc: PDFKit.PDFDocument, tableLeft: number, y: number, colWidths: {no: number, nama: number, instansi: number, tandatangan: number}, tableWidth: number) {
  // Draw header background and borders
  doc.rect(tableLeft, y, tableWidth, 40).stroke();
  
  // Draw vertical lines for columns using moveTo and lineTo
  doc.moveTo(tableLeft + colWidths.no, y).lineTo(tableLeft + colWidths.no, y + 40).stroke();
  doc.moveTo(tableLeft + colWidths.no + colWidths.nama, y).lineTo(tableLeft + colWidths.no + colWidths.nama, y + 40).stroke();
  doc.moveTo(tableLeft + colWidths.no + colWidths.nama + colWidths.instansi, y).lineTo(tableLeft + colWidths.no + colWidths.nama + colWidths.instansi, y + 40).stroke();
  
  // Header text
  doc.font('Helvetica-Bold').fontSize(12);
  doc.text('No', tableLeft + 5, y + 15, { width: colWidths.no - 10, align: 'center' });
  doc.text('Nama', tableLeft + colWidths.no + 5, y + 15, { width: colWidths.nama - 10, align: 'center' });
  doc.text('Instansi', tableLeft + colWidths.no + colWidths.nama + 5, y + 15, { width: colWidths.instansi - 10, align: 'center' });
  doc.text('Tanda Tangan', tableLeft + colWidths.no + colWidths.nama + colWidths.instansi + 5, y + 15, { width: colWidths.tandatangan - 10, align: 'center' });
}
