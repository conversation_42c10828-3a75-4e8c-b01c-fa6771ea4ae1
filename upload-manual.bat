@echo off
setlocal enabledelayedexpansion

echo ============================================
echo   UPLOAD MANUAL KE VPS - PILIH METODE
echo ============================================
echo.

:menu
echo Pilih metode upload:
echo.
echo 1. SCP - Upload Langsung (Cepat)
echo 2. SFTP - Upload Interaktif
echo 3. Upload File Satu per Satu (SCP)
echo 4. Verifikasi File di VPS
echo 5. Test Koneksi SSH
echo 0. Keluar
echo.
set /p choice="Masukkan pilihan (1-5): "

if "%choice%"=="1" goto scp_upload
if "%choice%"=="2" goto sftp_upload
if "%choice%"=="3" goto individual_upload
if "%choice%"=="4" goto verify_files
if "%choice%"=="5" goto test_connection
if "%choice%"=="0" goto end

echo Pi<PERSON>han tidak valid!
goto menu

:scp_upload
echo.
echo ========== SCP UPLOAD (BATCH) ==========
set /p username="Masukkan username VPS: "
echo.
echo Uploading deployment.tar.gz (34.9 MB)...
scp .next\deployment.tar.gz %<EMAIL>:/home/<USER>/
if errorlevel 1 (
    echo ERROR: Upload deployment.tar.gz gagal!
    pause
    goto menu
)

echo Uploading fix-photo-upload-vps.sh...
scp fix-photo-upload-vps.sh %<EMAIL>:/home/<USER>/
if errorlevel 1 (
    echo ERROR: Upload script gagal!
    pause
    goto menu
)

echo Uploading nginx-config.txt...
scp nginx-config.txt %<EMAIL>:/home/<USER>/
if errorlevel 1 (
    echo ERROR: Upload nginx config gagal!
    pause
    goto menu
)

echo.
echo ✅ UPLOAD SELESAI!
echo Semua file berhasil diupload ke VPS.
echo.
echo Langkah selanjutnya:
echo 1. SSH ke VPS: ssh %<EMAIL>
echo 2. Jalankan: mkdir -p /home/<USER>/deployment
echo 3. Jalankan: cd /home/<USER>/deployment
echo 4. Jalankan: tar -xzf ../deployment.tar.gz
echo 5. Jalankan: chmod +x /home/<USER>/fix-photo-upload-vps.sh
echo 6. Jalankan: sudo /home/<USER>/fix-photo-upload-vps.sh
echo.
pause
goto menu

:sftp_upload
echo.
echo ========== SFTP UPLOAD (INTERAKTIF) ==========
set /p username="Masukkan username VPS: "
echo.
echo Membuka SFTP session...
echo.
echo INSTRUKSI SFTP:
echo 1. Setelah login, ketik: cd /home/<USER>/
echo 2. Upload file: put .next/deployment.tar.gz
echo 3. Upload file: put fix-photo-upload-vps.sh
echo 4. Upload file: put nginx-config.txt
echo 5. Cek file: ls -la
echo 6. Keluar: quit
echo.
pause
sftp %<EMAIL>
goto menu

:individual_upload
echo.
echo ========== UPLOAD INDIVIDUAL ==========
set /p username="Masukkan username VPS: "
echo.

:upload_menu
echo Pilih file untuk diupload:
echo.
echo 1. deployment.tar.gz (34.9 MB) - File utama
echo 2. fix-photo-upload-vps.sh - Script perbaikan
echo 3. nginx-config.txt - Konfigurasi nginx
echo 4. Kembali ke menu utama
echo.
set /p file_choice="Pilih file (1-4): "

if "%file_choice%"=="1" (
    echo Uploading deployment.tar.gz...
    scp .next\deployment.tar.gz %<EMAIL>:/home/<USER>/
    echo Upload selesai!
    pause
    goto upload_menu
)

if "%file_choice%"=="2" (
    echo Uploading fix-photo-upload-vps.sh...
    scp fix-photo-upload-vps.sh %<EMAIL>:/home/<USER>/
    echo Upload selesai!
    pause
    goto upload_menu
)

if "%file_choice%"=="3" (
    echo Uploading nginx-config.txt...
    scp nginx-config.txt %<EMAIL>:/home/<USER>/
    echo Upload selesai!
    pause
    goto upload_menu
)

if "%file_choice%"=="4" goto menu

echo Pilihan tidak valid!
goto upload_menu

:verify_files
echo.
echo ========== VERIFIKASI FILE DI VPS ==========
set /p username="Masukkan username VPS: "
echo.
echo Checking files di VPS...
ssh %<EMAIL> "echo '=== File List ===' && ls -la /home/<USER>/ | grep -E '(deployment\.tar\.gz|fix-photo-upload-vps\.sh|nginx-config\.txt)' && echo && echo '=== File Sizes ===' && ls -lh /home/<USER>/deployment.tar.gz /home/<USER>/fix-photo-upload-vps.sh /home/<USER>/nginx-config.txt 2>/dev/null || echo 'Beberapa file belum ada'"
echo.
pause
goto menu

:test_connection
echo.
echo ========== TEST KONEKSI SSH ==========
set /p username="Masukkan username VPS: "
echo.
echo Testing SSH connection...
ssh %<EMAIL> "echo 'SSH connection successful!' && uname -a && df -h /home"
echo.
pause
goto menu

:end
echo.
echo Terima kasih!
echo.
pause
