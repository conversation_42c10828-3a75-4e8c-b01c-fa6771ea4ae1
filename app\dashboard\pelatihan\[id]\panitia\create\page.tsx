import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { notFound, redirect } from 'next/navigation';
import PanitiaForm from '@/components/PanitiaForm';

interface CreatePanitiaPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function CreatePanitiaPage(props: CreatePanitiaPageProps) {
  const params = await props.params;
  const user = await getCurrentUser();

  if (!user || !['ADMIN', 'GM1', 'GM2', 'GM3', 'GM4', 'GM5'].includes(user.role)) {
    redirect('/login');
  }

  // Periksa apakah pelatihan ada
  const pelatihan = await prisma.pelatihan.findUnique({
    where: { id: params.id },
    select: {
      id: true,
      nama: true,
    }
  });

  if (!pelatihan) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Tambah Panitia Kegiatan</h1>
      </div>
      
      <div className="mb-6">
        <div className="mb-1 text-sm font-medium text-gray-500">Nama Kegiatan</div>
        <div className="font-semibold">{pelatihan.nama}</div>
      </div>
      
      <PanitiaForm pelatihanId={params.id} />
    </div>
  );
}