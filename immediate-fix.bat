@echo off
echo ========================================
echo   IMMEDIATE FIX - NGINX STATIC FILES
echo ========================================
echo.

set VPS_HOST=kegiatan.bpmpkaltim.id
set VPS_USER=root

echo Problem: Files uploaded to public/uploads/ but nginx can't serve them
echo URL Expected: https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/[id]/filename
echo Current Issue: 404 error when accessing photos
echo.
echo Solution: Add nginx location block for /uploads/ path
echo.

echo [STEP 1] Upload immediate fix script...
scp immediate-nginx-fix.sh %VPS_USER%@%VPS_HOST%:/root/
if %errorlevel% neq 0 (
    echo ERROR: Upload gagal. Coba manual:
    echo 1. Upload immediate-nginx-fix.sh ke /root/
    echo 2. SSH dan jalankan: sudo ./immediate-nginx-fix.sh
    pause
    exit /b 1
)

echo [STEP 2] Execute immediate fix...
echo.
echo Script akan:
echo ✓ Auto-detect lokasi aplikasi Next.js
echo ✓ Pastikan folder uploads ada dan permissions benar
echo ✓ Update/buat nginx config untuk serve /uploads/
echo ✓ Test akses static files
echo ✓ Backup semua config sebelum perubahan
echo.
echo Executing fix...

ssh %VPS_USER%@%VPS_HOST% "chmod +x immediate-nginx-fix.sh && sudo ./immediate-nginx-fix.sh"

echo.
echo ========================================
echo   IMMEDIATE FIX COMPLETED
echo ========================================
echo.
echo VERIFICATION STEPS:
echo 1. Test URL: https://kegiatan.bpmpkaltim.id/uploads/
echo    Expected: 403 Forbidden atau 200 OK (bukan 404)
echo.
echo 2. Upload foto baru di admin panel
echo    Expected: Foto muncul di detail absensi tanpa 404
echo.
echo 3. Test specific photo URL:
echo    https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/[id]/[filename]
echo    Expected: 200 OK dan foto ditampilkan
echo.

echo QUICK VERIFICATION:
curl -I https://kegiatan.bpmpkaltim.id/uploads/
echo.

echo If still having issues, check nginx logs:
echo ssh %VPS_USER%@%VPS_HOST% "tail -f /var/log/nginx/error.log"
echo.
pause
