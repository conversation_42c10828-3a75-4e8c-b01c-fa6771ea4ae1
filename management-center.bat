@echo off
setlocal enabledelayedexpansion

:: COMPREHENSIVE MONITORING AND <PERSON><PERSON><PERSON>MENT INTERFACE
:: All-in-one solution untuk auto-refresh monitoring

title Advanced Auto-Refresh Management Center

:MAIN_MENU
cls
echo.
echo ================================================
echo    ADVANCED AUTO-REFRESH MANAGEMENT CENTER
echo    Comprehensive CloudPanel Ubuntu 24.04 Solution
echo ================================================
echo.
echo [36m🎯 CURRENT STATUS:[0m

:: Quick status check
echo [33mChecking system status...[0m
ssh root@************* "systemctl is-active nginx" >nul 2>&1
if %errorlevel% equ 0 (
    echo   • Nginx: [32m✅ ACTIVE[0m
) else (
    echo   • Nginx: [31m❌ INACTIVE[0m
)

ssh root@************* "systemctl is-active advanced-photo-monitor" >nul 2>&1
if %errorlevel% equ 0 (
    echo   • Photo Monitor: [32m✅ ACTIVE[0m
) else (
    echo   • Photo Monitor: [31m❌ INACTIVE[0m
)

echo.
echo [33m🔧 MANAGEMENT OPTIONS:[0m
echo.
echo   [32m1[0m. 🚀 Deploy Final Auto-Refresh Solution
echo   [32m2[0m. 📊 Real-time Monitoring Dashboard
echo   [32m3[0m. 🔍 Comprehensive Troubleshooter
echo   [32m4[0m. 🧪 Test Photo Upload Real-time
echo   [32m5[0m. 📋 View System Logs
echo   [32m6[0m. ⚙️  Advanced System Configuration
echo   [32m7[0m. 🔄 Service Management
echo   [32m8[0m. 📖 Documentation & Help
echo   [32m9[0m. 🆘 Emergency Rollback
echo   [32m0[0m. ❌ Exit
echo.

set /p choice="Choose option (0-9): "

if "%choice%"=="1" goto DEPLOY_SOLUTION
if "%choice%"=="2" goto MONITORING_DASHBOARD
if "%choice%"=="3" goto TROUBLESHOOTER
if "%choice%"=="4" goto TEST_UPLOAD
if "%choice%"=="5" goto VIEW_LOGS
if "%choice%"=="6" goto ADVANCED_CONFIG
if "%choice%"=="7" goto SERVICE_MANAGEMENT
if "%choice%"=="8" goto DOCUMENTATION
if "%choice%"=="9" goto EMERGENCY_ROLLBACK
if "%choice%"=="0" goto EXIT

echo [31m[ERROR][0m Invalid option. Please choose 0-9.
timeout /t 2 >nul
goto MAIN_MENU

:DEPLOY_SOLUTION
cls
echo.
echo [35m================================================[0m
echo    🚀 DEPLOY FINAL AUTO-REFRESH SOLUTION
echo [35m================================================[0m
echo.

echo [33m[WARNING][0m This will deploy the comprehensive solution including:
echo.
echo   • ✅ Advanced nginx configuration optimization
echo   • ✅ Real-time file monitoring system
echo   • ✅ Auto-permission fixing service
echo   • ✅ System inotify optimization
echo   • ✅ Background monitoring dashboard
echo   • ✅ Automated troubleshooting tools
echo.

set /p deploy_confirm="Deploy now? This may take 2-3 minutes (y/n): "

if /i not "%deploy_confirm%"=="y" goto MAIN_MENU

echo.
echo [35m[ACTION][0m Starting deployment...

call "%~dp0deploy-final-autorefresh.bat"

echo.
echo [36m[INFO][0m Deployment completed. Returning to main menu...
timeout /t 3 >nul
goto MAIN_MENU

:MONITORING_DASHBOARD
cls
echo.
echo [35m================================================[0m
echo    📊 REAL-TIME MONITORING DASHBOARD
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Choose monitoring mode:
echo.
echo   [32m1[0m. Single Status Check
echo   [32m2[0m. Continuous Monitoring (10s refresh)
echo   [32m3[0m. Real-time Test Mode
echo   [32m4[0m. Upload Statistics
echo   [32m0[0m. Back to Main Menu
echo.

set /p monitor_choice="Choose option (0-4): "

if "%monitor_choice%"=="1" (
    echo.
    echo [35m[ACTION][0m Running single status check...
    call "%~dp0monitor-autorefresh.bat" single
) else if "%monitor_choice%"=="2" (
    echo.
    echo [35m[ACTION][0m Starting continuous monitoring...
    echo [33m[INFO][0m Press Ctrl+C to stop monitoring
    call "%~dp0monitor-autorefresh.bat" continuous
) else if "%monitor_choice%"=="3" (
    echo.
    echo [35m[ACTION][0m Running real-time test...
    call "%~dp0monitor-autorefresh.bat" test
) else if "%monitor_choice%"=="4" (
    echo.
    echo [35m[ACTION][0m Displaying upload statistics...
    ssh root@************* "tail -50 /var/log/photo-monitor-stats.log"
) else if "%monitor_choice%"=="0" (
    goto MAIN_MENU
) else (
    echo [31m[ERROR][0m Invalid option
    timeout /t 2 >nul
)

echo.
pause
goto MAIN_MENU

:TROUBLESHOOTER
cls
echo.
echo [35m================================================[0m
echo    🔍 COMPREHENSIVE TROUBLESHOOTER
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Running automated troubleshooter...
echo [36m[INFO][0m This will check and automatically fix common issues
echo.

call "%~dp0troubleshoot-autorefresh.bat"

echo.
pause
goto MAIN_MENU

:TEST_UPLOAD
cls
echo.
echo [35m================================================[0m
echo    🧪 TEST PHOTO UPLOAD REAL-TIME
echo [35m================================================[0m
echo.

echo [33m[INFO][0m This will open the application for manual testing
echo.
echo [36m📋 TEST STEPS:[0m
echo   1. Login to the application
echo   2. Create new attendance or edit existing
echo   3. Upload a photo
echo   4. Immediately view attendance detail
echo   5. Photo should appear WITHOUT nginx reload
echo.

set /p open_app="Open application now? (y/n): "

if /i "%open_app%"=="y" (
    echo.
    echo [35m[ACTION][0m Opening application...
    start "" "https://kegiatan.bpmpkaltim.id"
    
    echo [35m[ACTION][0m Starting real-time monitoring in background...
    start "" cmd /c "call %~dp0monitor-autorefresh.bat continuous"
    
    echo.
    echo [36m[INFO][0m Application opened and monitoring started
    echo [33m[INFO][0m Follow the test steps above
)

echo.
pause
goto MAIN_MENU

:VIEW_LOGS
cls
echo.
echo [35m================================================[0m
echo    📋 VIEW SYSTEM LOGS
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Choose log type:
echo.
echo   [32m1[0m. Photo Monitor Logs (real-time)
echo   [32m2[0m. Nginx Access Logs
echo   [32m3[0m. Nginx Error Logs
echo   [32m4[0m. System Service Logs
echo   [32m5[0m. Upload Statistics
echo   [32m6[0m. All Recent Logs Summary
echo   [32m0[0m. Back to Main Menu
echo.

set /p log_choice="Choose option (0-6): "

if "%log_choice%"=="1" (
    echo.
    echo [35m[ACTION][0m Showing photo monitor logs (Press Ctrl+C to stop)...
    ssh root@************* "tail -f /var/log/advanced-photo-monitor.log"
) else if "%log_choice%"=="2" (
    echo.
    echo [35m[ACTION][0m Showing nginx access logs...
    ssh root@************* "tail -50 /var/log/nginx/photo_access.log"
) else if "%log_choice%"=="3" (
    echo.
    echo [35m[ACTION][0m Showing nginx error logs...
    ssh root@************* "tail -50 /var/log/nginx/error.log"
) else if "%log_choice%"=="4" (
    echo.
    echo [35m[ACTION][0m Showing service logs...
    ssh root@************* "journalctl -u advanced-photo-monitor -n 20 --no-pager"
) else if "%log_choice%"=="5" (
    echo.
    echo [35m[ACTION][0m Showing upload statistics...
    ssh root@************* "tail -100 /var/log/photo-monitor-stats.log"
) else if "%log_choice%"=="6" (
    echo.
    echo [35m[ACTION][0m Showing all recent logs summary...
    ssh root@************* "echo '=== PHOTO MONITOR ===' && tail -10 /var/log/advanced-photo-monitor.log && echo '=== NGINX ACCESS ===' && tail -10 /var/log/nginx/photo_access.log && echo '=== NGINX ERROR ===' && tail -10 /var/log/nginx/error.log"
) else if "%log_choice%"=="0" (
    goto MAIN_MENU
) else (
    echo [31m[ERROR][0m Invalid option
    timeout /t 2 >nul
    goto VIEW_LOGS
)

echo.
pause
goto MAIN_MENU

:ADVANCED_CONFIG
cls
echo.
echo [35m================================================[0m
echo    ⚙️ ADVANCED SYSTEM CONFIGURATION
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Advanced configuration options:
echo.
echo   [32m1[0m. Optimize inotify Limits
echo   [32m2[0m. Update Nginx Configuration
echo   [32m3[0m. Fix Directory Permissions
echo   [32m4[0m. Restart All Services
echo   [32m5[0m. System Performance Tuning
echo   [32m6[0m. Backup Current Configuration
echo   [32m0[0m. Back to Main Menu
echo.

set /p config_choice="Choose option (0-6): "

if "%config_choice%"=="1" (
    echo.
    echo [35m[ACTION][0m Optimizing inotify limits...
    ssh root@************* "echo 'fs.inotify.max_user_watches=1048576' >> /etc/sysctl.conf && sysctl -p"
    echo [32m[SUCCESS][0m inotify limits optimized
) else if "%config_choice%"=="2" (
    echo.
    echo [35m[ACTION][0m Testing and reloading nginx configuration...
    ssh root@************* "nginx -t && systemctl reload nginx"
    echo [32m[SUCCESS][0m Nginx configuration reloaded
) else if "%config_choice%"=="3" (
    echo.
    echo [35m[ACTION][0m Fixing directory permissions...
    ssh root@************* "find /home/<USER>/htdocs/public/uploads -type d -exec chmod 755 {} \; && find /home/<USER>/htdocs/public/uploads -type f -exec chmod 644 {} \;"
    echo [32m[SUCCESS][0m Directory permissions fixed
) else if "%config_choice%"=="4" (
    echo.
    echo [35m[ACTION][0m Restarting all services...
    ssh root@************* "systemctl restart nginx && systemctl restart advanced-photo-monitor"
    echo [32m[SUCCESS][0m All services restarted
) else if "%config_choice%"=="5" (
    echo.
    echo [35m[ACTION][0m Applying performance tuning...
    ssh root@************* "echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf && sysctl -p"
    echo [32m[SUCCESS][0m Performance tuning applied
) else if "%config_choice%"=="6" (
    echo.
    echo [35m[ACTION][0m Creating configuration backup...
    ssh root@************* "tar -czf /tmp/autorefresh-config-backup-$(date +%%Y%%m%%d-%%H%%M%%S).tar.gz /etc/nginx /etc/systemd/system/advanced-photo-monitor.service /usr/local/bin/advanced-photo-monitor.sh"
    echo [32m[SUCCESS][0m Configuration backup created in /tmp/
) else if "%config_choice%"=="0" (
    goto MAIN_MENU
) else (
    echo [31m[ERROR][0m Invalid option
    timeout /t 2 >nul
    goto ADVANCED_CONFIG
)

echo.
pause
goto MAIN_MENU

:SERVICE_MANAGEMENT
cls
echo.
echo [35m================================================[0m
echo    🔄 SERVICE MANAGEMENT
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Service management options:
echo.
echo   [32m1[0m. Start Photo Monitor Service
echo   [32m2[0m. Stop Photo Monitor Service
echo   [32m3[0m. Restart Photo Monitor Service
echo   [32m4[0m. Enable Auto-start Photo Monitor
echo   [32m5[0m. Disable Auto-start Photo Monitor
echo   [32m6[0m. Check Service Status
echo   [32m7[0m. Reload Nginx Configuration
echo   [32m8[0m. Restart Nginx Service
echo   [32m0[0m. Back to Main Menu
echo.

set /p service_choice="Choose option (0-8): "

if "%service_choice%"=="1" (
    echo [35m[ACTION][0m Starting photo monitor service...
    ssh root@************* "systemctl start advanced-photo-monitor"
) else if "%service_choice%"=="2" (
    echo [35m[ACTION][0m Stopping photo monitor service...
    ssh root@************* "systemctl stop advanced-photo-monitor"
) else if "%service_choice%"=="3" (
    echo [35m[ACTION][0m Restarting photo monitor service...
    ssh root@************* "systemctl restart advanced-photo-monitor"
) else if "%service_choice%"=="4" (
    echo [35m[ACTION][0m Enabling auto-start for photo monitor...
    ssh root@************* "systemctl enable advanced-photo-monitor"
) else if "%service_choice%"=="5" (
    echo [35m[ACTION][0m Disabling auto-start for photo monitor...
    ssh root@************* "systemctl disable advanced-photo-monitor"
) else if "%service_choice%"=="6" (
    echo [35m[ACTION][0m Checking service status...
    ssh root@************* "systemctl status advanced-photo-monitor nginx"
) else if "%service_choice%"=="7" (
    echo [35m[ACTION][0m Reloading nginx configuration...
    ssh root@************* "nginx -t && systemctl reload nginx"
) else if "%service_choice%"=="8" (
    echo [35m[ACTION][0m Restarting nginx service...
    ssh root@************* "systemctl restart nginx"
) else if "%service_choice%"=="0" (
    goto MAIN_MENU
) else (
    echo [31m[ERROR][0m Invalid option
    timeout /t 2 >nul
    goto SERVICE_MANAGEMENT
)

echo.
pause
goto MAIN_MENU

:DOCUMENTATION
cls
echo.
echo [35m================================================[0m
echo    📖 DOCUMENTATION & HELP
echo [35m================================================[0m
echo.

echo [33m🎯 TENTANG AUTO-REFRESH FIX:[0m
echo   Solution comprehensive untuk masalah foto upload yang
echo   memerlukan nginx reload manual setiap kali ada foto baru.
echo.
echo [33m🔧 KOMPONEN UTAMA:[0m
echo   • Advanced Nginx Configuration (no-cache untuk uploads)
echo   • Real-time File Monitoring Service (inotify-based)
echo   • Automatic Permission Fixing
echo   • System Optimization untuk File Watching
echo   • Background Health Monitoring
echo.
echo [33m📋 CARA KERJA:[0m
echo   1. Monitor file uploads dengan inotify real-time
echo   2. Auto-fix permissions secara langsung
echo   3. Nginx dikonfigurasi tanpa cache untuk uploads
echo   4. Background service handle semua otomatis
echo.
echo [33m🧪 TESTING PROCEDURE:[0m
echo   1. Upload foto di aplikasi absensi
echo   2. Langsung buka detail absensi
echo   3. Foto harus muncul TANPA reload nginx
echo   4. Monitor logs untuk verifikasi
echo.
echo [33m🔍 TROUBLESHOOTING:[0m
echo   • Restart monitor: systemctl restart advanced-photo-monitor
echo   • Reload nginx: systemctl reload nginx
echo   • Check permissions: ls -la /home/<USER>/htdocs/public/uploads/
echo   • Manual fix: chown -R user:group /path/to/uploads/
echo.
echo [33m📞 LOG FILES:[0m
echo   • Photo Monitor: /var/log/advanced-photo-monitor.log
echo   • Statistics: /var/log/photo-monitor-stats.log
echo   • Nginx Access: /var/log/nginx/photo_access.log
echo   • System Service: journalctl -u advanced-photo-monitor
echo.
echo [33m⚠️ EMERGENCY ROLLBACK:[0m
echo   • Stop monitor: systemctl stop advanced-photo-monitor
echo   • Disable monitor: systemctl disable advanced-photo-monitor
echo   • Restore nginx: use backup files in /tmp/
echo   • Restart nginx: systemctl restart nginx
echo.

pause
goto MAIN_MENU

:EMERGENCY_ROLLBACK
cls
echo.
echo [35m================================================[0m
echo    🆘 EMERGENCY ROLLBACK
echo [35m================================================[0m
echo.

echo [31m⚠️ WARNING: This will rollback all auto-refresh changes![0m
echo.
echo [33m[INFO][0m Rollback will:
echo   • Stop and disable photo monitor service
echo   • Restore original nginx configuration
echo   • Remove monitoring scripts
echo   • Restart nginx with original settings
echo.

set /p rollback_confirm="Are you sure you want to rollback? (yes/no): "

if not "%rollback_confirm%"=="yes" (
    echo [36m[INFO][0m Rollback cancelled
    timeout /t 2 >nul
    goto MAIN_MENU
)

echo.
echo [35m[ACTION][0m Starting emergency rollback...

:: Stop services
echo [36m[INFO][0m Stopping services...
ssh root@************* "systemctl stop advanced-photo-monitor 2>/dev/null || true"
ssh root@************* "systemctl disable advanced-photo-monitor 2>/dev/null || true"

:: Remove service files
echo [36m[INFO][0m Removing service files...
ssh root@************* "rm -f /etc/systemd/system/advanced-photo-monitor.service"
ssh root@************* "rm -f /usr/local/bin/advanced-photo-monitor.sh"
ssh root@************* "systemctl daemon-reload"

:: Restore nginx config from backup if available
echo [36m[INFO][0m Restoring nginx configuration...
ssh root@************* "if [ -f /tmp/advanced-autorefresh-backup-*/nginx.conf.original ]; then cp /tmp/advanced-autorefresh-backup-*/nginx.conf.original /etc/nginx/nginx.conf; fi"

:: Restart nginx
echo [36m[INFO][0m Restarting nginx...
ssh root@************* "nginx -t && systemctl restart nginx"

echo.
echo [32m[SUCCESS][0m ✅ Emergency rollback completed!
echo [33m[INFO][0m System restored to original state
echo [36m[INFO][0m You may need to manually configure static file serving

echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo [36m[INFO][0m Thank you for using Advanced Auto-Refresh Management Center!
echo [32m[INFO][0m For support, check the troubleshooting documentation.
echo.
pause
exit /b 0
