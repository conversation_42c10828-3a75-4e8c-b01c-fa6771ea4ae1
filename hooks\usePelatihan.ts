import { useState, useEffect } from 'react';

interface Pelatihan {
  id: string;
  nama: string;
  tempat: string;
  tgl_mulai: string;
  tgl_berakhir: string;
  jenjang: string;
  target_peserta: number;
}

export function usePelatihan() {
  const [pelatihan, setPelatihan] = useState<Pelatihan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPelatihan = async () => {
      try {
        const response = await fetch('/api/pelatihan');
        if (!response.ok) {
          throw new Error('Failed to fetch pelatihan data');
        }
        const data = await response.json();
        setPelatihan(data);
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        setIsLoading(false);
      }
    };

    fetchPelatihan();
  }, []);

  return { pelatihan, isLoading, error };
}