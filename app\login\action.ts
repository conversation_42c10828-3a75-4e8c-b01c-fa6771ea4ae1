'use server';

import { loginUser } from '../../lib/auth';
import { generateCsrfToken, validateCsrfToken } from '../../lib/csrf';

// Tipe hasil login
type LoginResult = 
  | { success: false; message: string; }
  | { success: true; user: { id: string; name: string; email: string; role: string; }; }
  | { success: true; redirect: true; path: string; };

export async function loginAction(email: string, password: string, csrfToken?: string): Promise<LoginResult> {
  try {
    // Validasi CSRF token jika ada
    if (csrfToken) {
      const isValidCsrf = await validateCsrfToken(csrfToken);
      if (!isValidCsrf) {
        console.warn('Server Action: CSRF token tidak valid');
        return { 
          success: false, 
          message: 'Sesi keamanan tidak valid. Silakan muat ulang halaman dan coba lagi.' 
        };
      }
    }

    console.log('Server Action: Mencoba login untuk email:', email);
    const result = await loginUser(email, password);
    
    if (result.success) {
      console.log('Server Action: Login berhasil, melakukan redirect');
      
      // Generate CSRF token baru setelah login berhasil
      await generateCsrfToken();
      
      // Mengembalikan objek redirect
      return {
        success: true,
        redirect: true,
        path: '/dashboard'
      };
    }
    
    console.log('Server Action: Login gagal:', result.message);
    return result as LoginResult;
  } catch (error) {
    console.error('Server Action: Login error:', error);
    return { success: false, message: 'Terjadi kesalahan server saat login' };
  }
}

// Helper function untuk mendapatkan CSRF token baru
export async function getCsrfToken(): Promise<string> {
  return await generateCsrfToken();
}