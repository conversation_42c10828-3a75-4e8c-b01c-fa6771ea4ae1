// File: utils/frontendTimezoneHelpers.ts
/**
 * Helper functions untuk konversi timezone di frontend sebelum submit ke backend
 * Contoh implementasi untuk berbagai jenis form input
 */

import { 
  formatDateForBackend, 
  prepareDateTimeForSubmission
} from './dateUtils';

/**
 * Contoh implementasi untuk form biodata dengan tanggal lahir
 */
export function prepareBiodataForSubmission(formData: any) {
  return {
    ...formData,
    // Konversi tanggal lahir ke timezone Indonesia sebelum dikirim ke backend
    tanggal_lahir: formatDateForBackend(formData.tanggal_lahir)
  };
}

/**
 * Contoh implementasi untuk form pelatihan dengan tanggal mulai dan berakhir
 */
export function preparePelatihanForSubmission(formData: any) {
  return {
    ...formData,
    // Konversi tanggal mulai dan berakhir ke timezone Indonesia
    tgl_mulai: formatDateForBackend(formData.tgl_mulai),
    tgl_berakhir: formatDateForBackend(formData.tgl_berakhir)
  };
}

/**
 * Contoh implementasi untuk form absensi dengan waktu submit
 */
export function prepareAbsensiForSubmission(formData: any) {
  // Tidak perlu konversi waktu di frontend, biarkan backend yang mengatur waktu absensi
  return {
    ...formData
    // waktu akan diisi oleh backend (createIndonesiaTime)
  };
}

/**
 * Helper umum untuk memproses semua field tanggal/waktu dalam form
 */
export function processFormDatesForSubmission(formData: any, dateFields: string[] = []) {
  const processedData = { ...formData };
  
  // List default field tanggal yang umum digunakan, TANPA 'waktu'!
  const commonDateFields = ['tanggal', 'tgl_mulai', 'tgl_berakhir', 'tanggal_lahir'];
  const fieldsToProcess = [...commonDateFields, ...dateFields];
  
  fieldsToProcess.forEach(field => {
    if (processedData[field]) {      try {
        // Konversi ke timezone Indonesia
        processedData[field] = formatDateForBackend(processedData[field]);      } catch (_error) {
        // Silently handle conversion errors
      }
    }  });
  
  return processedData;
}

/**
 * Hook untuk form yang membutuhkan konversi timezone
 */
export function useIndonesiaTimezone() {
  const convertForSubmission = (formData: any, customDateFields: string[] = []) => {
    return processFormDatesForSubmission(formData, customDateFields);
  };
  
  const convertSingleDate = (date: string | Date) => {
    return formatDateForBackend(date);
  };
  
  const getCurrentIndonesiaTime = () => {
    return prepareDateTimeForSubmission(new Date());
  };
  
  return {
    convertForSubmission,
    convertSingleDate,
    getCurrentIndonesiaTime
  };
}

/**
 * Wrapper untuk handleSubmit yang otomatis mengkonversi timezone
 */
export function withIndonesiaTimezone<T>(
  submitFunction: (data: T) => Promise<any>,
  dateFields: string[] = []
) {
  return async (formData: T) => {
    const processedData = processFormDatesForSubmission(formData, dateFields);
    return await submitFunction(processedData);
  };
}

/**
 * Contoh penggunaan untuk form generic
 */
export const ExampleFormSubmission = {
  // Contoh untuk form biodata
  biodata: (formData: any) => prepareBiodataForSubmission(formData),
  
  // Contoh untuk form pelatihan
  pelatihan: (formData: any) => preparePelatihanForSubmission(formData),
  
  // Contoh untuk form absensi
  absensi: (formData: any) => prepareAbsensiForSubmission(formData),
  
  // Contoh untuk form custom dengan field tanggal tertentu
  custom: (formData: any, dateFields: string[]) => 
    processFormDatesForSubmission(formData, dateFields)
};

const frontendTimezoneHelpers = {
  prepareBiodataForSubmission,
  preparePelatihanForSubmission,
  prepareAbsensiForSubmission,
  processFormDatesForSubmission,
  useIndonesiaTimezone,
  withIndonesiaTimezone,
  ExampleFormSubmission
};

export default frontendTimezoneHelpers;
