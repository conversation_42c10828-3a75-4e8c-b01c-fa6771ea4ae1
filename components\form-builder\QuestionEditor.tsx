import { useState, useRef } from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Trash } from "lucide-react";

// Define Question interface
interface Question {
  title?: string;
  type: string;
  options?: { choices: any[] } | null;
  isRequired?: boolean;
  [key: string]: any;
}

interface QuestionTypeOptionsProps {
  options: { choices: any[] };
  onChange: (field: string, value: any) => void;
}

interface QuestionEditorProps {
  question: Question;
  onSave: (question: Question) => void;
  onUpdate?: (question: Question) => void;
}

// Component for handling question options (choices)
const QuestionTypeOptions = ({ options, onChange }: QuestionTypeOptionsProps) => {
  const addOption = () => {
    const newChoices = [...(options.choices || []), { text: "", value: `option_${Date.now()}` }];
    onChange('options', { choices: newChoices });
  };

  const updateOption = (index: number, text: string) => {
    const newChoices = [...options.choices];
    newChoices[index] = { ...newChoices[index], text };
    onChange('options', { choices: newChoices });
  };

  const removeOption = (index: number) => {
    const newChoices = options.choices.filter((_, i) => i !== index);
    onChange('options', { choices: newChoices });
  };

  return (
    <div className="space-y-3">
      <Label>Pilihan</Label>
      {(options.choices || []).map((option, index) => (
        <div key={index} className="flex items-center space-x-2">
          <Input
            value={option.text || ''}
            onChange={(e) => updateOption(index, e.target.value)}
            placeholder={`Pilihan ${index + 1}`}
            className="flex-grow"
          />
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => removeOption(index)}
            type="button"
          >
            <Trash size={16} className="text-red-500" />
          </Button>
        </div>
      ))}
      <Button 
        variant="outline" 
        onClick={addOption} 
        type="button" 
        size="sm"
      >
        Tambah Pilihan
      </Button>
    </div>
  );
};

const QuestionEditor = ({ question, onSave, onUpdate }: QuestionEditorProps) => {
  // Initialize local state to manage question data
  const [localQuestion, setLocalQuestion] = useState<Question>(question);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  // Fungsi untuk menangani perubahan field
  const handleFieldChange = (field: string, value: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`QuestionEditor: Field ${field} changed to:`, value);
    }
    
    const updatedQuestion = {
      ...localQuestion,
      [field]: value
    };
    
    // Update state lokal
    setLocalQuestion(updatedQuestion);
    
    // Jika onUpdate tersedia, gunakan itu untuk update UI
    if (onUpdate) {
      onUpdate(updatedQuestion);
    }
    
    // Kirim perubahan ke API setelah penundaan singkat
    // untuk menghindari terlalu banyak permintaan
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }    saveTimeoutRef.current = setTimeout(() => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Sending updated question to API:`, updatedQuestion);
      }
      try {
        onSave(updatedQuestion);
      } catch (error) {
        console.error('Error in handleFieldChange:', error);
      }
    }, 1000);
  };
  // Fungsi untuk menangani perubahan tipe pertanyaan
  const handleTypeChange = (newType: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Changing question type to:', newType);
    }
    
    // Pastikan newType adalah salah satu dari nilai yang valid
    const validTypes = [
      'SHORT_TEXT', 'LONG_TEXT', 'SINGLE_CHOICE', 'MULTIPLE_CHOICE',
      'CHECKLIST', 'DROPDOWN', 'DATE', 'TIME', 'EMAIL', 'PHONE', 'NUMBER'
    ];
      // Normalisasi nilai jika diperlukan
    let normalizedType = newType;
    if (newType === 'CHECKBOX') {
      normalizedType = 'CHECKLIST';
      if (process.env.NODE_ENV === 'development') {
        console.log('Normalized CHECKBOX to CHECKLIST');
      }
    }
    
    if (!validTypes.includes(normalizedType)) {
      console.error(`Invalid question type: ${normalizedType}`);
      return;
    }
    
    // Cek apakah tipe pertanyaan membutuhkan options
    const needsOptions = ['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'CHECKLIST', 'DROPDOWN'].includes(normalizedType);
    
    // Salin options yang ada jika tipe sebelumnya dan tipe baru sama-sama membutuhkan options
    const currentNeedsOptions = ['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'CHECKLIST', 'DROPDOWN'].includes(localQuestion.type);
    
    let updatedOptions;
    if (needsOptions) {
      // Jika tipe baru membutuhkan options
      if (currentNeedsOptions && localQuestion.options) {
        // Gunakan options yang sudah ada jika tipe sebelumnya juga membutuhkan options
        updatedOptions = localQuestion.options;
      } else {
        // Buat options baru jika tipe sebelumnya tidak membutuhkan options
        updatedOptions = { choices: [] };
      }
    } else {
      // Jika tipe baru tidak membutuhkan options
      updatedOptions = undefined;
    }
    
    // Buat pertanyaan yang diperbarui dengan nilai yang sudah dinormalisasi
    const updatedQuestion = {
      ...localQuestion,
      type: normalizedType,
      options: updatedOptions    };

    if (process.env.NODE_ENV === 'development') {
      console.log('Updated question:', updatedQuestion);
    }

    // Kirim data lengkap ke API
    try {
      // Update state lokal terlebih dahulu
      setLocalQuestion(updatedQuestion);

      // Jika onUpdate tersedia, gunakan itu untuk update UI
      if (onUpdate) {
        onUpdate(updatedQuestion);
      }

      // Kirim perubahan ke API dengan objek yang lebih sederhana
      const questionToSave = {
        ...updatedQuestion,
        // Pastikan options adalah null jika tidak dibutuhkan
        options: needsOptions ? updatedOptions : null      };
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Saving question:', questionToSave);
      }
      onSave(questionToSave);
    } catch (error) {
      console.error('Error in handleTypeChange:', error);
    }
  };

  // Pastikan fungsi handleFieldChange digunakan di komponen
  return (
    <div className="space-y-4">
      {/* Input untuk judul pertanyaan */}
      <div>
        <Label htmlFor="question-title">Pertanyaan</Label>
        <Input
          id="question-title"
          value={localQuestion.title || ''}
          onChange={(e) => handleFieldChange('title', e.target.value)}
          placeholder="Masukkan pertanyaan"
          className="mt-1"
        />
      </div>
      
      {/* Pilihan tipe pertanyaan */}
      <div>
        <Label htmlFor="question-type">Tipe Pertanyaan</Label>
        <Select 
          value={localQuestion.type} 
          onValueChange={(value) => handleTypeChange(value)}
        >
          <SelectTrigger id="question-type" className="mt-1">
            <SelectValue placeholder="Pilih tipe pertanyaan" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="SHORT_TEXT">Teks Pendek</SelectItem>
            <SelectItem value="LONG_TEXT">Teks Panjang</SelectItem>
            <SelectItem value="SINGLE_CHOICE">Pilihan Tunggal</SelectItem>
            <SelectItem value="MULTIPLE_CHOICE">Pilihan Ganda</SelectItem>
            <SelectItem value="CHECKLIST">Checklist</SelectItem>
            <SelectItem value="DROPDOWN">Dropdown</SelectItem>
            <SelectItem value="DATE">Tanggal</SelectItem>
            <SelectItem value="TIME">Waktu</SelectItem>
            <SelectItem value="EMAIL">Email</SelectItem>
            <SelectItem value="PHONE">Telepon</SelectItem>
            <SelectItem value="NUMBER">Angka</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Opsi khusus berdasarkan tipe pertanyaan */}
      {['SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'CHECKLIST', 'DROPDOWN'].includes(localQuestion.type) && (
        <QuestionTypeOptions 
          options={localQuestion.options || { choices: [] }}
          onChange={handleFieldChange}
        />
      )}
      
      {/* Switch untuk required/optional */}
      <div className="flex items-center space-x-2">
        <Switch
          id="question-required"
          checked={localQuestion.isRequired || false}
          onCheckedChange={(checked) => handleFieldChange('isRequired', checked)}
        />
        <Label htmlFor="question-required">Wajib diisi</Label>
      </div>
      
      {/* Tombol untuk menyimpan perubahan */}
      <Button onClick={() => onSave(localQuestion)} className="mt-4">
        Simpan Perubahan
      </Button>
    </div>
  );
};

export default QuestionEditor;










