# Dependencies
node_modules
.pnp
.pnp.js

# Next.js build output
.next
out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea
.vscode
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Build artifacts
dist
build

# Testing
coverage

# Temporary files
*.log
*.tmp
.cache

# Prisma
prisma/.env
