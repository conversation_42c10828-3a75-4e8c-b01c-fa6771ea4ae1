# Upload File Permissions Fix

## Problem Identified
Uploaded files were being created without proper permissions, causing 404 errors when nginx tried to serve them. The files were created with restrictive permissions that prevented the web server from reading them.

## Solution Implemented

### 1. File Permission Updates Applied
Added `fs.chmod(filePath, 0o644)` calls to all upload API routes after successful file writes:

#### Fixed Routes:
- **`/app/api/absensi/upload-photo/route.ts`** - Attendance photo uploads
- **`/app/api/biodata/upload-photo/route.ts`** - Biodata photo uploads  
- **`/app/api/biodata/upload-foto/route.ts`** - Alternative biodata photo endpoint
- **`/app/api/pelatihan/[id]/upload-materi/route.ts`** - Training material uploads
- **`/app/api/pelatihan/[id]/materi/route.ts`** - Alternative material upload endpoint

### 2. Permission Settings
- **File permissions set to: 644 (rw-r--r--)**
  - Owner: read/write
  - Group: read
  - Others: read
- This allows nginx (running as www-data) to read and serve the files

### 3. Code Changes Applied

Each upload route now includes:
```typescript
import { writeFile, mkdir, chmod } from 'fs/promises';

// After file write operation:
await writeFile(filePath, optimizedImageBuffer);

// Set proper file permissions (644 - rw-r--r--)
await chmod(filePath, 0o644);
```

### 4. Production Deployment Considerations

For production environments, the deployment script (`deploy.sh`) already includes:
```bash
# Make sure nginx can read the files
sudo chown -R www-data:www-data public/uploads/
sudo chmod -R 755 public/uploads/

# Create upload directories if they don't exist
mkdir -p public/uploads/absensi/photos
mkdir -p public/uploads/biodata/photos
mkdir -p public/uploads/materi
```

## Testing the Fix

### Local Testing
1. Upload a file through any of the forms
2. Check file permissions:
   ```bash
   ls -la public/uploads/[category]/[file]
   ```
3. Verify files are accessible via browser

### Production Testing
1. Deploy the updated code
2. Upload test files
3. Verify nginx can serve the files
4. Check that no 404 errors occur

## File Structure
```
public/uploads/
├── absensi/
│   └── photos/
│       └── [pelatihan-id]/
│           └── [filename].jpg
├── biodata/
│   └── photos/
│       └── [pelatihan-id]/
│           └── [filename].jpg
└── materi/
    └── [filename].pdf
```

## Benefits of This Fix
1. **Resolves 404 errors** - Files are now readable by nginx
2. **Maintains security** - Files are not executable, only readable
3. **Works across environments** - Consistent permissions in dev and production
4. **Automatic application** - No manual permission fixes needed after uploads

## Notes
- The fix is applied automatically for all new uploads
- Existing files may need manual permission fixes using the deployment script commands
- The 644 permission is the standard for web-served files
- No additional ownership changes needed as files are created by the Node.js process
