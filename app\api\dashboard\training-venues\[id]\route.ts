import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - Get specific venue
export async function GET(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const venue = await prisma.training_venue.findUnique({
      where: { id: params.id }
    });

    if (!venue) {
      return NextResponse.json(
        { error: 'Venue tidak ditemukan' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      venue
    });

  } catch (error) {
    console.error('Get Training Venue Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data venue' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// PUT - Update training venue
export async function PUT(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const body = await request.json();
    const { nama, alamat, latitude, longitude, radius, is_active } = body;

    // Check if venue exists
    const existingVenue = await prisma.training_venue.findUnique({
      where: { id: params.id }
    });

    if (!existingVenue) {
      return NextResponse.json(
        { error: 'Venue tidak ditemukan' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (nama !== undefined) updateData.nama = nama;
    if (alamat !== undefined) updateData.alamat = alamat;
    if (latitude !== undefined) updateData.latitude = parseFloat(latitude);
    if (longitude !== undefined) updateData.longitude = parseFloat(longitude);
    if (radius !== undefined) {
      if (radius < 50 || radius > 500) {
        return NextResponse.json(
          { error: 'Radius harus antara 50-500 meter' },
          { status: 400 }
        );
      }
      updateData.radius = parseInt(radius);
    }
    if (is_active !== undefined) updateData.is_active = is_active;

    updateData.updatedAt = new Date();

    // Check if new name already exists (if name is being updated)
    if (nama && nama !== existingVenue.nama) {
      const nameExists = await prisma.training_venue.findFirst({
        where: { 
          nama,
          id: { not: params.id }
        }
      });

      if (nameExists) {
        return NextResponse.json(
          { error: 'Nama venue sudah digunakan' },
          { status: 400 }
        );
      }
    }

    // Update venue
    const venue = await prisma.training_venue.update({
      where: { id: params.id },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      venue,
      message: 'Venue berhasil diupdate'
    });

  } catch (error) {
    console.error('Update Training Venue Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengupdate venue' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// DELETE - Delete training venue
export async function DELETE(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Check if venue exists
    const existingVenue = await prisma.training_venue.findUnique({
      where: { id: params.id },
      include: { pelatihan: true }
    });

    if (!existingVenue) {
      return NextResponse.json(
        { error: 'Venue tidak ditemukan' },
        { status: 404 }
      );
    }

    // Check if venue is being used by any pelatihan
    if (existingVenue.pelatihan.length > 0) {
      return NextResponse.json(
        { error: 'Venue tidak dapat dihapus karena masih digunakan oleh pelatihan' },
        { status: 400 }
      );
    }

    // Delete venue
    await prisma.training_venue.delete({
      where: { id: params.id }
    });

    return NextResponse.json({
      success: true,
      message: 'Venue berhasil dihapus'
    });

  } catch (error) {
    console.error('Delete Training Venue Error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus venue' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
