import { redirect } from 'next/navigation';
import { getCurrentUser } from '../../../lib/auth';
import { prisma } from '../../../lib/prisma';
import { Metadata } from 'next';
import BiodataExport from '../../../components/BiodataExport';
import { getFilteredBiodata } from '../../../utils/biodataHelpers';
import FilterBar from '../../../components/FilterBar';
import Card from '../../../components/Card';
import Pagination from '../../../components/Pagination';

// Define type for searchParams
interface SearchParams {
  page?: string;
  limit?: string;
  pelatihan?: string;
  search?: string;
  [key: string]: string | undefined;
}

export const metadata: Metadata = {
  title: 'Manajemen Peserta',
  description: ''
};

export default async function BiodataPage(
  props: { 
    searchParams: Promise<SearchParams>
  }
) {
  const searchParams = await props.searchParams;
  const user = await getCurrentUser();

  if (!user) {
    redirect('/login');
  }

  // Get page and limit from search params
  const page = searchParams?.page || '1';
  const limit = searchParams?.limit || '20';
  const pelatihanId = searchParams?.pelatihan || null;
  const search = searchParams?.search || null;

  try {
    // Use the new helper function to get filtered biodata
    const { biodata, pelatihan, totalPages, currentPage } = await getFilteredBiodata(
      prisma,
      user.id,
      page,
      limit,
      pelatihanId,
      search
    );

    // Build options for the filter with id property as required by FilterBar
    const pelatihanOptions = pelatihan.map((p) => ({
      id: p.id,
      value: p.id,
      label: p.nama
    }));
    
    return (
      <div className="container px-4 mx-auto sm:px-6 lg:px-8">
        <div className="flex flex-col items-start justify-between gap-4 my-4 sm:my-6 sm:flex-row sm:items-center">
          <h1 className="text-xl font-semibold sm:text-2xl">Daftar Peserta</h1>
        </div>
        
        <Card className="mb-6">
          <FilterBar
            filters={[
              {
                name: 'pelatihan',
                label: 'Kegiatan',
                type: 'select',
                options: [{ id: 'all', value: '', label: 'Semua Kegiatan' }, ...pelatihanOptions]
              },
              {
                name: 'search',
                label: 'Cari Peserta',
                type: 'text',
                placeholder: 'Nama, email, atau jabatan'
              }
            ]}
            baseUrl="/dashboard/biodata"
            currentParams={searchParams}
          />
        </Card>
        
        <Card>          <div className="w-full overflow-x-auto">
            <BiodataExport 
              biodataList={biodata}
              pelatihanList={pelatihan}
              enableDelete={true}
            />
          </div>
          
          {totalPages > 1 && (
            <div className="mt-4 sm:mt-6">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                limit={Number(limit)}
                searchParams={searchParams}
                baseUrl="/dashboard/biodata"
              />
            </div>
          )}        </Card>
      </div>
    );
  } catch (_error) {
    return (
      <div className="container px-4 mx-auto sm:px-6 lg:px-8">
        <div className="flex flex-col items-start justify-between gap-4 my-4 sm:my-6 sm:flex-row sm:items-center">
          <h1 className="text-xl font-semibold sm:text-2xl">Daftar Peserta</h1>
        </div>
        
        <Card>
          <div className="p-4 my-4 text-red-700 bg-red-100 border border-red-300 rounded-md">
            <p>Terjadi kesalahan saat mengambil data peserta. Silakan coba lagi nanti.</p>
          </div>
        </Card>
      </div>
    );
  }
}
