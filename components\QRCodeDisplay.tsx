'use client';

import { QRCodeCanvas } from 'qrcode.react';
import React, { useRef, useState } from 'react';
import Button from './Button';
import Card from './Card';
import { getBaseUrl } from '../lib/constants';

interface QRCodeDisplayProps {
  value: string;
  title: string;
  url: string;
  size?: number;
  className?: string;
}

export default function QRCodeDisplay({ value, title, url, size = 128, className = '' }: QRCodeDisplayProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const qrRef = useRef<HTMLDivElement>(null);

  // Fungsi untuk descargar el QR como imagen PNG
  const downloadQRCode = () => {
    if (!qrRef.current) return;
    
    setIsGenerating(true);
    try {
      const canvas = qrRef.current.querySelector('canvas');
      if (!canvas) return;
        // Ensure we're using the correct base URL when generating the QR
      
      // Convertir canvas a imagen
      const image = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      
      // Nombre de archivo: QR_TipoLink_Nombre.png
      const filename = `QR_${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.png`;
      
      link.href = image;
      link.download = filename;
      link.click();
    } catch (error) {
      console.error('Error downloading QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className={`${className}`}>
      <h3 className="mb-2 text-sm font-medium text-gray-800 sm:text-base">{title}</h3>
      
      <div className="flex flex-col items-center space-y-3">
        {/* QR Code */}        <div 
          ref={qrRef} 
          className="p-2 bg-white border rounded shadow-sm"
        >
          <QRCodeCanvas 
            value={getBaseUrl() ? value.replace(/^https?:\/\/[^\/]+/, getBaseUrl()) : value} 
            size={size}
            level="M"
            includeMargin={false}
          />
        </div>
        
        {/* URL display - shortened for compactness */}
        <div className="w-full px-2 py-1.5 overflow-hidden text-xs text-center text-gray-500 break-all bg-gray-100 rounded sm:text-sm">
          {url.length > 40 ? url.substring(0, 40) + '...' : url}
        </div>
        
        {/* Actions - more compact buttons */}
        <div className="flex space-x-2">
          <Button 
            variant="primary" 
            size="sm"
            onClick={downloadQRCode}
            isLoading={isGenerating}
            className="py-1 text-xs"
          >
            Download
          </Button>
          
          <a
            href={value}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block"
          >
            <Button
              variant="secondary"
              size="sm"
              className="py-1 text-xs"
            >
              Buka Link
            </Button>
          </a>
        </div>
      </div>
    </Card>
  );
}
