'use client';

import React, { useRef, useCallback, useState, useEffect } from 'react';
import Image from 'next/image';

interface PhotoCaptureProps {
  onPhotoCapture: (photo: string) => void;
  isRequired?: boolean;
}

interface DebugInfo {
  timestamp: string;
  message: string;
}

const PhotoCapture: React.FC<PhotoCaptureProps> = ({ onPhotoCapture, isRequired = false }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cameraStarting, setCameraStarting] = useState(false);
  const [debugInfo, setDebugInfo] = useState<DebugInfo[]>([]);
  const [browserInfo, setBrowserInfo] = useState<{
    isChrome: boolean;
    isFirefox: boolean;
    isHTTPS: boolean;
    hasCameraAPI: boolean;
  }>({
    isChrome: false,
    isFirefox: false,
    isHTTPS: false,
    hasCameraAPI: false
  });
  // Debug logging function
  const addDebugInfo = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PhotoCapture ${timestamp}] ${message}`);
    }
    setDebugInfo(prev => [...prev.slice(-4), { timestamp, message }]); // Keep last 5 entries
  }, []);

  // Check browser compatibility
  useEffect(() => {
    const userAgent = navigator.userAgent;
    const isChrome = /Chrome/.test(userAgent) && !/Edge/.test(userAgent);
    const isFirefox = /Firefox/.test(userAgent);
    const isHTTPS = window.location.protocol === 'https:' || window.location.hostname === 'localhost';
    const hasCameraAPI = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);

    setBrowserInfo({ isChrome, isFirefox, isHTTPS, hasCameraAPI });
    addDebugInfo(`Browser: ${isChrome ? 'Chrome' : isFirefox ? 'Firefox' : 'Other'}, HTTPS: ${isHTTPS}, Camera API: ${hasCameraAPI}`);
  }, [addDebugInfo]);

  // Wait for video element to be available in DOM
  const waitForVideoElement = useCallback(async (maxRetries: number = 10): Promise<boolean> => {
    addDebugInfo(`Waiting for video element (max ${maxRetries} retries)`);
    
    for (let i = 0; i < maxRetries; i++) {
      if (videoRef.current) {
        addDebugInfo(`Video element found on attempt ${i + 1}`);
        return true;
      }
      addDebugInfo(`Video element not found, waiting... (attempt ${i + 1})`);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    addDebugInfo(`Video element not found after ${maxRetries} retries`);
    return false;
  }, [addDebugInfo]);

  const startCamera = useCallback(async () => {
    // Prevent multiple simultaneous camera start attempts
    if (cameraStarting || isLoading) {
      addDebugInfo('Camera start already in progress, skipping');
      return;
    }

    addDebugInfo('Starting camera...');
    setCameraStarting(true);
    setIsLoading(true);
    setError(null);

    try {
      // Wait for video element to be available
      const videoAvailable = await waitForVideoElement();
      if (!videoAvailable) {
        throw new Error('Video element not available after waiting');
      }

      addDebugInfo('Requesting camera access...');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: false
      });

      addDebugInfo('Camera access granted, setting up video stream');
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        
        await new Promise<void>((resolve, reject) => {
          if (videoRef.current) {
            videoRef.current.onloadedmetadata = () => {
              addDebugInfo('Video metadata loaded');
              resolve();
            };
            videoRef.current.onerror = (e) => {
              addDebugInfo(`Video error: ${e}`);
              reject(new Error('Video failed to load'));
            };
          } else {
            reject(new Error('Video element became unavailable'));
          }
        });

        await videoRef.current.play();
        setIsStreaming(true);
        addDebugInfo('Camera started successfully');
      } else {
        throw new Error('Video element became unavailable during setup');
      }
    } catch (error: any) {
      addDebugInfo(`Camera error: ${error.message}`);
      console.error('Error accessing camera:', error);
      
      let errorMessage = 'Gagal mengakses kamera: ';
      if (error.name === 'NotAllowedError') {
        errorMessage += 'Izin kamera ditolak. Silakan berikan izin dan coba lagi.';
      } else if (error.name === 'NotFoundError') {
        errorMessage += 'Kamera tidak ditemukan. Pastikan perangkat memiliki kamera.';
      } else if (error.name === 'NotSupportedError') {
        errorMessage += 'Browser tidak mendukung akses kamera.';
      } else if (error.name === 'NotReadableError') {
        errorMessage += 'Kamera sedang digunakan aplikasi lain.';
      } else if (error.message.includes('Video element not available')) {
        errorMessage += 'Komponen kamera belum siap. Silakan coba lagi.';
      } else if (!browserInfo.isHTTPS) {
        errorMessage += 'Akses kamera memerlukan koneksi HTTPS.';
      } else if (error.message.includes('refreshing')) {
        errorMessage += 'Silakan refresh halaman dan coba lagi.';
      } else {
        errorMessage += error.message;
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setCameraStarting(false);
    }
  }, [cameraStarting, isLoading, waitForVideoElement, browserInfo.isHTTPS, addDebugInfo]);

  const stopCamera = useCallback(() => {
    addDebugInfo('Stopping camera...');
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsStreaming(false);
    addDebugInfo('Camera stopped');
  }, [addDebugInfo]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) {
      addDebugInfo('Cannot capture: video or canvas not available');
      setError('Video atau canvas tidak tersedia');
      return;
    }

    addDebugInfo('Capturing photo...');
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) {
      addDebugInfo('Cannot get canvas context');
      setError('Tidak dapat mengakses canvas');
      return;
    }

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedPhoto(photoDataUrl);
    onPhotoCapture(photoDataUrl);
    stopCamera();
    addDebugInfo('Photo captured successfully');
  }, [onPhotoCapture, stopCamera, addDebugInfo]);

  const retakePhoto = useCallback(() => {
    addDebugInfo('Retaking photo...');
    setCapturedPhoto(null);
    startCamera();
  }, [startCamera, addDebugInfo]);

  const clearPhoto = useCallback(() => {
    addDebugInfo('Clearing photo...');
    setCapturedPhoto(null);
    onPhotoCapture('');
  }, [onPhotoCapture, addDebugInfo]);  // Cleanup on unmount
  useEffect(() => {
    const video = videoRef.current;
    return () => {
      if (video && video.srcObject) {
        const stream = video.srcObject as MediaStream;
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="mb-4">
          <h3 className="mb-2 text-lg font-semibold text-gray-800">
            Foto {isRequired && <span className="text-red-500">*</span>}
          </h3>
          
          {/* Browser compatibility info */}
          <div className="p-2 mb-3 text-xs text-gray-600 rounded bg-gray-50">
            <div className="grid grid-cols-2 gap-1">
              <div>Browser: {browserInfo.isChrome ? '✅ Chrome' : browserInfo.isFirefox ? '✅ Firefox' : '⚠️ Other'}</div>
              <div>HTTPS: {browserInfo.isHTTPS ? '✅ Ya' : '❌ Tidak'}</div>
              <div>Camera API: {browserInfo.hasCameraAPI ? '✅ Ada' : '❌ Tidak'}</div>
            </div>
          </div>

          {!capturedPhoto ? (
            <>
              {/* Video element - always rendered but hidden when not streaming */}
              <div className="relative mb-4">
                <video
                  ref={videoRef}
                  className={`w-full rounded-lg ${isStreaming ? 'block' : 'hidden'}`}
                  playsInline
                  muted
                />
                
                {!isStreaming && (
                  <div className="flex items-center justify-center w-full h-48 bg-gray-200 rounded-lg">
                    <div className="text-center">
                      <div className="mb-2 text-4xl">📷</div>
                      <p className="text-gray-600">Kamera belum aktif</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Camera controls */}
              <div className="flex gap-2">
                {!isStreaming ? (
                  <button
                    onClick={startCamera}
                    disabled={isLoading || cameraStarting}
                    className="flex-1 px-4 py-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading || cameraStarting ? 'Memulai...' : 'Mulai Kamera'}
                  </button>
                ) : (
                  <>
                    <button
                      onClick={capturePhoto}
                      className="flex-1 px-4 py-2 text-white bg-green-500 rounded-lg hover:bg-green-600"
                    >
                      📸 Ambil Foto
                    </button>
                    <button
                      onClick={stopCamera}
                      className="px-4 py-2 text-white bg-red-500 rounded-lg hover:bg-red-600"
                    >
                      Stop
                    </button>
                  </>
                )}
              </div>
            </>
          ) : (
            <>              {/* Captured photo display */}
              <div className="mb-4">
                <Image
                  src={capturedPhoto}
                  alt="Captured"
                  className="w-full rounded-lg"
                  width={500}
                  height={400}
                  unoptimized={true}
                />
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={retakePhoto}
                  className="flex-1 px-4 py-2 text-white bg-yellow-500 rounded-lg hover:bg-yellow-600"
                >
                  📷 Ambil Ulang
                </button>
                <button
                  onClick={clearPhoto}
                  className="px-4 py-2 text-white bg-red-500 rounded-lg hover:bg-red-600"
                >
                  🗑️ Hapus
                </button>
              </div>
            </>
          )}

          {/* Error display */}
          {error && (
            <div className="p-3 mt-4 bg-red-100 border border-red-300 rounded-lg">
              <p className="text-sm text-red-700">{error}</p>
              
              {/* Troubleshooting help */}
              <div className="mt-2 text-xs text-red-600">
                <p className="font-semibold">Tips mengatasi masalah:</p>
                <ul className="mt-1 space-y-1 list-disc list-inside">
                  <li>Pastikan browser mendukung kamera (Chrome/Firefox)</li>
                  <li>Berikan izin akses kamera saat diminta</li>
                  <li>Pastikan kamera tidak digunakan aplikasi lain</li>
                  <li>Coba refresh halaman dan ulangi</li>
                  {!browserInfo.isHTTPS && <li>Gunakan koneksi HTTPS untuk akses kamera</li>}
                </ul>
              </div>
            </div>
          )}

          {/* Debug info panel */}
          {process.env.NODE_ENV === 'development' && debugInfo.length > 0 && (
            <div className="p-2 mt-4 text-xs bg-gray-100 rounded">
              <details>
                <summary className="font-semibold cursor-pointer">Debug Info ({debugInfo.length})</summary>
                <div className="mt-2 space-y-1 overflow-y-auto max-h-32">
                  {debugInfo.map((info, index) => (
                    <div key={index} className="text-gray-600">
                      <span className="text-gray-500">{info.timestamp}:</span> {info.message}
                    </div>
                  ))}
                </div>
              </details>
            </div>
          )}
        </div>
      </div>

      {/* Hidden canvas for photo capture */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};

export default PhotoCapture;