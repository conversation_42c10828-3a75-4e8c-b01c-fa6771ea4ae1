'use client';

import { useCallback, useEffect, useState, Fragment, useRef } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';

type LogLevel = 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL';

interface ErrorLog {
  id: string;
  level: LogLevel;
  message: string;
  stack: string | null;
  path: string | null;
  method: string | null;
  userId: string | null;
  userAgent: string | null;
  ip: string | null;
  createdAt: string;
  resolved: boolean;
}

interface LogsResponse {
  logs: ErrorLog[];
  pagination: {
    currentPage: number;
    pageSize: number;
    totalPages: number;
    totalItems: number;
  };
}

export default function LogsContent() {
  const [logs, setLogs] = useState<ErrorLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,  // This is likely constant
    totalPages: 0,
    totalItems: 0,
  });

  // Store pageSize in a ref if it's not meant to change
  const pageSizeRef = useRef(pagination.pageSize);

  // Add states for log detail modal
  const [selectedLog, setSelectedLog] = useState<ErrorLog | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [selectedLevel, setSelectedLevel] = useState<string | null>(null);
  const [resolvedFilter, setResolvedFilter] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();

  // Updated fetchLogs to not depend on state variables, but rather take parameters
  const fetchLogs = useCallback(async (
    page: number,
    level: string | null,
    resolved: string | null,
    start: string | null,
    end: string | null,
    pageSize: number
  ) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams();
      if (level) queryParams.set('level', level);
      if (resolved) queryParams.set('resolved', resolved);
      if (start) queryParams.set('startDate', start);
      if (end) queryParams.set('endDate', end);
      queryParams.set('page', page.toString());
      queryParams.set('pageSize', pageSize.toString());
      
      const response = await fetch(`/api/logs?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch logs: " + response.statusText);
      }
      
      const data: LogsResponse = await response.json();
      setLogs(data.logs);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      // Remove console.error('Error fetching logs:', err);
    } finally {
      setIsLoading(false);
    }
  }, []); // No dependencies, so it won't cause re-renders

  // Parse search params only when they change
  useEffect(() => {
    const page = parseInt(searchParams.get('page') || '1', 10);
    const level = searchParams.get('level');
    const resolved = searchParams.get('resolved');
    const start = searchParams.get('startDate');
    const end = searchParams.get('endDate');
    
    // Update state
    setSelectedLevel(level);
    setResolvedFilter(resolved);
    setStartDate(start);
    setEndDate(end);
    setPagination(prev => ({ ...prev, currentPage: page }));
    
    // Use the ref value instead
    fetchLogs(page, level, resolved, start, end, pageSizeRef.current);
    
  }, [searchParams, fetchLogs]); // Remove pagination.pageSize from dependencies

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`?${params.toString()}`);
  };

  const handleFilterChange = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const params = new URLSearchParams();
    if (selectedLevel) params.set('level', selectedLevel);
    if (resolvedFilter) params.set('resolved', resolvedFilter);
    if (startDate) params.set('startDate', startDate);
    if (endDate) params.set('endDate', endDate);
    params.set('page', '1'); // Reset to first page on filter change
    
    router.push(`?${params.toString()}`);
  };

  const handleResolveLog = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/logs/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ resolved: !currentStatus }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update log: ${response.statusText}`);
      }
      
      // Fetch logs with current URL parameters instead of relying on state
      const page = parseInt(searchParams.get('page') || '1', 10);
      const level = searchParams.get('level');
      const resolved = searchParams.get('resolved');
      const start = searchParams.get('startDate');
      const end = searchParams.get('endDate');
      
      fetchLogs(page, level, resolved, start, end, pagination.pageSize);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      // Remove console.error('Error updating log:', err);
    }
  };

  // Add function to handle showing log details
  const handleShowDetails = (log: ErrorLog) => {
    setSelectedLog(log);
    setIsModalOpen(true);
  };

  const getLevelBadgeClass = (level: LogLevel) => {
    switch (level) {
      case 'INFO': return 'bg-blue-100 text-blue-800';
      case 'WARNING': return 'bg-yellow-100 text-yellow-800';
      case 'ERROR': return 'bg-red-100 text-red-800';
      case 'CRITICAL': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('id-ID', {
      dateStyle: 'medium',
      timeStyle: 'medium',
    }).format(date);
  };

  // Format stack trace for better readability
  const formatStackTrace = (stack: string | null) => {
    if (!stack) return null;
    
    // Replace newlines with HTML line breaks for display
    return stack.split('\n').map((line, index) => (
      <span key={index} className="block py-0.5">
        {line}
      </span>
    ));
  };

  if (isLoading) {
    return <div className="flex items-center justify-center p-8">
      <div className="w-8 h-8 border-4 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
      <span className="ml-2">Memuat log...</span>
    </div>;
  }

  return (
    <div className="space-y-6">
      {/* Filter Form */}
      <div className="p-4 bg-white rounded-lg shadow-sm">
        <h2 className="mb-4 text-lg font-medium">Filter Log</h2>
        <form onSubmit={handleFilterChange} className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <label htmlFor="level" className="block mb-1 text-sm font-medium text-gray-700">Level</label>
            <select
              id="level"
              value={selectedLevel || ''}
              onChange={(e) => setSelectedLevel(e.target.value || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Semua Level</option>
              <option value="INFO">INFO</option>
              <option value="WARNING">WARNING</option>
              <option value="ERROR">ERROR</option>
              <option value="CRITICAL">CRITICAL</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="resolved" className="block mb-1 text-sm font-medium text-gray-700">Status</label>
            <select
              id="resolved"
              value={resolvedFilter || ''}
              onChange={(e) => setResolvedFilter(e.target.value || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Semua Status</option>
              <option value="false">Belum Diselesaikan</option>
              <option value="true">Sudah Diselesaikan</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="startDate" className="block mb-1 text-sm font-medium text-gray-700">Tanggal Mulai</label>
            <input
              type="date"
              id="startDate"
              value={startDate || ''}
              onChange={(e) => setStartDate(e.target.value || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label htmlFor="endDate" className="block mb-1 text-sm font-medium text-gray-700">Tanggal Akhir</label>
            <input
              type="date"
              id="endDate"
              value={endDate || ''}
              onChange={(e) => setEndDate(e.target.value || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div className="flex items-end md:col-span-2 lg:col-span-4">
            <button
              type="submit"
              className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Terapkan Filter
            </button>
            
            <button
              type="button"
              onClick={() => {
                setSelectedLevel(null);
                setResolvedFilter(null);
                setStartDate(null);
                setEndDate(null);
                router.push('/dashboard/logs');
              }}
              className="px-4 py-2 ml-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Reset
            </button>
          </div>
        </form>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="p-4 text-red-700 bg-red-100 rounded-md">
          <p>{error}</p>
        </div>
      )}
      
      {/* Logs Table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Level</th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Pesan</th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Path</th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Waktu</th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Status</th>
              <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Aksi</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {logs.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                  Tidak ada data log yang ditemukan
                </td>
              </tr>
            ) : (
              logs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLevelBadgeClass(log.level)}`}>
                      {log.level}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="max-w-xs overflow-hidden text-sm text-gray-900 truncate">{log.message}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="max-w-xs overflow-hidden text-sm text-gray-500 truncate">
                      {log.path ? `${log.method} ${log.path}` : '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{formatDate(log.createdAt)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${log.resolved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                      {log.resolved ? 'Selesai' : 'Pending'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleResolveLog(log.id, log.resolved)}
                        className={`px-3 py-1 text-xs font-medium text-white rounded-md ${log.resolved ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-green-500 hover:bg-green-600'}`}
                      >
                        {log.resolved ? 'Tandai Belum Selesai' : 'Tandai Selesai'}
                      </button>
                      <button 
                        onClick={() => handleShowDetails(log)}
                        className="px-3 py-1 text-xs font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600">
                        Detail
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
          <div className="flex items-center text-sm text-gray-700">
            <span>
              Menampilkan {((pagination.currentPage - 1) * pagination.pageSize) + 1} - {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalItems)} dari {pagination.totalItems} log
            </span>
          </div>
          <div className="flex space-x-1">
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage <= 1}
              className={`relative inline-flex items-center px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md ${pagination.currentPage <= 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              Sebelumnya
            </button>
            {[...Array(pagination.totalPages)].map((_, i) => (
              <button
                key={i + 1}
                onClick={() => handlePageChange(i + 1)}
                className={`relative inline-flex items-center px-4 py-2 text-sm font-medium border ${pagination.currentPage === i + 1 ? 'bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'}`}
              >
                {i + 1}
              </button>
            ))}
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage >= pagination.totalPages}
              className={`relative inline-flex items-center px-4 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md ${pagination.currentPage >= pagination.totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              Berikutnya
            </button>
          </div>
        </div>
      )}

      {/* Log Detail Modal */}
      <Transition appear show={isModalOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setIsModalOpen(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-50" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex items-center justify-center min-h-full p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-4xl p-6 overflow-hidden text-left align-middle transition-all transform bg-white rounded-lg shadow-xl">
                  <div className="flex items-center justify-between mb-4">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      Detail Log
                    </Dialog.Title>
                    <button
                      type="button"
                      className="text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none"
                      onClick={() => setIsModalOpen(false)}
                    >
                      <span className="sr-only">Close</span>
                      <XMarkIcon className="w-6 h-6" aria-hidden="true" />
                    </button>
                  </div>
                  
                  {selectedLog && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Level</h4>
                          <div className="mt-1">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getLevelBadgeClass(selectedLog.level)}`}>
                              {selectedLog.level}
                            </span>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Status</h4>
                          <div className="mt-1">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedLog.resolved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                              {selectedLog.resolved ? 'Selesai' : 'Pending'}
                            </span>
                          </div>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Waktu</h4>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(selectedLog.createdAt)}</p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Request</h4>
                          <p className="mt-1 text-sm text-gray-900">{selectedLog.method} {selectedLog.path || '-'}</p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">IP Address</h4>
                          <p className="mt-1 text-sm text-gray-900">{selectedLog.ip || '-'}</p>
                        </div>
                        
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">User ID</h4>
                          <p className="mt-1 text-sm text-gray-900">{selectedLog.userId || '-'}</p>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">User Agent</h4>
                        <p className="mt-1 text-sm text-gray-900 break-words">{selectedLog.userAgent || '-'}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Pesan</h4>
                        <p className="mt-1 text-sm text-gray-900 break-words">{selectedLog.message}</p>
                      </div>
                      
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Stack Trace</h4>
                        <div className="p-3 mt-1 overflow-auto border border-gray-200 rounded bg-gray-50 max-h-96">
                          <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                            {formatStackTrace(selectedLog.stack)}
                          </pre>
                        </div>
                      </div>
                      
                      <div className="flex justify-end mt-6">
                        <button
                          type="button"
                          className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                          onClick={() => setIsModalOpen(false)}
                        >
                          Tutup
                        </button>
                      </div>
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
}
