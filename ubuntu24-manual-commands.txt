# ===============================================
#   UBUNTU 24.04 NGINX MANUAL FIX COMMANDS
# ===============================================
# Copy paste commands ini di SSH terminal Ubuntu 24.04

echo "🐧 Ubuntu 24.04 Manual Fix untuk Photo Upload 404"
echo "Nginx config: /etc/nginx/nginx.conf"
echo ""

# STEP 1: Find aplikasi Next.js
echo "🔍 STEP 1: Mencari aplikasi Next.js..."
find /home /var/www /opt /usr/local -name "server.js" -path "*/standalone/*" 2>/dev/null
find /home /var/www /opt -name "package.json" -exec grep -l "next" {} \; 2>/dev/null

# Set APP_DIR (update dengan hasil pencarian di atas)
APP_DIR="/var/www/html"  # UPDATE INI dengan path yang ditemukan!
echo "📁 Using app directory: $APP_DIR"

# STEP 2: Backup
echo ""
echo "💾 STEP 2: Membuat backup..."
BACKUP_DIR="/tmp/nginx-backup-$(date +%Y%m%d-%H%M%S)"
sudo mkdir -p $BACKUP_DIR
sudo cp /etc/nginx/nginx.conf $BACKUP_DIR/
echo "✅ Backup di: $BACKUP_DIR"

# STEP 3: Buat struktur folder
echo ""
echo "📁 STEP 3: Membuat struktur uploads..."
sudo mkdir -p $APP_DIR/public/uploads/absensi/photos
sudo chown -R www-data:www-data $APP_DIR/public/uploads
sudo chmod -R 755 $APP_DIR/public/uploads
echo "✅ Folder uploads dibuat"

# STEP 4: Perbaiki duplikasi jika ada
echo ""
echo "🔧 STEP 4: Cek duplikasi folder..."
if [ -d "$APP_DIR/public/public" ]; then
    echo "⚠️ Duplikasi ditemukan, memperbaiki..."
    sudo cp -r $APP_DIR/public/public/uploads/* $APP_DIR/public/uploads/ 2>/dev/null || true
    sudo rm -rf $APP_DIR/public/public
    echo "✅ Duplikasi diperbaiki"
else
    echo "✅ Tidak ada duplikasi"
fi

# STEP 5: Cek struktur nginx.conf
echo ""
echo "🔍 STEP 5: Analisis nginx structure..."
if grep -q "include.*sites-enabled" /etc/nginx/nginx.conf; then
    echo "📋 Menggunakan sites-enabled structure"
    sudo mkdir -p /etc/nginx/sites-available /etc/nginx/sites-enabled
    SITE_CONFIG="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
    USE_SITES="true"
elif grep -q "include.*conf.d" /etc/nginx/nginx.conf; then
    echo "📋 Menggunakan conf.d structure"
    sudo mkdir -p /etc/nginx/conf.d
    SITE_CONFIG="/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
    USE_SITES="false"
else
    echo "📋 Setup conf.d structure..."
    sudo mkdir -p /etc/nginx/conf.d
    # Tambahkan include jika belum ada
    if ! grep -q "include.*conf.d" /etc/nginx/nginx.conf; then
        sudo sed -i '/http {/a\    include /etc/nginx/conf.d/*.conf;' /etc/nginx/nginx.conf
    fi
    SITE_CONFIG="/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
    USE_SITES="false"
fi

echo "📝 Site config: $SITE_CONFIG"

# STEP 6: Buat konfigurasi site
echo ""
echo "⚙️ STEP 6: Membuat site config..."

# Backup existing config
if [ -f "$SITE_CONFIG" ]; then
    sudo cp "$SITE_CONFIG" "$BACKUP_DIR/"
fi

# Buat config baru
sudo tee "$SITE_CONFIG" > /dev/null << EOF
server {
    listen 80;
    listen [::]:80;
    server_name kegiatan.bpmpkaltim.id;

    # Static files untuk uploads
    location /uploads/ {
        root $APP_DIR/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        
        # Logging
        access_log /var/log/nginx/uploads_access.log;
        error_log /var/log/nginx/uploads_error.log;
        
        try_files \$uri \$uri/ =404;
    }

    # Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

echo "✅ Site config dibuat"

# STEP 7: Aktifkan site jika menggunakan sites-enabled
if [ "$USE_SITES" = "true" ]; then
    echo ""
    echo "🔗 STEP 7: Aktifkan site..."
    sudo ln -sf "$SITE_CONFIG" /etc/nginx/sites-enabled/
    
    # Nonaktifkan default jika tidak perlu
    if [ -f "/etc/nginx/sites-enabled/default" ]; then
        if ! grep -q "kegiatan.bpmpkaltim.id" /etc/nginx/sites-enabled/default; then
            sudo rm -f /etc/nginx/sites-enabled/default
            echo "✅ Default site dinonaktifkan"
        fi
    fi
    echo "✅ Site diaktifkan"
fi

# STEP 8: Test dan reload nginx
echo ""
echo "🧪 STEP 8: Test dan reload nginx..."
sudo nginx -t
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "✅ Nginx config OK dan direload"
else
    echo "❌ Nginx config error!"
    echo "🔄 Restoring backup..."
    sudo cp $BACKUP_DIR/nginx.conf /etc/nginx/nginx.conf
    sudo systemctl reload nginx
    exit 1
fi

# STEP 9: Test akses
echo ""
echo "🌐 STEP 9: Test akses..."

# Buat test file
echo "Test access file" | sudo tee $APP_DIR/public/uploads/test.txt > /dev/null
sudo chown www-data:www-data $APP_DIR/public/uploads/test.txt
sudo chmod 644 $APP_DIR/public/uploads/test.txt

# Test lokal
echo "Testing local access..."
curl -s -o /dev/null -w "Local Status: %{http_code}\n" "http://localhost/uploads/test.txt"

# Test external (jika ada curl)
echo "Testing external access..."
curl -s -o /dev/null -w "External Status: %{http_code}\n" "https://kegiatan.bpmpkaltim.id/uploads/test.txt" --max-time 10

# STEP 10: Summary
echo ""
echo "🎉 UBUNTU 24.04 FIX COMPLETED!"
echo ""
echo "✅ Folder: $APP_DIR/public/uploads/absensi/photos/"
echo "✅ Config: $SITE_CONFIG"  
echo "✅ Backup: $BACKUP_DIR"
echo ""

# Count photos
PHOTO_COUNT=$(find $APP_DIR/public/uploads -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l)
echo "📸 Photos found: $PHOTO_COUNT"

echo ""
echo "🔍 VERIFICATION:"
echo "1. Test admin panel upload"
echo "2. Check URL: https://kegiatan.bpmpkaltim.id/uploads/"
echo "3. Monitor: tail -f /var/log/nginx/uploads_error.log"
echo ""

echo "📋 QUICK COMMANDS:"
echo "• nginx -t"
echo "• systemctl status nginx" 
echo "• systemctl reload nginx"
echo "• tail -f /var/log/nginx/uploads_access.log"

# Cleanup
sudo rm -f $APP_DIR/public/uploads/test.txt

echo ""
echo "⚠️ If issues persist, restore backup:"
echo "sudo cp $BACKUP_DIR/nginx.conf /etc/nginx/nginx.conf"
echo "sudo systemctl reload nginx"
