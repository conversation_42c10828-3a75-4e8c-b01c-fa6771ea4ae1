@echo off
setlocal enabledelayedexpansion

:: Advanced CloudPanel Auto-Refresh Fix Deployment
:: Windows automation untuk upload dan execute script

title Advanced Auto-Refresh Fix - CloudPanel Ubuntu 24.04

echo.
echo ================================================
echo    ADVANCED AUTO-REFRESH FIX DEPLOYMENT
echo ================================================
echo.

:: Configuration
set VPS_IP=*************
set VPS_USER=root
set SCRIPT_NAME=advanced-cloudpanel-autorefresh-fix.sh
set LOCAL_SCRIPT=%~dp0%SCRIPT_NAME%
set REMOTE_PATH=/tmp/%SCRIPT_NAME%

:: Colors (basic for Windows)
echo [36m[INFO][0m Starting deployment process...

:: Check if script exists
if not exist "%LOCAL_SCRIPT%" (
    echo [31m[ERROR][0m Script tidak ditemukan: %LOCAL_SCRIPT%
    echo.
    echo Pastikan file %SCRIPT_NAME% ada di direktori yang sama.
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script found: %LOCAL_SCRIPT%

:: Step 1: Upload script via SCP
echo.
echo [35m[ACTION][0m Uploading script to VPS...
echo Command: scp "%LOCAL_SCRIPT%" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%

scp "%LOCAL_SCRIPT%" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to upload script
    echo.
    echo Troubleshooting:
    echo 1. Pastikan koneksi internet stabil
    echo 2. Cek IP VPS: %VPS_IP%
    echo 3. Pastikan SSH key sudah terkonfigurasi
    echo 4. Coba manual: scp %SCRIPT_NAME% %VPS_USER%@%VPS_IP%:/tmp/
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script uploaded successfully

:: Step 2: Make script executable and run
echo.
echo [35m[ACTION][0m Making script executable and running...

:: Connect via SSH and execute commands
ssh %VPS_USER%@%VPS_IP% "chmod +x %REMOTE_PATH% && %REMOTE_PATH%"

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Script execution failed
    echo.
    echo Untuk menjalankan manual:
    echo 1. ssh %VPS_USER%@%VPS_IP%
    echo 2. chmod +x %REMOTE_PATH%
    echo 3. %REMOTE_PATH%
    echo.
    pause
    exit /b 1
)

echo.
echo [32m[SUCCESS][0m Advanced auto-refresh fix deployed successfully!

:: Step 3: Test connection and monitoring
echo.
echo [35m[ACTION][0m Testing deployment...

:: Check services status
echo.
echo Checking services status...
ssh %VPS_USER%@%VPS_IP% "systemctl is-active nginx && systemctl is-active advanced-photo-monitor.service"

if %errorlevel% eq 0 (
    echo [32m[SUCCESS][0m All services are running!
) else (
    echo [33m[WARNING][0m Some services may not be running properly
)

:: Step 4: Show monitoring commands
echo.
echo [36m================================================[0m
echo [32m   DEPLOYMENT COMPLETED SUCCESSFULLY![0m
echo [36m================================================[0m
echo.
echo [36m[INFO][0m Monitoring Commands:
echo.
echo [33mService Status:[0m
echo   systemctl status advanced-photo-monitor
echo   systemctl status nginx
echo.
echo [33mLive Monitoring:[0m
echo   journalctl -u advanced-photo-monitor -f
echo   tail -f /var/log/advanced-photo-monitor.log
echo   tail -f /var/log/photo-monitor-stats.log
echo.
echo [33mTesting:[0m
echo   1. Upload foto baru di aplikasi
echo   2. Langsung cek detail absensi
echo   3. Foto harus muncul TANPA reload nginx
echo.
echo [33mQuick SSH Command:[0m
echo   ssh %VPS_USER%@%VPS_IP%
echo.

:: Step 5: Offer to open monitoring session
echo [36m[INFO][0m Apakah ingin membuka monitoring session? (y/n)
set /p choice="Choice: "

if /i "%choice%"=="y" (
    echo.
    echo [35m[ACTION][0m Opening monitoring session...
    echo Press Ctrl+C to exit monitoring
    echo.
    ssh %VPS_USER%@%VPS_IP% "journalctl -u advanced-photo-monitor -f"
) else (
    echo.
    echo [32m[SUCCESS][0m Deployment selesai!
    echo [36m[INFO][0m Silakan test upload foto di aplikasi.
)

echo.
pause
