@echo off
echo ========================================
echo  VPS Deployment Script for Windows
echo ========================================
echo.

REM Check if deployment archive exists
if not exist ".next\deployment.tar.gz" (
    echo Error: deployment.tar.gz not found!
    echo Please run: npm run build
    echo Then run: npm run fix-deployment
    pause
    exit /b 1
)

echo Found deployment archive: .next\deployment.tar.gz
echo Size: 
for %%I in (".next\deployment.tar.gz") do echo %%~zI bytes
echo.

echo Next steps to deploy to your VPS:
echo.
echo 1. Upload the deployment archive to your VPS:
echo    scp .next\deployment.tar.gz <EMAIL>:/path/to/deployment/
echo.
echo 2. SSH into your VPS:
echo    ssh <EMAIL>
echo.
echo 3. Extract the archive:
echo    cd /path/to/deployment
echo    tar -xzf deployment.tar.gz
echo.
echo 4. Update nginx configuration using nginx-config.txt
echo.
echo 5. Set proper permissions:
echo    sudo chown -R www-data:www-data public/uploads/
echo    sudo chmod -R 755 public/uploads/
echo.
echo 6. Start/restart your application:
echo    pm2 restart server.js
echo.
echo 7. Test photo upload functionality
echo.

echo Generated files for deployment:
echo - .next\deployment.tar.gz (main deployment archive)
echo - nginx-config.txt (nginx configuration)
echo - DEPLOYMENT_FIX_GUIDE.md (detailed guide)
echo.

pause
