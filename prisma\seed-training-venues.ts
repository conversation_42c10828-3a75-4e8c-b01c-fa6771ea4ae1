// Seed script to add default training venues
import { PrismaClient } from '@prisma/client';
import { DEFAULT_TRAINING_VENUES } from '../lib/geofencing';

const prisma = new PrismaClient();

async function seedTrainingVenues() {
  console.log('🌱 Seeding training venues...');

  try {
    const defaultVenues = DEFAULT_TRAINING_VENUES;    for (const venue of defaultVenues) {
      const existingVenue = await prisma.training_venue.findFirst({
        where: { nama: venue.name }
      });

      if (!existingVenue) {
        await prisma.training_venue.create({
          data: {
            nama: venue.name,
            alamat: venue.address || 'Alamat tidak tersedia',
            latitude: venue.center.lat,
            longitude: venue.center.lng,
            radius: venue.radius,
            is_active: true
          }
        });
        console.log(`✅ Added venue: ${venue.name}`);
      } else {
        console.log(`⏭️  Venue already exists: ${venue.name}`);
      }
    }

    console.log('🎉 Training venues seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding training venues:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedTrainingVenues()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedTrainingVenues };
