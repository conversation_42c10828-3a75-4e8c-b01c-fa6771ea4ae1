'use client';

import React, { useState, useEffect, use } from 'react';
import QRCode from 'react-qr-code';
import { useRouter } from 'next/navigation';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Plus, Save, Settings, Eye, Trash2, GripVertical, ArrowLeft, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { getBaseUrl } from '@/lib/constants';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { QuestionTypeOptions } from '@/components/form-builder/QuestionTypeOptions';
import { formatIndonesiaDate } from '../../../../../utils/dateUtils';

// Tipe data untuk pertanyaan
type Question = {
  id: string;
  questionText: string;
  questionType: string;
  isRequired: boolean;
  order: number;
  options?: any;
  validationRules?: any;
};

// Tipe data untuk form
type Form = {
  id: string;
  title: string;
  description?: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
  questions: Question[];
};

export default function FormEditorPage({ params }: { params: Promise<{ formId: string }> }) {
  const router = useRouter();
  
  const unwrappedParams = use(params);
  const { formId } = unwrappedParams;

  const [form, setForm] = useState<Form | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    const fetchForm = async () => {
      try {
        const response = await fetch(`/api/forms/${formId}`);
        if (response.ok) {
          const data = await response.json();
          setForm(data);
        } else {
          // Handle error - form not found
          router.push('/dashboard/forms');
        }
      } catch (error) {
        console.error('Error fetching form:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchForm();
  }, [formId, router]);

  const handleFormChange = (field: string, value: string) => {
    if (!form) return;

    setForm({
      ...form,
      [field]: value,
    });
  };

  const handleAddQuestion = () => {
    if (!form) return;

    const newQuestion: Question = {
      id: `temp_${Date.now()}`, // Temporary ID until saved
      questionText: 'Pertanyaan baru',
      questionType: 'SHORT_TEXT',
      isRequired: false,
      order: form.questions.length,
    };

    setForm({
      ...form,
      questions: [...form.questions, newQuestion],
    });
  };

  const handleQuestionChange = (questionId: string, field: string, value: any) => {
    if (!form) return;

    setForm({
      ...form,
      questions: form.questions.map(q =>
        q.id === questionId ? { ...q, [field]: value } : q
      ),
    });
  };

  const handleDeleteQuestion = (questionId: string) => {
    if (!form) return;

    setForm({
      ...form,
      questions: form.questions.filter(q => q.id !== questionId)
        .map((q, index) => ({ ...q, order: index })),
    });
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination || !form) return;

    const questions = Array.from(form.questions);
    const [reorderedItem] = questions.splice(result.source.index, 1);
    questions.splice(result.destination.index, 0, reorderedItem);

    // Update order property
    const updatedQuestions = questions.map((q, index) => ({
      ...q,
      order: index,
    }));

    setForm({
      ...form,
      questions: updatedQuestions,
    });
  };

  const handleSaveForm = async () => {
    if (!form) return;

    setIsSaving(true);
    setSaveMessage('');

    try {
      // Buat salinan form dan ubah tipe pertanyaan CHECKLIST menjadi MULTIPLE_CHOICE
      // untuk kompatibilitas jika migrasi belum dijalankan
      const formToSave = {
        ...form,
        questions: form.questions.map(q => ({
          ...q,
          questionType: q.questionType === 'CHECKLIST' ? 'MULTIPLE_CHOICE' : q.questionType
        }))
      };

      const response = await fetch(`/api/forms/${formId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formToSave),
      });

      if (response.ok) {
        const updatedForm = await response.json();
        setForm(updatedForm);
        setSaveMessage('Formulir berhasil disimpan');

        // Clear message after 3 seconds
        setTimeout(() => {
          setSaveMessage('');
        }, 3000);
      }
    } catch (error) {
      console.error('Error saving form:', error);
      setSaveMessage('Gagal menyimpan formulir');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublishForm = async () => {
    if (!form) return;

    try {
      const response = await fetch(`/api/forms/${formId}/publish`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPublished: !form.isPublished }),
      });

      if (response.ok) {
        const updatedForm = await response.json();
        setForm(updatedForm);
      }
    } catch (error) {
      console.error('Error publishing form:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="container flex items-center justify-center h-64 py-6 mx-auto">
        <p>Memuat formulir...</p>
      </div>
    );
  }

  if (!form) {
    return (
      <div className="container py-6 mx-auto">
        <Alert variant="destructive">
          <AlertDescription>
            Formulir tidak ditemukan. Silakan kembali ke daftar formulir.
          </AlertDescription>
        </Alert>
        <Button className="mt-4" onClick={() => router.push('/dashboard/forms')}>
          Kembali ke Daftar Formulir
        </Button>
      </div>
    );
  }

  return (
    <div className="container py-6 mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button variant="ghost" onClick={() => router.push('/dashboard/forms')}>
            <ArrowLeft className="w-4 h-4 mr-1" />
            Kembali
          </Button>
          <h1 className="text-2xl font-bold">{form.title}</h1>
        </div>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/dashboard/forms/${formId}/preview`)}
                >
                  <Eye className="w-4 h-4 mr-1" />
                  Pratinjau
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Lihat pratinjau formulir</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Button
            variant={form.isPublished ? "destructive" : "default"}
            size="sm"
            onClick={handlePublishForm}
          >
            {form.isPublished ? 'Batalkan Publikasi' : 'Publikasikan'}
          </Button>

          <Button
            variant="default"
            size="sm"
            onClick={handleSaveForm}
            disabled={isSaving}
          >
            <Save className="w-4 h-4 mr-1" />
            {isSaving ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </div>
      </div>

      {saveMessage && (
        <Alert className={saveMessage.includes('berhasil') ? 'bg-green-50 border-green-200 text-green-800 mb-4' : 'bg-red-50 border-red-200 text-red-800 mb-4'}>
          <AlertDescription>{saveMessage}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        <div className="lg:col-span-3">
          <Card className="mb-6">
            <CardHeader className="pb-2">
              <Input
                value={form.title}
                onChange={(e) => handleFormChange('title', e.target.value)}
                placeholder="Judul Formulir"
                className="px-0 text-xl font-bold border-none focus-visible:ring-0"
              />
            </CardHeader>
            <CardContent>
              <Textarea
                value={form.description || ''}
                onChange={(e) => handleFormChange('description', e.target.value)}
                placeholder="Deskripsi formulir (opsional)"
                className="px-0 border-none resize-none focus-visible:ring-0"
              />
            </CardContent>
          </Card>

          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="questions">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-4"
                >
                  {form.questions.length === 0 ? (
                    <div className="py-12 text-center border border-gray-300 border-dashed rounded-lg bg-gray-50">
                      <p className="mb-4 text-gray-500">Belum ada pertanyaan. Tambahkan pertanyaan pertama Anda.</p>
                      <Button onClick={handleAddQuestion}>
                        <Plus className="w-4 h-4 mr-1" />
                        Tambah Pertanyaan
                      </Button>
                    </div>
                  ) : (
                    form.questions.map((question, index) => (
                      <Draggable key={question.id} draggableId={question.id} index={index}>
                        {(provided) => (
                          <Card
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="relative"
                          >
                            <div
                              {...provided.dragHandleProps}
                              className="absolute text-gray-400 -translate-y-1/2 left-2 top-1/2 cursor-grab"
                            >
                              <GripVertical className="w-5 h-5" />
                            </div>
                            <CardHeader className="pb-2 pl-10">
                              <div className="flex items-start justify-between">
                                <Input
                                  value={question.questionText}
                                  onChange={(e) => handleQuestionChange(question.id, 'questionText', e.target.value)}
                                  placeholder="Teks pertanyaan"
                                  className="px-0 text-base font-medium border-none focus-visible:ring-0"
                                />
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteQuestion(question.id)}
                                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </CardHeader>
                            <CardContent className="pb-2 pl-10">
                              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div>
                                  <Label htmlFor={`question-type-${question.id}`}>Tipe Pertanyaan</Label>
                                  <Select
                                    value={question.questionType}
                                    onValueChange={(value) => handleQuestionChange(question.id, 'questionType', value)}
                                  >
                                    <SelectTrigger id={`question-type-${question.id}`}>
                                      <SelectValue placeholder="Pilih tipe pertanyaan" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="SHORT_TEXT">Teks Pendek</SelectItem>
                                      <SelectItem value="LONG_TEXT">Teks Panjang</SelectItem>
                                      <SelectItem value="SINGLE_CHOICE">Pilihan Tunggal</SelectItem>
                                      <SelectItem value="MULTIPLE_CHOICE">Pilihan Ganda</SelectItem>
                                      <SelectItem value="DROPDOWN">Dropdown</SelectItem>
                                      <SelectItem value="DATE">Tanggal</SelectItem>
                                      <SelectItem value="TIME">Waktu</SelectItem>
                                      <SelectItem value="EMAIL">Email</SelectItem>
                                      <SelectItem value="PHONE">Nomor Telepon</SelectItem>
                                      <SelectItem value="NUMBER">Angka</SelectItem>
                                      <SelectItem value="CHECKLIST">Ceklist</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div className="flex items-center mt-auto space-x-2">
                                  <Switch
                                    id={`required-${question.id}`}
                                    checked={question.isRequired}
                                    onCheckedChange={(checked) => handleQuestionChange(question.id, 'isRequired', checked)}
                                  />
                                  <Label htmlFor={`required-${question.id}`}>Wajib diisi</Label>
                                </div>
                              </div>

                              {/* Opsi khusus berdasarkan tipe pertanyaan */}
                              <div className="mt-4">
                                <QuestionTypeOptions
                                  question={question}
                                  onChange={(field, value) => handleQuestionChange(question.id, field, value)}
                                />
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </Draggable>
                    ))
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>

          <div className="mt-4">
            <Button onClick={handleAddQuestion} className="w-full">
              <Plus className="w-4 h-4 mr-1" />
              Tambah Pertanyaan
            </Button>
          </div>
        </div>

        <div className="lg:col-span-1">
          <Card className="sticky top-6">
            <CardHeader>
              <div className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                <h3 className="font-medium">Pengaturan Formulir</h3>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="general" className="w-full">
                <TabsList className="w-full">
                  <TabsTrigger value="general" className="flex-1">Umum</TabsTrigger>
                  <TabsTrigger value="sharing" className="flex-1">Berbagi</TabsTrigger>
                </TabsList>
                <TabsContent value="general" className="mt-4 space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="form-published">Status Publikasi</Label>
                      <Switch
                        id="form-published"
                        checked={form.isPublished}
                        onCheckedChange={handlePublishForm}
                      />
                    </div>
                    <p className="text-sm text-gray-500">
                      {form.isPublished
                        ? 'Formulir ini dipublikasikan dan dapat diakses oleh responden.'
                        : 'Formulir ini masih dalam mode draft dan tidak dapat diakses oleh responden.'}
                    </p>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <Label>Informasi Formulir</Label>                    <p className="text-sm text-gray-500">
                      Dibuat: {formatIndonesiaDate(new Date(form.createdAt))}
                      <br />
                      Diperbarui: {formatIndonesiaDate(new Date(form.updatedAt))}
                      <br />
                      Jumlah Pertanyaan: {form.questions.length}
                    </p>
                  </div>
                </TabsContent>
                <TabsContent value="sharing" className="mt-4 space-y-6 sharing-tab">
                  <div className="space-y-2">
                    <Label htmlFor="form-url">URL Formulir</Label>
                    <div className="flex">
                      <Input
                        id="form-url"
                        value={`${getBaseUrl()}/forms/${form.id}`}
                        readOnly
                        className="rounded-r-none"
                      />
                      <Button
                        variant="outline"
                        className={`rounded-l-none ${copySuccess ? 'bg-green-100 text-green-800 border-green-300' : ''}`}
                        onClick={() => {
                          navigator.clipboard.writeText(`${getBaseUrl()}/forms/${form.id}`);
                          setCopySuccess(true);
                          setTimeout(() => setCopySuccess(false), 2000);
                        }}
                      >
                        {copySuccess ? (
                          <>
                            <Check className="w-4 h-4 mr-1" />
                            Tersalin!
                          </>
                        ) : (
                          'Salin Link'
                        )}
                      </Button>
                    </div>
                    <p className="text-sm text-gray-500">
                      Bagikan URL ini kepada responden untuk mengisi formulir.
                    </p>
                  </div>

                  <div className="space-y-3">
                    <Label>QR Code Formulir</Label>
                    <div className="flex flex-col items-center p-4 bg-white border rounded-md">
                      <QRCode
                        value={`${getBaseUrl()}/forms/${form.id}`}
                        size={180}
                        level="H"
                        className="mx-auto"
                      />
                      <p className="mt-3 text-sm text-center text-gray-500">
                        Scan QR code ini untuk mengakses formulir
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-3"
                        onClick={() => {
                          // Membuat canvas dari QR code
                          const canvas = document.createElement('canvas');
                          const svg = document.querySelector('.sharing-tab svg');
                          if (svg) {
                            const svgData = new XMLSerializer().serializeToString(svg);
                            const img = new Image();
                            img.onload = () => {
                              canvas.width = img.width;
                              canvas.height = img.height;
                              const ctx = canvas.getContext('2d');
                              if (ctx) {
                                ctx.fillStyle = 'white';
                                ctx.fillRect(0, 0, canvas.width, canvas.height);
                                ctx.drawImage(img, 0, 0);
                                const pngFile = canvas.toDataURL('image/png');

                                // Download QR code
                                const downloadLink = document.createElement('a');
                                downloadLink.download = `qrcode-form-${form.id}.png`;
                                downloadLink.href = pngFile;
                                downloadLink.click();
                              }
                            };
                            img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
                          }
                        }}
                      >
                        Unduh QR Code
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push(`/dashboard/forms/${formId}/responses`)}
              >
                Lihat Respons
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
