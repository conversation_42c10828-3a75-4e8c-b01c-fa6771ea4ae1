'use client';

import { useEffect, useState } from 'react';
import { user } from '@prisma/client';
import { formatDate } from '../../../../utils/helpers';

export default function UserDetail({ userId }: { userId: string }) {
  const [user, setUser] = useState<user | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user data');
        }
        
        const text = await response.text();
        if (!text) {
          throw new Error('Empty response from server');
        }
        
        const userData = JSON.parse(text);
        setUser(userData);
      } catch (err) {
        console.error('Error fetching user:', err);
        setError('Gagal memuat data pengguna');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-4 py-3 text-red-700 border border-red-200 rounded-md bg-red-50">
        <p>{error}</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="px-4 py-3 text-yellow-700 border border-yellow-200 rounded-md bg-yellow-50">
        <p>Pengguna tidak ditemukan</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="p-4 rounded-lg bg-gray-50">
          <h2 className="mb-1 text-sm font-medium text-gray-500">Nama</h2>
          <p className="text-lg font-medium text-gray-900">{user.name}</p>
        </div>
        
        <div className="p-4 rounded-lg bg-gray-50">
          <h2 className="mb-1 text-sm font-medium text-gray-500">Email</h2>
          <p className="text-lg font-medium text-gray-900">{user.email}</p>
        </div>
        
        <div className="p-4 rounded-lg bg-gray-50">
          <h2 className="mb-1 text-sm font-medium text-gray-500">Role</h2>
          <p className="text-lg font-medium text-gray-900">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              user.role === 'ADMIN' 
                ? 'bg-purple-100 text-purple-800' 
                : 'bg-blue-100 text-blue-800'
            }`}>
              {user.role}
            </span>
          </p>
        </div>
        
        <div className="p-4 rounded-lg bg-gray-50">
          <h2 className="mb-1 text-sm font-medium text-gray-500">Tanggal Dibuat</h2>
          <p className="text-lg font-medium text-gray-900">{formatDate(user.createdAt)}</p>
        </div>
      </div>
    </div>
  );
}
