# =============================================
#   IMMEDIATE MANUAL FIX - Copy Paste Commands
# =============================================

echo "🎯 MASALAH: Files uploaded ke public/uploads/ tapi nginx tidak bisa serve"
echo "🎯 SOLUSI: Tambah nginx location block untuk /uploads/ path"
echo ""

# STEP 1: Find aplikasi Next.js
echo "📁 STEP 1: Mencari lokasi aplikasi..."
find /home /var/www /opt -name "server.js" -path "*/standalone/*" 2>/dev/null
find /home /var/www /opt -name "package.json" -exec grep -l "next" {} \; 2>/dev/null

# Set APP_DIR (update dengan hasil pencarian)
APP_DIR="/var/www/html"  # UPDATE INI!
echo "📂 Using: $APP_DIR"

# STEP 2: Pastikan folder uploads ada
echo ""
echo "📁 STEP 2: Memastikan struktur folder..."
sudo mkdir -p $APP_DIR/public/uploads/absensi/photos
sudo chown -R www-data:www-data $APP_DIR/public/uploads
sudo chmod -R 755 $APP_DIR/public/uploads
echo "✅ Folder dan permissions OK"

# Count existing photos  
PHOTOS=$(find $APP_DIR/public/uploads -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l)
echo "📸 Existing photos: $PHOTOS"

# STEP 3: Backup nginx configs
echo ""
echo "💾 STEP 3: Backup nginx configs..."
BACKUP_DIR="/tmp/nginx-backup-$(date +%Y%m%d-%H%M%S)"
sudo mkdir -p $BACKUP_DIR
sudo cp /etc/nginx/nginx.conf $BACKUP_DIR/
echo "✅ Backup: $BACKUP_DIR"

# STEP 4: Find nginx config for domain
echo ""
echo "🔍 STEP 4: Mencari config untuk kegiatan.bpmpkaltim.id..."

# Check possible locations
for config in "/etc/nginx/sites-available/kegiatan.bpmpkaltim.id" "/etc/nginx/sites-available/default" "/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf" "/etc/nginx/conf.d/default.conf"; do
    if [ -f "$config" ]; then
        if grep -q "kegiatan.bpmpkaltim.id\|bpmpkaltim" "$config" 2>/dev/null; then
            echo "✅ Config found: $config"
            CONFIG_FILE="$config"
            break
        fi
    fi
done

# If no config found, create new one
if [ -z "$CONFIG_FILE" ]; then
    echo "⚠️ Config tidak ditemukan, membuat baru..."
    
    if [ -d "/etc/nginx/sites-available" ]; then
        CONFIG_FILE="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
        sudo mkdir -p /etc/nginx/sites-enabled
        USE_SITES="true"
    else
        CONFIG_FILE="/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf"
        sudo mkdir -p /etc/nginx/conf.d
        USE_SITES="false"
        
        # Add include if missing
        if ! grep -q "include.*conf.d" /etc/nginx/nginx.conf; then
            sudo sed -i '/http {/a\    include /etc/nginx/conf.d/*.conf;' /etc/nginx/nginx.conf
        fi
    fi
    
    echo "📝 Creating: $CONFIG_FILE"
    
    # Create new config
    sudo tee "$CONFIG_FILE" > /dev/null << EOF
server {
    listen 80;
    listen [::]:80;
    server_name kegiatan.bpmpkaltim.id;

    # Static files untuk uploads
    location /uploads/ {
        root $APP_DIR/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
        try_files \$uri \$uri/ =404;
    }

    # Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    # Enable site if using sites-enabled
    if [ "$USE_SITES" = "true" ]; then
        sudo ln -sf "$CONFIG_FILE" /etc/nginx/sites-enabled/
        echo "✅ Site enabled"
    fi
    
else
    echo "🔧 STEP 5: Update existing config..."
    
    # Backup existing config
    sudo cp "$CONFIG_FILE" "$BACKUP_DIR/"
    
    # Check if location /uploads/ already exists
    if grep -q "location /uploads/" "$CONFIG_FILE"; then
        echo "⚠️ Location /uploads/ sudah ada, update root path..."
        
        # Update root path
        sudo sed -i "/location \/uploads\//,/}/ s|root.*|root $APP_DIR/public;|" "$CONFIG_FILE"
        echo "✅ Root path updated"
        
    else
        echo "➕ Menambahkan location /uploads/..."
        
        # Add location block before location /
        sudo sed -i '/location \/ {/i\
    # Static files untuk uploads\
    location /uploads/ {\
        root '"$APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        add_header Access-Control-Allow-Origin "*";\
        try_files $uri $uri/ =404;\
    }\
' "$CONFIG_FILE"
        
        echo "✅ Location /uploads/ added"
    fi
fi

# STEP 6: Test dan reload nginx
echo ""
echo "🧪 STEP 6: Test dan reload nginx..."
sudo nginx -t
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "✅ Nginx reloaded successfully"
else
    echo "❌ Nginx config error!"
    exit 1
fi

# STEP 7: Test akses
echo ""
echo "🌐 STEP 7: Test akses static files..."

# Create test file
sudo mkdir -p $APP_DIR/public/uploads/test
echo "Test file $(date)" | sudo tee $APP_DIR/public/uploads/test/test.txt > /dev/null
sudo chown www-data:www-data $APP_DIR/public/uploads/test/test.txt

# Test local
LOCAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost/uploads/test/test.txt" 2>/dev/null || echo "000")
echo "🔍 Local test: $LOCAL_STATUS"

# Test external  
EXTERNAL_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://kegiatan.bpmpkaltim.id/uploads/test/test.txt" --max-time 10 2>/dev/null || echo "000")
echo "🔍 External test: $EXTERNAL_STATUS"

# Test specific photo directory
PHOTO_DIR_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/" --max-time 10 2>/dev/null || echo "000")
echo "🔍 Photo directory: $PHOTO_DIR_STATUS"

# Cleanup
sudo rm -f $APP_DIR/public/uploads/test/test.txt
sudo rmdir $APP_DIR/public/uploads/test 2>/dev/null || true

# STEP 8: Summary
echo ""
echo "🎉 IMMEDIATE FIX COMPLETED!"
echo ""
echo "✅ App directory: $APP_DIR"
echo "✅ Config file: $CONFIG_FILE"
echo "✅ Backup: $BACKUP_DIR"
echo "✅ Photos: $PHOTOS existing"
echo ""
echo "📊 TEST RESULTS:"
echo "• Local: $LOCAL_STATUS"
echo "• External: $EXTERNAL_STATUS"  
echo "• Photo dir: $PHOTO_DIR_STATUS"
echo ""
echo "🔍 IMMEDIATE VERIFICATION:"
echo "1. Test: curl -I https://kegiatan.bpmpkaltim.id/uploads/"
echo "2. Upload foto baru di admin panel"
echo "3. Check foto tidak 404 lagi"
echo ""
echo "📋 TROUBLESHOOTING:"
echo "• tail -f /var/log/nginx/access.log | grep uploads"
echo "• tail -f /var/log/nginx/error.log"
echo ""
echo "⚠️ RESTORE BACKUP if needed:"
echo "sudo cp $BACKUP_DIR/nginx.conf /etc/nginx/nginx.conf"
echo "sudo systemctl reload nginx"
