#!/bin/bash

# CloudPanel Ubuntu 24.04 - Fix Auto Refresh untuk Photo Upload
# Mengatasi masalah harus reload nginx setiap upload foto baru

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_action() {
    echo -e "${PURPLE}[ACTION]${NC} $1"
}

echo -e "${GREEN}"
echo "============================================"
echo "   CLOUDPANEL NGINX AUTO-REFRESH FIX"
echo "============================================"
echo -e "${NC}"

# STEP 1: Detect CloudPanel structure
log_info "Mendeteksi struktur CloudPanel..."

# CloudPanel biasanya menggunakan struktur /home/<USER>/htdocs
CLOUDPANEL_SITES=()
for domain_dir in /home/<USER>/htdocs; do
    if [ -d "$domain_dir" ]; then
        CLOUDPANEL_SITES+=("$domain_dir")
        log_success "CloudPanel site ditemukan: $domain_dir"
    fi
done

# Cari juga di struktur standar
POSSIBLE_APPS=()
while IFS= read -r -d '' app_path; do
    app_dir=$(dirname "$app_path")
    POSSIBLE_APPS+=("$app_dir")
    log_success "Next.js app ditemukan: $app_dir"
done < <(find /home /var/www /opt -name "server.js" -o -name "package.json" -print0 2>/dev/null | head -20 || true)

# Gabungkan semua kemungkinan
ALL_APPS=("${CLOUDPANEL_SITES[@]}" "${POSSIBLE_APPS[@]}")

if [ ${#ALL_APPS[@]} -eq 0 ]; then
    log_warning "Menggunakan default path..."
    ALL_APPS=("/var/www/html" "/home/<USER>/htdocs")
fi

# STEP 2: Backup
BACKUP_DIR="/tmp/cloudpanel-nginx-fix-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
log_info "Backup directory: $BACKUP_DIR"

# STEP 3: Fix setiap aplikasi yang ditemukan
for APP_DIR in "${ALL_APPS[@]}"; do
    if [ ! -d "$APP_DIR" ]; then
        continue
    fi
    
    echo ""
    log_action "Memproses aplikasi: $APP_DIR"
    
    # Pastikan folder uploads ada
    if [ ! -d "$APP_DIR/public/uploads" ]; then
        mkdir -p "$APP_DIR/public/uploads/absensi/photos"
        log_success "Folder uploads dibuat"
    fi
    
    # STEP 3.1: Fix permissions untuk CloudPanel
    log_action "Mengatur permissions CloudPanel..."
    
    # CloudPanel menggunakan user yang sama dengan domain
    DOMAIN_USER=$(stat -c '%U' "$APP_DIR" 2>/dev/null || echo "www-data")
    DOMAIN_GROUP=$(stat -c '%G' "$APP_DIR" 2>/dev/null || echo "www-data")
    
    log_info "Domain user: $DOMAIN_USER, group: $DOMAIN_GROUP"
    
    # Set ownership dan permissions yang tepat
    chown -R "$DOMAIN_USER:$DOMAIN_GROUP" "$APP_DIR/public/uploads"
    
    # Permissions khusus untuk real-time file access
    chmod -R 755 "$APP_DIR/public/uploads"
    
    # Set SGID pada folder agar file baru otomatis mengikuti group
    find "$APP_DIR/public/uploads" -type d -exec chmod g+s {} \;
    
    log_success "Permissions CloudPanel diatur"
    
    # STEP 3.2: Hitung foto yang ada
    PHOTO_COUNT=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" 2>/dev/null | wc -l)
    log_info "📸 Total foto: $PHOTO_COUNT"
    
done

# STEP 4: Fix nginx configuration untuk CloudPanel
log_action "Mengoptimalkan nginx config untuk real-time access..."

# Cari file config nginx CloudPanel
NGINX_CONFIGS=()
for config_path in \
    "/etc/nginx/sites-available/kegiatan.bpmpkaltim.id" \
    "/etc/nginx/sites-available/kegiatan-bpmpkaltim-id.conf" \
    "/etc/nginx/conf.d/kegiatan.bpmpkaltim.id.conf" \
    "/etc/nginx/conf.d/kegiatan-bpmpkaltim-id.conf" \
    "/etc/nginx/sites-available/default"; do
    
    if [ -f "$config_path" ]; then
        NGINX_CONFIGS+=("$config_path")
    fi
done

if [ ${#NGINX_CONFIGS[@]} -eq 0 ]; then
    log_warning "Tidak ditemukan config nginx yang sesuai"
    log_info "Membuat config baru di conf.d..."
    
    # Tentukan app directory utama
    if [ ${#ALL_APPS[@]} -gt 0 ] && [ -d "${ALL_APPS[0]}" ]; then
        MAIN_APP="${ALL_APPS[0]}"
    else
        MAIN_APP="/var/www/html"
    fi
    
    NGINX_CONFIG="/etc/nginx/conf.d/kegiatan-bpmpkaltim-id.conf"
    NGINX_CONFIGS+=("$NGINX_CONFIG")
    
    cat > "$NGINX_CONFIG" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name kegiatan.bpmpkaltim.id;

    # Static files dengan optimasi real-time
    location /uploads/ {
        root $MAIN_APP/public;
        
        # Disable caching untuk file baru
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        
        # Allow CORS
        add_header Access-Control-Allow-Origin "*";
        
        # Real-time file checking
        try_files \$uri \$uri/ =404;
        
        # Logging
        access_log /var/log/nginx/uploads_access.log;
        error_log /var/log/nginx/uploads_error.log;
    }

    # Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    log_success "Config nginx baru dibuat"
    
else
    # Update config yang ada
    for config in "${NGINX_CONFIGS[@]}"; do
        log_action "Mengupdate config: $config"
        
        # Backup
        cp "$config" "$BACKUP_DIR/$(basename $config).backup"
        
        # Cek apakah ada konfigurasi uploads
        if grep -q "location /uploads/" "$config"; then
            log_info "Mengoptimalkan konfigurasi uploads yang ada..."
            
            # Update konfigurasi uploads untuk real-time access
            sed -i '/location \/uploads\/ {/,/}/ {
                /expires/c\        expires -1;
                /add_header Cache-Control/c\        add_header Cache-Control "no-cache, no-store, must-revalidate";
                /}$/i\        add_header Pragma "no-cache";
            }' "$config"
            
            log_success "Konfigurasi uploads dioptimalkan"
            
        else
            log_warning "Konfigurasi uploads tidak ditemukan di $config"
            
            # Tentukan app directory
            if [ ${#ALL_APPS[@]} -gt 0 ] && [ -d "${ALL_APPS[0]}" ]; then
                MAIN_APP="${ALL_APPS[0]}"
            else
                MAIN_APP="/var/www/html"
            fi
            
            # Tambahkan konfigurasi uploads
            sed -i '/location \/ {/i\
    # Static files dengan optimasi real-time\
    location /uploads/ {\
        root '"$MAIN_APP"'/public;\
        expires -1;\
        add_header Cache-Control "no-cache, no-store, must-revalidate";\
        add_header Pragma "no-cache";\
        add_header Access-Control-Allow-Origin "*";\
        try_files $uri $uri/ =404;\
        access_log /var/log/nginx/uploads_access.log;\
        error_log /var/log/nginx/uploads_error.log;\
    }\
' "$config"
            
            log_success "Konfigurasi uploads ditambahkan"
        fi
    done
fi

# STEP 5: Optimasi sistem untuk real-time file access
log_action "Mengoptimalkan sistem untuk real-time access..."

# Increase inotify limits untuk file watching
echo "fs.inotify.max_user_watches=524288" >> /etc/sysctl.conf 2>/dev/null || true
echo "fs.inotify.max_user_instances=8192" >> /etc/sysctl.conf 2>/dev/null || true
sysctl -p >/dev/null 2>&1 || true

log_success "Sistem dioptimalkan"

# STEP 6: Test dan reload nginx
log_action "Testing dan reload nginx..."

if nginx -t; then
    systemctl reload nginx
    log_success "Nginx direload dengan config baru"
else
    log_error "Nginx config error!"
    exit 1
fi

# STEP 7: Setup monitoring script untuk auto-refresh
log_action "Membuat monitoring script..."

MONITOR_SCRIPT="/usr/local/bin/nginx-uploads-monitor.sh"
cat > "$MONITOR_SCRIPT" << 'EOF'
#!/bin/bash

# Monitor untuk auto-refresh nginx ketika ada file upload baru
# Script ini berjalan di background untuk memastikan file baru langsung accessible

WATCH_DIRS=()
for dir in /home/<USER>/htdocs/public/uploads /var/www/*/public/uploads /var/www/html/public/uploads; do
    if [ -d "$dir" ]; then
        WATCH_DIRS+=("$dir")
    fi
done

if [ ${#WATCH_DIRS[@]} -eq 0 ]; then
    echo "No upload directories found"
    exit 1
fi

echo "Monitoring upload directories: ${WATCH_DIRS[@]}"

# Monitor file changes dan fix permissions secara real-time
inotifywait -m -r -e create,moved_to --format '%w%f %e' "${WATCH_DIRS[@]}" 2>/dev/null | while read file event; do
    if [[ "$file" =~ \.(jpg|jpeg|png|gif)$ ]]; then
        echo "$(date): New image uploaded: $file"
        
        # Fix permissions immediately
        chmod 644 "$file" 2>/dev/null || true
        
        # Get directory owner
        dir_owner=$(stat -c '%U:%G' "$(dirname "$file")" 2>/dev/null || echo "www-data:www-data")
        chown "$dir_owner" "$file" 2>/dev/null || true
        
        echo "$(date): Permissions fixed for: $file"
    fi
done
EOF

chmod +x "$MONITOR_SCRIPT"
log_success "Monitoring script dibuat: $MONITOR_SCRIPT"

# STEP 8: Setup systemd service untuk monitoring
log_action "Setup systemd service untuk auto-monitoring..."

SYSTEMD_SERVICE="/etc/systemd/system/nginx-uploads-monitor.service"
cat > "$SYSTEMD_SERVICE" << EOF
[Unit]
Description=Nginx Uploads Monitor
After=network.target

[Service]
Type=simple
ExecStart=$MONITOR_SCRIPT
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable nginx-uploads-monitor.service
systemctl start nginx-uploads-monitor.service

log_success "Auto-monitoring service diaktifkan"

# STEP 9: Test upload scenario
log_action "Testing upload scenario..."

# Buat test file baru
for APP_DIR in "${ALL_APPS[@]}"; do
    if [ -d "$APP_DIR/public/uploads" ]; then
        TEST_FILE="$APP_DIR/public/uploads/test-auto-refresh-$(date +%s).jpg"
        echo "Test file content" > "$TEST_FILE"
        
        # Fix permissions
        DOMAIN_USER=$(stat -c '%U' "$APP_DIR" 2>/dev/null || echo "www-data")
        DOMAIN_GROUP=$(stat -c '%G' "$APP_DIR" 2>/dev/null || echo "www-data")
        chown "$DOMAIN_USER:$DOMAIN_GROUP" "$TEST_FILE"
        chmod 644 "$TEST_FILE"
        
        # Test akses
        TEST_URL="https://kegiatan.bpmpkaltim.id/uploads/$(basename $TEST_FILE)"
        HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 5 2>/dev/null || echo "000")
        
        if [ "$HTTP_STATUS" = "200" ]; then
            log_success "✅ Test file dapat diakses langsung: Status $HTTP_STATUS"
        else
            log_warning "⚠️ Test file status: $HTTP_STATUS (mungkin perlu waktu propagasi)"
        fi
        
        # Cleanup
        rm -f "$TEST_FILE"
        break
    fi
done

# STEP 10: Summary
echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   CLOUDPANEL AUTO-REFRESH FIX SELESAI"
echo "============================================"
echo -e "${NC}"

log_success "✅ Nginx config dioptimalkan untuk real-time access"
log_success "✅ Permissions CloudPanel diatur dengan benar"
log_success "✅ Auto-monitoring service diaktifkan"
log_success "✅ Sistem inotify dioptimalkan"

echo ""
log_info "🔍 HASIL:"
echo "• File upload baru seharusnya langsung accessible tanpa reload nginx"
echo "• Permissions otomatis diatur untuk setiap file baru"
echo "• Nginx cache dinonaktifkan untuk folder uploads"
echo "• Background monitoring aktif"

echo ""
log_info "📋 MONITORING:"
echo "• Service status: systemctl status nginx-uploads-monitor"
echo "• Monitor logs: journalctl -u nginx-uploads-monitor -f"
echo "• Nginx logs: tail -f /var/log/nginx/uploads_access.log"

echo ""
log_info "🧪 TEST SEKARANG:"
echo "1. Upload foto baru di admin panel"
echo "2. Langsung cek detail absensi - foto harus muncul tanpa reload"
echo "3. Monitor: journalctl -u nginx-uploads-monitor -f"

echo ""
log_warning "⚠️ Jika masih ada masalah:"
echo "• Restart monitoring: sudo systemctl restart nginx-uploads-monitor"
echo "• Cek permissions: ls -la /path/to/uploads/"
echo "• Manual reload: sudo systemctl reload nginx"

log_success "Auto-refresh fix untuk CloudPanel selesai! 🎉"
