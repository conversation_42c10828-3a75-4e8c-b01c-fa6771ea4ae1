#!/usr/bin/env node

/**
 * Fix Deployment Paths Script
 * Mengatasi masalah duplikasi folder public dalam Next.js standalone deployment
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 Starting deployment path fix...');

// Paths
const standaloneDir = './.next/standalone';
const publicDir = './public';
const standalonePublicDir = path.join(standaloneDir, 'public');

function createSymlink() {
  try {
    // Check if standalone directory exists
    if (!fs.existsSync(standaloneDir)) {
      console.error('❌ Standalone directory not found. Please run "npm run build" first.');
      process.exit(1);
    }

    // Remove existing public directory in standalone if it exists
    if (fs.existsSync(standalonePublicDir)) {
      console.log('📁 Removing existing public directory in standalone...');
      fs.rmSync(standalonePublicDir, { recursive: true, force: true });
    }

    // Create symlink from standalone/public to root public
    console.log('🔗 Creating symlink from standalone/public to root public...');
    
    // Get absolute path to root public directory
    const absolutePublicPath = path.resolve(publicDir);
    
    // Create symlink
    fs.symlinkSync(absolutePublicPath, standalonePublicDir, 'dir');
    
    console.log('✅ Symlink created successfully!');
    console.log(`   ${standalonePublicDir} -> ${absolutePublicPath}`);
    
  } catch (error) {
    console.error('❌ Failed to create symlink:', error.message);
    
    // Fallback: copy files instead of symlink
    console.log('🔄 Falling back to copying files...');
    copyPublicFiles();
  }
}

function copyPublicFiles() {
  try {
    if (!fs.existsSync(publicDir)) {
      console.error('❌ Public directory not found.');
      return;
    }

    // Copy public directory to standalone
    console.log('📋 Copying public files to standalone...');
    copyRecursive(publicDir, standalonePublicDir);
    
    console.log('✅ Public files copied successfully!');
    
  } catch (error) {
    console.error('❌ Failed to copy public files:', error.message);
  }
}

function copyRecursive(src, dest) {
  const stats = fs.statSync(src);
  
  if (stats.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const items = fs.readdirSync(src);
    items.forEach(item => {
      copyRecursive(path.join(src, item), path.join(dest, item));
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

function createNginxConfig() {
  const nginxConfig = `
# Nginx configuration for Next.js standalone deployment
# File: /etc/nginx/sites-available/kegiatan.bpmpkaltim.id

server {
    listen 80;
    server_name kegiatan.bpmpkaltim.id;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name kegiatan.bpmpkaltim.id;
    
    # SSL configuration (adjust paths according to your setup)
    ssl_certificate /path/to/your/ssl/certificate.crt;
    ssl_certificate_key /path/to/your/ssl/private.key;
    
    # Root directory for static files
    root /path/to/your/deployment/.next/standalone/public;
    
    # Upload limit for file uploads
    client_max_body_size 50M;
    
    # Serve static files directly
    location /uploads/ {
        try_files $uri $uri/ =404;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    location /_next/static/ {
        try_files $uri $uri/ =404;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
    
    location /favicon.ico {
        try_files $uri $uri/ =404;
        expires 365d;
        add_header Cache-Control "public, immutable";
    }
    
    # Proxy all other requests to Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
`;

  fs.writeFileSync('./nginx-config.txt', nginxConfig);
  console.log('📝 Nginx configuration saved to nginx-config.txt');
}

function createDeploymentGuide() {
  const guide = `
# Deployment Fix Guide for kegiatan.bpmpkaltim.id

## Problem
- Files uploaded to: /public/public/uploads/absensi/photos/
- URLs accessing: /uploads/absensi/photos/
- Result: 404 File Not Found

## Solution Applied
1. ✅ Fixed public folder structure in standalone build
2. ✅ Created proper symlink/copy of public directory
3. ✅ Generated nginx configuration

## Manual Steps for VPS

### 1. Update your nginx configuration:
\`\`\`bash
sudo nano /etc/nginx/sites-available/kegiatan.bpmpkaltim.id
\`\`\`

Copy the content from nginx-config.txt and adjust the paths:
- Replace "/path/to/your/deployment" with your actual deployment path
- Replace SSL certificate paths with your actual certificate paths

### 2. Test nginx configuration:
\`\`\`bash
sudo nginx -t
\`\`\`

### 3. Reload nginx:
\`\`\`bash
sudo systemctl reload nginx
\`\`\`

### 4. Check file permissions:
\`\`\`bash
# Make sure nginx can access the files
sudo chown -R www-data:www-data /path/to/your/deployment/.next/standalone/public/uploads/
sudo chmod -R 755 /path/to/your/deployment/.next/standalone/public/uploads/
\`\`\`

### 5. Restart your Next.js application:
\`\`\`bash
# In your deployment directory
cd /path/to/your/deployment/.next/standalone
pm2 restart server.js  # or your process manager
\`\`\`

## Verification
After applying these fixes, test:
1. Upload a new photo in absensi form
2. Check the file is created in the correct location
3. Verify the photo displays correctly in the detail view

## Alternative Quick Fix
If the above doesn't work immediately, you can create a symlink manually on the server:

\`\`\`bash
cd /path/to/your/deployment/.next/standalone
rm -rf public  # Remove existing public folder
ln -s ../../public public  # Create symlink to root public folder
\`\`\`
`;

  fs.writeFileSync('./DEPLOYMENT_FIX_GUIDE.md', guide);
  console.log('📋 Deployment guide saved to DEPLOYMENT_FIX_GUIDE.md');
}

// Main execution
console.log('🚀 Next.js Standalone Deployment Path Fix');
console.log('==========================================');

createSymlink();
createNginxConfig();
createDeploymentGuide();

console.log('\n✅ All fixes applied!');
console.log('📖 Please read DEPLOYMENT_FIX_GUIDE.md for manual steps on your VPS.');
