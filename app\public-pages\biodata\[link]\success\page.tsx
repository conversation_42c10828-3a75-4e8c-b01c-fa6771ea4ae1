'use client';

import Link from 'next/link';
import { useEffect } from 'react';

export default function BiodataSuccessPage() {
  // Prevent going back to the form page
  useEffect(() => {
    // Replace the current history entry to prevent going back
    window.history.replaceState(null, '', window.location.href);

    // Handle the popstate event (when user clicks back button)
    const handlePopState = () => {
      window.history.pushState(null, '', window.location.href);
    };

    // Add event listener for back button clicks
    window.addEventListener('popstate', handlePopState);

    return () => {
      // Clean up event listener
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen px-4 bg-gray-100 sm:px-6">
      <div className="w-full max-w-md p-4 text-center bg-white rounded-lg shadow-md sm:p-6 md:p-8">
        <div className="flex items-center justify-center w-12 h-12 mx-auto mb-3 bg-green-100 rounded-full sm:w-16 sm:h-16 sm:mb-4">
          <svg
            className="w-6 h-6 text-green-600 sm:w-8 sm:h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h1 className="mb-2 text-xl font-bold text-gray-800 sm:text-2xl">Pendaftaran Berhasil</h1>
        <p className="mb-4 text-sm text-gray-600 sm:text-base sm:mb-6">
          Terima kasih. Data pendaftaran Anda telah berhasil disimpan.
        </p>
        <Link
          href="/"
          className="inline-block px-4 py-2 text-sm text-white transition-colors duration-200 bg-blue-600 rounded-md sm:text-base hover:bg-blue-700"
        >
          Kembali ke Halaman Utama
        </Link>
        <p className="mt-6 text-xs text-gray-500 sm:mt-8">
          © {new Date().getFullYear()} BPMP Provinsi Kalimantan Timur
        </p>
      </div>
    </div>
  );
}