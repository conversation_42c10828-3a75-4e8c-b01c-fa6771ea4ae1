'use client';

import { useState, useEffect, FormEvent } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import InputField from '../../../../../components/InputField';
import Toast, { ToastType } from '../../../../../components/Toast';

type FormData = {
  name: string;
  email: string;
  role: string;
  password: string;
};

export default function EditUserPage() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    role: '',
    password: '',
  });
  
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [toast, setToast] = useState({ message: '', type: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;

  // Fetch user data on component mount
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }

        const text = await response.text();
        // Periksa apakah respons kosong
        if (!text) {
          throw new Error('Empty response from server');
        }
        
        // Parse JSON hanya jika ada konten
        const userData = JSON.parse(text);
        setFormData({
          name: userData.name,
          email: userData.email,
          role: userData.role,
          password: '',
        });
      } catch (err) {
        console.error('Error fetching user:', err);
        setToast({
          message: 'Gagal memuat data pengguna',
          type: 'error',
        });
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear errors for this field
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    let isValid = true;

    if (!formData.name.trim()) {
      newErrors.name = 'Nama harus diisi';
      isValid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email harus diisi';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
      isValid = false;
    }

    if (!formData.role) {
      newErrors.role = 'Role harus dipilih';
      isValid = false;
    }

    // Validate password only if it's provided (optional on edit)
    if (formData.password && formData.password.length < 6) {
      newErrors.password = 'Password minimal 6 karakter';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setIsSubmitting(true);

      const userData = {
        name: formData.name,
        email: formData.email,
        role: formData.role,
      };

      // Only include password if it's provided
      if (formData.password) {
        Object.assign(userData, { password: formData.password });
      }

      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Gagal mengupdate pengguna');
      }

      setToast({
        message: 'Data pengguna berhasil diperbarui!',
        type: 'success',
      });

      // Redirect to user details after 2 seconds
      setTimeout(() => {
        router.push(`/dashboard/users/${userId}`);
      }, 2000);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Gagal mengupdate pengguna';
      setToast({
        message: errorMessage,
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="w-12 h-12 border-t-2 border-b-2 border-blue-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Edit Pengguna</h1>
        <Link
          href={`/dashboard/users/${userId}`}
          className="px-4 py-2 text-gray-700 transition-colors bg-gray-200 rounded-md hover:bg-gray-300"
        >
          Kembali
        </Link>
      </div>

      {toast.message && (
        <Toast 
          toast={{
            id: 'edit-user-toast',
            message: toast.message,
            type: toast.type as ToastType
          }}
          onClose={() => setToast({ message: '', type: '' })} 
        />
      )}

      <div className="p-6 bg-white rounded-lg shadow">
        <form onSubmit={handleSubmit} className="space-y-6">
          <InputField
            label="Nama"
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleChange}
            error={errors.name}
            required
          />

          <InputField
            label="Email"
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            error={errors.email}
            required
          />

          <InputField
            label="Password (Kosongkan jika tidak ingin mengubah)"
            id="password"
            name="password"
            type="password"
            value={formData.password}
            onChange={handleChange}
            error={errors.password}
          />

          <div className="space-y-2">
            <label htmlFor="role" className="block text-sm font-medium text-gray-700">
              Role
            </label>
            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleChange}
              className={`block w-full px-4 py-2 border ${
                errors.role ? 'border-red-500' : 'border-gray-300'
              } rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500`}
            >
              <option value="USER">USER</option>
              <option value="ADMIN">ADMIN</option>
              <option value="GM1">GM1</option>
              <option value="GM2">GM2</option>
              <option value="GM3">GM3</option>
              <option value="GM4">GM4</option>
              <option value="GM5">GM5</option>
              <option value="KEPALA">KEPALA</option>
              <option value="KASUBAG">KASUBAG</option>
            </select>
            {errors.role && <p className="mt-1 text-sm text-red-600">{errors.role}</p>}
          </div>

          <div className="flex justify-end space-x-4">
            <Link
              href={`/dashboard/users/${userId}`}
              className="px-4 py-2 text-gray-700 transition-colors bg-gray-200 rounded-md hover:bg-gray-300"
            >
              Batal
            </Link>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-white transition-colors bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Menyimpan...' : 'Simpan Perubahan'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
