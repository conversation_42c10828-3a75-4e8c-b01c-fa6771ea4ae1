import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { BasePreviewProps } from './BasePreviewProps';
import { normalizeQuestion } from './helpers';

export function MultipleChoicePreview({ 
  question, 
  answers, 
  errors, 
  handleMultipleChoiceChange 
}: BasePreviewProps) {
  const q = normalizeQuestion(question);
  
  if (!handleMultipleChoiceChange) {
    console.error('handleMultipleChoiceChange is required for MultipleChoicePreview');
    return null;
  }
  
  // Pastikan answers[q.id] adalah array
  const selectedChoices = Array.isArray(answers[q.id]) ? answers[q.id] : [];
  
  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-lg">
          {q.title}
          {q.isRequired && <span className="ml-1 text-red-500">*</span>}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {q.options?.choices?.map((choice: string, index: number) => (
            <div key={index} className="flex items-center space-x-2">
              <Checkbox 
                id={`${q.id}-option-${index}`}
                checked={selectedChoices.includes(choice)}
                onCheckedChange={(checked) => 
                  handleMultipleChoiceChange(q.id, choice, checked === true)
                }
              />
              <Label htmlFor={`${q.id}-option-${index}`}>{choice}</Label>
            </div>
          ))}
        </div>
        {errors[q.id] && (
          <p className="mt-2 text-sm text-red-500">{errors[q.id]}</p>
        )}
      </CardContent>
    </Card>
  );
}