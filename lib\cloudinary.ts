// lib/cloudinary.ts
import { v2 as cloudinary } from 'cloudinary';

// Konfigurasi Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Interface untuk hasil upload
export interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  url: string;
  format: string;
  width: number;
  height: number;
  bytes: number;
}

// Upload image ke Cloudinary
export async function uploadToCloudinary(
  file: Buffer | string,
  folder: string = 'pelatihan',
  publicId?: string
): Promise<CloudinaryUploadResult> {
  try {
    const uploadOptions: any = {
      folder: folder,
      resource_type: 'image',
      transformation: [
        {
          width: 800,
          height: 800,
          crop: 'limit',
          quality: 'auto:good',
          format: 'webp'
        }
      ]
    };

    if (publicId) {
      uploadOptions.public_id = publicId;
      uploadOptions.overwrite = true;
    }

    const result = await cloudinary.uploader.upload(
      file.toString('base64').includes('data:') 
        ? file.toString() 
        : `data:image/jpeg;base64,${file.toString('base64')}`,
      uploadOptions
    );

    return {
      public_id: result.public_id,
      secure_url: result.secure_url,
      url: result.url,
      format: result.format,
      width: result.width,
      height: result.height,
      bytes: result.bytes,
    };
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error);
    throw new Error('Gagal mengupload foto ke Cloudinary');
  }
}

// Hapus image dari Cloudinary
export async function deleteFromCloudinary(publicId: string): Promise<boolean> {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    return false;
  }
}

// Generate transformation URL
export function getCloudinaryUrl(
  publicId: string, 
  options: {
    width?: number;
    height?: number;
    crop?: string;
    quality?: string;
    format?: string;
  } = {}
): string {
  const {
    width = 300,
    height = 300,
    crop = 'fill',
    quality = 'auto:good',
    format = 'webp'
  } = options;

  return cloudinary.url(publicId, {
    width,
    height,
    crop,
    quality,
    format,
    secure: true
  });
}

export default cloudinary;
