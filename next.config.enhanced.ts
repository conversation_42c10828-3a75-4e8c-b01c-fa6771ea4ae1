import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: ["pdfkit"],
  devIndicators: false,
  output: 'standalone',
  
  // Image optimization configuration
  images: {
    // Image formats to support
    formats: ['image/webp', 'image/avif'],
    
    // Device sizes for responsive images
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    
    // Image sizes for different breakpoints
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // Domains allowed for external images
    domains: [],
    
    // Disable static image imports if needed
    disableStaticImages: false,
    
    // Cache settings
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  }
};

export default nextConfig;
