@echo off
setlocal enabledelayedexpansion

:: DIRECT SERVER DEPLOYMENT LAUNCHER
:: Upload and execute deployment script directly on VPS

title Direct Server Deployment - Photo Upload Fix

echo.
echo ===============================================
echo    DIRECT SERVER DEPLOYMENT LAUNCHER
echo    Photo Upload Auto-Refresh Fix
echo ===============================================
echo.

set "VPS_HOST=*************"
set "VPS_USER=root"
set "SCRIPT_NAME=execute-deployment-server.sh"

echo [36m🎯 TARGET SERVER:[0m
echo   Host: %VPS_HOST%
echo   User: %VPS_USER%
echo   Script: %SCRIPT_NAME%
echo.

echo [36m🔧 DEPLOYMENT PLAN:[0m
echo   1. Upload deployment script to server
echo   2. Make script executable
echo   3. Execute deployment directly on server
echo   4. Monitor deployment progress
echo   5. Verify results
echo.

set /p confirm="Execute direct server deployment? (y/n): "

if /i not "%confirm%"=="y" (
    echo [33m[INFO][0m Deployment cancelled
    pause
    exit /b 0
)

echo.
echo [35m[ACTION][0m Starting direct server deployment...

:: Step 1: Check if deployment script exists
if not exist "%SCRIPT_NAME%" (
    echo [31m[ERROR][0m Deployment script not found: %SCRIPT_NAME%
    echo [33m[INFO][0m Please ensure the script file exists in current directory
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Deployment script found

:: Step 2: Upload script to server
echo.
echo [35m[ACTION][0m Step 1/3: Uploading deployment script to server...

scp "%SCRIPT_NAME%" %VPS_USER%@%VPS_HOST%:/tmp/

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to upload deployment script
    echo [33m[INFO][0m Please check SSH connection and credentials
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script uploaded to /tmp/%SCRIPT_NAME%

:: Step 3: Make script executable and execute
echo.
echo [35m[ACTION][0m Step 2/3: Making script executable...

ssh %VPS_USER%@%VPS_HOST% "chmod +x /tmp/%SCRIPT_NAME%"

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to make script executable
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Script is now executable

:: Step 4: Execute deployment script on server
echo.
echo [35m[ACTION][0m Step 3/3: Executing deployment on server...
echo [36m[INFO][0m This may take 5-10 minutes...
echo.

ssh %VPS_USER%@%VPS_HOST% "/tmp/%SCRIPT_NAME%"

set "deployment_result=%errorlevel%"

echo.
echo ===============================================
echo    DEPLOYMENT EXECUTION COMPLETED
echo ===============================================
echo.

if %deployment_result%==0 (
    echo [32m✅ DEPLOYMENT SUCCESSFUL![0m
    echo.
    echo [36m🎉 RESULTS:[0m
    echo   - Photo upload auto-refresh fix deployed
    echo   - File permissions corrected
    echo   - Nginx configuration updated
    echo   - Monitoring service installed
    echo   - All services restarted
    echo.
    echo [36m🔍 VERIFICATION:[0m
    echo   1. Test photo upload in your application
    echo   2. Check that photos are immediately accessible
    echo   3. No manual nginx reload should be needed
    echo.
) else (
    echo [33m⚠️  DEPLOYMENT COMPLETED WITH WARNINGS[0m
    echo.
    echo [36m📋 NEXT STEPS:[0m
    echo   1. Check server logs for any issues
    echo   2. Test photo upload functionality
    echo   3. Run verification manually if needed
    echo.
)

:: Post-deployment verification
echo [35m[ACTION][0m Running quick verification...

echo [36m[INFO][0m Testing server connectivity...
ssh %VPS_USER%@%VPS_HOST% "echo 'Server connection OK'" 2>nul

if %errorlevel%==0 (
    echo [32m[SUCCESS][0m Server is accessible
) else (
    echo [33m[WARNING][0m Server connection issues
)

echo [36m[INFO][0m Testing nginx status...
ssh %VPS_USER%@%VPS_HOST% "systemctl is-active nginx" 2>nul

if %errorlevel%==0 (
    echo [32m[SUCCESS][0m Nginx is running
) else (
    echo [33m[WARNING][0m Nginx status unknown
)

echo [36m[INFO][0m Testing uploads directory...
ssh %VPS_USER%@%VPS_HOST% "[ -d '/var/www/vhosts/kegiatan.bpmpkaltim.id/public_html/uploads' ] && echo 'Uploads directory exists'" 2>nul

if %errorlevel%==0 (
    echo [32m[SUCCESS][0m Uploads directory confirmed
) else (
    echo [33m[WARNING][0m Uploads directory needs verification
)

echo.
echo ===============================================
echo    DIRECT SERVER DEPLOYMENT SUMMARY
echo ===============================================
echo.

echo [36m📊 FINAL STATUS:[0m
if %deployment_result%==0 (
    echo   [32m✅ Photo upload fix successfully deployed[0m
    echo   [32m✅ Server configuration updated[0m
    echo   [32m✅ Services restarted[0m
    echo   [32m✅ Ready for testing[0m
) else (
    echo   [33m⚠️  Deployment completed with some warnings[0m
    echo   [36m💡 Photos should still work immediately after upload[0m
)

echo.
echo [36m🚀 NEXT ACTIONS:[0m
echo   1. Test photo upload: https://kegiatan.bpmpkaltim.id
echo   2. Verify photos are immediately accessible
echo   3. Monitor for any issues
echo.

echo [36m📞 TROUBLESHOOTING:[0m
echo   - Check server logs: ssh %VPS_USER%@%VPS_HOST% "tail -f /var/log/photo-upload-monitor.log"
echo   - Restart services: ssh %VPS_USER%@%VPS_HOST% "systemctl restart nginx && pm2 restart all"
echo   - Manual verification: ssh %VPS_USER%@%VPS_HOST% "ls -la /var/www/vhosts/*/public_html/uploads/"
echo.

echo Deployment completed at: %date% %time%
echo.
pause
