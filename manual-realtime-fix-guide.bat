@echo off
cls
color 0E
echo =========================================================
echo     MANUAL CLOUDPANEL REALTIME PHOTO FIX GUIDE
echo =========================================================
echo.

echo STEP 1: Upload Script to VPS
echo =============================
echo 1. Open WinSCP, FileZilla, atau SSH client favorit Anda
echo 2. Connect ke VPS CloudPanel Anda
echo 3. Upload file 'cloudpanel-realtime-photo-fix.sh' ke folder /tmp/
echo.

echo STEP 2: Execute Script on VPS
echo ==============================
echo Copy dan paste command berikut ke SSH terminal:
echo.
echo chmod +x /tmp/cloudpanel-realtime-photo-fix.sh
echo /tmp/cloudpanel-realtime-photo-fix.sh
echo.

echo STEP 3: Monitor Execution
echo =========================
echo Script akan melakukan:
echo - Auto-detect CloudPanel site
echo - Install requirements (inotify-tools)
echo - Configure nginx untuk real-time access
echo - Create monitoring service
echo - Test real-time functionality
echo.

echo STEP 4: Verification Commands
echo =============================
echo Setelah script selesai, verifikasi dengan:
echo.
echo # Check service status
echo systemctl status realtime-photo-monitor
echo.
echo # Monitor real-time logs
echo tail -f /var/log/realtime-photo-monitor.log
echo.
echo # Check nginx photo logs
echo tail -f /var/log/nginx/photo_realtime.log
echo.
echo # Test photo upload (manual test)
echo curl -I http://YOUR_DOMAIN/uploads/path/to/photo.jpg
echo.

echo TROUBLESHOOTING
echo ===============
echo Jika ada masalah:
echo.
echo 1. Check nginx config:
echo    nginx -t
echo.
echo 2. Restart services:
echo    systemctl restart nginx
echo    systemctl restart realtime-photo-monitor
echo.
echo 3. Check logs:
echo    tail -f /var/log/nginx/error.log
echo    journalctl -u realtime-photo-monitor -f
echo.

echo EXPECTED RESULTS
echo ================
echo Setelah fix berhasil:
echo - Photo upload langsung bisa diakses tanpa reload nginx
echo - Real-time monitoring service berjalan
echo - Nginx headers no-cache untuk uploads
echo - Auto permission fixing untuk file baru
echo.

echo =========================================================
echo    Script ready for deployment: cloudpanel-realtime-photo-fix.sh
echo =========================================================
echo.
pause
