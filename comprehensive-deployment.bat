@echo off
setlocal enabledelayedexpansion

:: COMPREHENSIVE DEPLOYMENT FOR PHOTO UPLOAD FIX
:: Complete solution that deploys all fixes in proper sequence

title Comprehensive Photo Upload Fix Deployment

echo.
echo ===============================================
echo    COMPREHENSIVE PHOTO UPLOAD FIX
echo    Complete Deployment for kegiatan.bpmpkaltim.id
echo ===============================================
echo.

echo [36m🎯 PROBLEM SUMMARY:[0m
echo   Photos upload successfully but require manual nginx reload 
echo   to become accessible via URLs
echo.
echo [36m🔧 SOLUTION OVERVIEW:[0m
echo   1. Fix file permissions in all upload API routes
echo   2. Configure proper nginx caching and static file handling  
echo   3. Implement real-time monitoring service
echo   4. Deploy comprehensive VPS configuration
echo.

set /p deploy_confirm="Deploy comprehensive fix now? (y/n): "

if /i not "%deploy_confirm%"=="y" (
    echo [33m[INFO][0m Deployment cancelled
    pause
    exit /b 0
)

echo.
echo [35m[ACTION][0m Starting comprehensive deployment...

:: Check prerequisites
echo [36m[INFO][0m Checking prerequisites...

:: Check if we're in the correct directory
if not exist "package.json" (
    echo [31m[ERROR][0m Not in Next.js project directory!
    echo [33m[INFO][0m Please run from f:\online\zoom_rutin
    pause
    exit /b 1
)

:: Check if deployment files exist
set "missing_files=0"
if not exist "fix-photo-upload-vps.sh" (
    echo [31m[ERROR][0m fix-photo-upload-vps.sh missing
    set /a missing_files+=1
)

if not exist "advanced-cloudpanel-autorefresh-fix.sh" (
    echo [31m[ERROR][0m advanced-cloudpanel-autorefresh-fix.sh missing
    set /a missing_files+=1
)

if %missing_files% gtr 0 (
    echo [31m[ERROR][0m Missing %missing_files% required deployment files
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m All prerequisites satisfied

:: Step 1: Build the application
echo.
echo [35m[ACTION][0m Step 1/6: Building Next.js application...
call npm run build

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Build failed!
    echo [33m[INFO][0m Please fix build errors before deployment
    pause
    exit /b 1
)

echo [32m[SUCCESS][0m Application built successfully

:: Step 2: Create deployment package
echo.
echo [35m[ACTION][0m Step 2/6: Creating deployment package...

:: Create deployment directory
mkdir deployment_temp 2>nul
cd deployment_temp

:: Copy necessary files
echo [36m[INFO][0m Copying application files...
xcopy /E /I /Y ..\.next next_build
xcopy /E /I /Y ..\public public
copy ..\package.json package.json
copy ..\next.config.enhanced.ts next.config.ts

:: Create deployment archive
tar -czf ..\deployment.tar.gz *
cd ..
rmdir /s /q deployment_temp

echo [32m[SUCCESS][0m Deployment package created (deployment.tar.gz)

:: Step 3: Upload files to VPS
echo.
echo [35m[ACTION][0m Step 3/6: Uploading files to VPS...

set "VPS_HOST=*************"
set "VPS_USER=root"

echo [36m[INFO][0m Uploading to %VPS_USER%@%VPS_HOST%...

:: Upload main deployment
scp deployment.tar.gz %VPS_USER%@%VPS_HOST%:/tmp/

if %errorlevel% neq 0 (
    echo [31m[ERROR][0m Failed to upload deployment package
    pause
    exit /b 1
)

:: Upload fix scripts
scp fix-photo-upload-vps.sh %VPS_USER%@%VPS_HOST%:/tmp/
scp advanced-cloudpanel-autorefresh-fix.sh %VPS_USER%@%VPS_HOST%:/tmp/

echo [32m[SUCCESS][0m Files uploaded to VPS

:: Step 4: Execute deployment on VPS
echo.
echo [35m[ACTION][0m Step 4/6: Executing deployment on VPS...

ssh %VPS_USER%@%VPS_HOST% "chmod +x /tmp/fix-photo-upload-vps.sh && chmod +x /tmp/advanced-cloudpanel-autorefresh-fix.sh"

echo [36m[INFO][0m Running comprehensive fix script...
ssh %VPS_USER%@%VPS_HOST% "/tmp/advanced-cloudpanel-autorefresh-fix.sh"

if %errorlevel% neq 0 (
    echo [33m[WARNING][0m Advanced fix script completed with warnings
    echo [36m[INFO][0m Proceeding with basic fix...
    ssh %VPS_USER%@%VPS_HOST% "/tmp/fix-photo-upload-vps.sh"
)

echo [32m[SUCCESS][0m VPS deployment completed

:: Step 5: Restart services
echo.
echo [35m[ACTION][0m Step 5/6: Restarting services...

ssh %VPS_USER%@%VPS_HOST% "systemctl restart nginx && pm2 restart all"

if %errorlevel% neq 0 (
    echo [33m[WARNING][0m Service restart completed with warnings
) else (
    echo [32m[SUCCESS][0m Services restarted successfully
)

:: Step 6: Verify deployment
echo.
echo [35m[ACTION][0m Step 6/6: Verifying deployment...

echo [36m[INFO][0m Testing photo upload functionality...

:: Test the main upload endpoint
curl -s -o nul -w "%%{http_code}" https://kegiatan.bpmpkaltim.id/api/absensi/upload-photo > temp_status.txt
set /p http_status=<temp_status.txt
del temp_status.txt

if "%http_status%"=="405" (
    echo [32m[SUCCESS][0m Upload endpoint is accessible (Method Not Allowed expected for GET)
) else if "%http_status%"=="200" (
    echo [32m[SUCCESS][0m Upload endpoint is fully accessible
) else (
    echo [33m[WARNING][0m Upload endpoint returned status: %http_status%
)

:: Test static file serving
curl -s -o nul -w "%%{http_code}" https://kegiatan.bpmpkaltim.id/uploads/ > temp_status.txt
set /p static_status=<temp_status.txt
del temp_status.txt

if "%static_status%"=="403" (
    echo [32m[SUCCESS][0m Static file directory is accessible (403 Forbidden expected for directory listing)
) else if "%static_status%"=="200" (
    echo [32m[SUCCESS][0m Static file directory is fully accessible
) else (
    echo [33m[WARNING][0m Static directory returned status: %static_status%
)

echo.
echo ===============================================
echo    DEPLOYMENT COMPLETED
echo ===============================================
echo.
echo [32m✅ SUMMARY:[0m
echo   1. Application built and deployed successfully
echo   2. File permissions fixed in all upload routes
echo   3. VPS configuration updated
echo   4. Nginx and PM2 services restarted
echo   5. Upload endpoints are accessible
echo.
echo [36m🔍 NEXT STEPS:[0m
echo   1. Test photo upload in the application
echo   2. Verify photos are immediately accessible
echo   3. Run monitor-autorefresh.bat for continuous monitoring
echo.
echo [33m📋 FILES CREATED:[0m
echo   - deployment.tar.gz (can be deleted after successful deployment)
echo.

echo Deployment completed at: %date% %time%
echo.
pause
