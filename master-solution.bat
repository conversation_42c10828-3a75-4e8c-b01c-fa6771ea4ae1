@echo off
setlocal enabledelayedexpansion

:: MASTER AUTO-REFRESH SOLUTION
:: Complete solution untuk masalah foto upload nginx reload

title Master Auto-Refresh Solution - kegiatan.bpmpkaltim.id

:SPLASH_SCREEN
cls
echo.
echo [32m██████╗ ██╗  ██╗ ██████╗ ████████╗ ██████╗      ███████╗██╗██╗  ██╗[0m
echo [32m██╔══██╗██║  ██║██╔═══██╗╚══██╔══╝██╔═══██╗     ██╔════╝██║╚██╗██╔╝[0m
echo [32m██████╔╝███████║██║   ██║   ██║   ██║   ██║     █████╗  ██║ ╚███╔╝ [0m
echo [32m██╔═══╝ ██╔══██║██║   ██║   ██║   ██║   ██║     ██╔══╝  ██║ ██╔██╗ [0m
echo [32m██║     ██║  ██║╚██████╔╝   ██║   ╚██████╔╝     ██║     ██║██╔╝ ██╗[0m
echo [32m╚═╝     ╚═╝  ╚═╝ ╚═════╝    ╚═╝    ╚═════╝      ╚═╝     ╚═╝╚═╝  ╚═╝[0m
echo.
echo [36m           AUTO-REFRESH MASTER SOLUTION[0m
echo [36m         Complete Fix untuk kegiatan.bpmpkaltim.id[0m
echo.
echo [33m🎯 MASALAH:[0m Foto upload perlu reload nginx manual
echo [33m🔧 SOLUSI:[0m Real-time monitoring + advanced nginx config
echo [33m🎉 HASIL:[0m Foto langsung accessible tanpa reload
echo.
timeout /t 3 >nul

:MAIN_MENU
cls
echo.
echo ================================================
echo    MASTER AUTO-REFRESH SOLUTION
echo    Advanced CloudPanel Ubuntu 24.04 Fix
echo ================================================
echo.

:: Quick status check
echo [36m🔍 QUICK STATUS CHECK:[0m
ssh root@************* "systemctl is-active nginx" >nul 2>&1
if %errorlevel% equ 0 (
    set NGINX_STATUS=[32m✅ ACTIVE[0m
) else (
    set NGINX_STATUS=[31m❌ INACTIVE[0m
)

ssh root@************* "systemctl is-active advanced-photo-monitor" >nul 2>&1
if %errorlevel% equ 0 (
    set MONITOR_STATUS=[32m✅ ACTIVE[0m
) else (
    set MONITOR_STATUS=[31m❌ INACTIVE[0m
)

echo   • Nginx: !NGINX_STATUS!
echo   • Photo Monitor: !MONITOR_STATUS!
echo.

echo [33m🚀 DEPLOYMENT OPTIONS:[0m
echo.
echo   [32m1[0m. 🎯 Quick Start (Recommended)
echo   [32m2[0m. 📋 Step-by-Step Deployment
echo   [32m3[0m. 🔧 Full Management Center
echo.
echo [33m🔍 TESTING & MONITORING:[0m
echo.
echo   [32m4[0m. ✅ Verify Current Setup
echo   [32m5[0m. 📊 Real-time Monitoring
echo   [32m6[0m. 🧪 Test Photo Upload
echo.
echo [33m🛠️ MAINTENANCE:[0m
echo.
echo   [32m7[0m. 🔍 Troubleshooter
echo   [32m8[0m. 📋 View Logs
echo   [32m9[0m. ⚙️  Service Management
echo.
echo [33m📖 DOCUMENTATION:[0m
echo.
echo   [32mH[0m. 📖 Help & Documentation
echo   [32mR[0m. 📄 Read Full Guide
echo   [32mE[0m. 🆘 Emergency Rollback
echo   [32m0[0m. ❌ Exit
echo.

set /p choice="Choose option: "

if /i "%choice%"=="1" goto QUICK_START
if /i "%choice%"=="2" goto STEP_BY_STEP
if /i "%choice%"=="3" goto MANAGEMENT_CENTER
if /i "%choice%"=="4" goto VERIFY_SETUP
if /i "%choice%"=="5" goto MONITORING
if /i "%choice%"=="6" goto TEST_UPLOAD
if /i "%choice%"=="7" goto TROUBLESHOOTER
if /i "%choice%"=="8" goto VIEW_LOGS
if /i "%choice%"=="9" goto SERVICE_MANAGEMENT
if /i "%choice%"=="h" goto HELP
if /i "%choice%"=="r" goto READ_GUIDE
if /i "%choice%"=="e" goto EMERGENCY_ROLLBACK
if /i "%choice%"=="0" goto EXIT

echo [31m[ERROR][0m Invalid option. Please try again.
timeout /t 2 >nul
goto MAIN_MENU

:QUICK_START
cls
echo.
echo [35m================================================[0m
echo    🎯 QUICK START - ONE-CLICK SOLUTION
echo [35m================================================[0m
echo.

echo [33m[INFO][0m This will automatically deploy the complete solution:
echo.
echo   ✅ Advanced nginx configuration
echo   ✅ Real-time file monitoring system
echo   ✅ Auto-permission fixing service
echo   ✅ System optimization
echo   ✅ Monitoring tools
echo.

if exist "%~dp0quick-start-autorefresh.bat" (
    echo [35m[ACTION][0m Running quick start deployment...
    call "%~dp0quick-start-autorefresh.bat"
) else (
    echo [31m[ERROR][0m Quick start script not found!
    echo [36m[INFO][0m Using alternative deployment...
    if exist "%~dp0deploy-final-autorefresh.bat" (
        call "%~dp0deploy-final-autorefresh.bat"
    ) else (
        echo [31m[ERROR][0m No deployment scripts found!
    )
)

echo.
pause
goto MAIN_MENU

:STEP_BY_STEP
cls
echo.
echo [35m================================================[0m
echo    📋 STEP-BY-STEP DEPLOYMENT
echo [35m================================================[0m
echo.

if exist "%~dp0deploy-final-autorefresh.bat" (
    call "%~dp0deploy-final-autorefresh.bat"
) else (
    echo [31m[ERROR][0m Deployment script not found!
)

echo.
pause
goto MAIN_MENU

:MANAGEMENT_CENTER
cls
echo.
echo [35m================================================[0m
echo    🔧 FULL MANAGEMENT CENTER
echo [35m================================================[0m
echo.

if exist "%~dp0management-center.bat" (
    call "%~dp0management-center.bat"
) else (
    echo [31m[ERROR][0m Management center script not found!
)

goto MAIN_MENU

:VERIFY_SETUP
cls
echo.
echo [35m================================================[0m
echo    ✅ VERIFY CURRENT SETUP
echo [35m================================================[0m
echo.

if exist "%~dp0verify-autorefresh.bat" (
    call "%~dp0verify-autorefresh.bat"
) else (
    echo [31m[ERROR][0m Verification script not found!
    echo [36m[INFO][0m Running manual verification...
    
    echo [35m[ACTION][0m Manual verification...
    ssh root@************* "echo 'System Status:' && systemctl status nginx advanced-photo-monitor --no-pager -l"
)

echo.
pause
goto MAIN_MENU

:MONITORING
cls
echo.
echo [35m================================================[0m
echo    📊 REAL-TIME MONITORING
echo [35m================================================[0m
echo.

if exist "%~dp0monitor-autorefresh.bat" (
    call "%~dp0monitor-autorefresh.bat"
) else (
    echo [31m[ERROR][0m Monitoring script not found!
    echo [36m[INFO][0m Using direct SSH monitoring...
    ssh root@************* "tail -f /var/log/advanced-photo-monitor.log"
)

echo.
pause
goto MAIN_MENU

:TEST_UPLOAD
cls
echo.
echo [35m================================================[0m
echo    🧪 TEST PHOTO UPLOAD
echo [35m================================================[0m
echo.

echo [33m📋 MANUAL TEST PROCEDURE:[0m
echo.
echo   1. Application akan dibuka di browser
echo   2. Login ke sistem absensi
echo   3. Upload foto baru di form absensi
echo   4. Langsung buka detail absensi
echo   5. Foto harus muncul TANPA reload nginx
echo.

set /p proceed="Start test procedure? (y/n): "

if /i "%proceed%"=="y" (
    echo [35m[ACTION][0m Opening application...
    start "" "https://kegiatan.bpmpkaltim.id"
    
    echo [35m[ACTION][0m Starting monitoring...
    if exist "%~dp0monitor-autorefresh.bat" (
        start "" cmd /c "call %~dp0monitor-autorefresh.bat single"
    ) else (
        start "" cmd /c "title Monitor Logs && ssh root@************* tail -f /var/log/advanced-photo-monitor.log"
    )
    
    echo [36m[INFO][0m Application dan monitoring telah dibuka
    echo [36m[INFO][0m Silakan lakukan test upload
)

echo.
pause
goto MAIN_MENU

:TROUBLESHOOTER
cls
echo.
echo [35m================================================[0m
echo    🔍 AUTOMATED TROUBLESHOOTER
echo [35m================================================[0m
echo.

if exist "%~dp0troubleshoot-autorefresh.bat" (
    call "%~dp0troubleshoot-autorefresh.bat"
) else (
    echo [31m[ERROR][0m Troubleshooter script not found!
    echo [36m[INFO][0m Running manual troubleshooting...
    ssh root@************* "/tmp/troubleshoot-autorefresh.sh" 2>nul || (
        echo [33m[WARNING][0m Remote troubleshooter not found
        echo [36m[INFO][0m Running basic checks...
        ssh root@************* "systemctl status nginx advanced-photo-monitor"
    )
)

echo.
pause
goto MAIN_MENU

:VIEW_LOGS
cls
echo.
echo [35m================================================[0m
echo    📋 VIEW SYSTEM LOGS
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Choose log type:
echo.
echo   [32m1[0m. Photo Monitor Logs
echo   [32m2[0m. Nginx Access Logs
echo   [32m3[0m. Nginx Error Logs
echo   [32m4[0m. All Recent Logs
echo   [32m0[0m. Back to Main Menu
echo.

set /p log_choice="Choose option (0-4): "

if "%log_choice%"=="1" (
    echo [35m[ACTION][0m Showing photo monitor logs...
    ssh root@************* "tail -50 /var/log/advanced-photo-monitor.log 2>/dev/null || echo 'Log file not found'"
) else if "%log_choice%"=="2" (
    echo [35m[ACTION][0m Showing nginx access logs...
    ssh root@************* "tail -50 /var/log/nginx/photo_access.log 2>/dev/null || tail -50 /var/log/nginx/access.log"
) else if "%log_choice%"=="3" (
    echo [35m[ACTION][0m Showing nginx error logs...
    ssh root@************* "tail -50 /var/log/nginx/error.log"
) else if "%log_choice%"=="4" (
    echo [35m[ACTION][0m Showing all recent logs...
    ssh root@************* "echo '=== PHOTO MONITOR ===' && tail -10 /var/log/advanced-photo-monitor.log 2>/dev/null && echo '=== NGINX ACCESS ===' && tail -10 /var/log/nginx/access.log && echo '=== NGINX ERROR ===' && tail -10 /var/log/nginx/error.log"
) else if "%log_choice%"=="0" (
    goto MAIN_MENU
) else (
    echo [31m[ERROR][0m Invalid option
    timeout /t 2 >nul
    goto VIEW_LOGS
)

echo.
pause
goto MAIN_MENU

:SERVICE_MANAGEMENT
cls
echo.
echo [35m================================================[0m
echo    ⚙️ SERVICE MANAGEMENT
echo [35m================================================[0m
echo.

echo [33m[INFO][0m Service management options:
echo.
echo   [32m1[0m. Restart All Services
echo   [32m2[0m. Start Photo Monitor
echo   [32m3[0m. Stop Photo Monitor
echo   [32m4[0m. Reload Nginx
echo   [32m5[0m. Check Service Status
echo   [32m0[0m. Back to Main Menu
echo.

set /p svc_choice="Choose option (0-5): "

if "%svc_choice%"=="1" (
    echo [35m[ACTION][0m Restarting all services...
    ssh root@************* "systemctl restart nginx advanced-photo-monitor && echo 'Services restarted successfully'"
) else if "%svc_choice%"=="2" (
    echo [35m[ACTION][0m Starting photo monitor...
    ssh root@************* "systemctl start advanced-photo-monitor && echo 'Photo monitor started'"
) else if "%svc_choice%"=="3" (
    echo [35m[ACTION][0m Stopping photo monitor...
    ssh root@************* "systemctl stop advanced-photo-monitor && echo 'Photo monitor stopped'"
) else if "%svc_choice%"=="4" (
    echo [35m[ACTION][0m Reloading nginx...
    ssh root@************* "nginx -t && systemctl reload nginx && echo 'Nginx reloaded successfully'"
) else if "%svc_choice%"=="5" (
    echo [35m[ACTION][0m Checking service status...
    ssh root@************* "systemctl status nginx advanced-photo-monitor --no-pager"
) else if "%svc_choice%"=="0" (
    goto MAIN_MENU
) else (
    echo [31m[ERROR][0m Invalid option
    timeout /t 2 >nul
    goto SERVICE_MANAGEMENT
)

echo.
pause
goto MAIN_MENU

:HELP
cls
echo.
echo [35m================================================[0m
echo    📖 HELP & DOCUMENTATION
echo [35m================================================[0m
echo.

echo [33m🎯 ABOUT AUTO-REFRESH FIX:[0m
echo   Complete solution untuk masalah foto upload yang memerlukan
echo   nginx reload manual setiap kali ada foto baru.
echo.
echo [33m🔧 COMPONENTS:[0m
echo   • Advanced Nginx Configuration - No-cache untuk uploads
echo   • Real-time File Monitoring - inotify-based monitoring
echo   • Auto-permission Fixing - Background service
echo   • System Optimization - inotify limits, network tuning
echo.
echo [33m📋 HOW IT WORKS:[0m
echo   1. Monitor file uploads dengan inotify real-time
echo   2. Auto-fix permissions secara langsung
echo   3. Nginx dikonfigurasi tanpa cache untuk uploads
echo   4. Background service handle semua otomatis
echo.
echo [33m🧪 TESTING:[0m
echo   1. Upload foto di aplikasi absensi
echo   2. Langsung buka detail absensi
echo   3. Foto harus muncul TANPA reload nginx
echo.
echo [33m📞 SUPPORT:[0m
echo   • Use troubleshooter untuk automated fixing
echo   • Check logs untuk debugging
echo   • Restart services untuk reset
echo.

pause
goto MAIN_MENU

:READ_GUIDE
cls
echo.
echo [35m================================================[0m
echo    📄 FULL DOCUMENTATION
echo [35m================================================[0m
echo.

if exist "%~dp0FINAL_AUTO_REFRESH_README.md" (
    echo [36m[INFO][0m Opening documentation...
    start "" "%~dp0FINAL_AUTO_REFRESH_README.md"
) else (
    echo [31m[ERROR][0m Documentation file not found!
    echo [36m[INFO][0m Creating basic guide...
    
    echo [33m📋 QUICK REFERENCE:[0m
    echo.
    echo [32mDeployment:[0m
    echo   quick-start-autorefresh.bat     - One-click solution
    echo   deploy-final-autorefresh.bat    - Step-by-step
    echo   management-center.bat           - Full control
    echo.
    echo [32mMonitoring:[0m
    echo   monitor-autorefresh.bat         - Real-time dashboard
    echo   verify-autorefresh.bat          - System verification
    echo.
    echo [32mTroubleshooting:[0m
    echo   troubleshoot-autorefresh.bat    - Automated fixes
    echo.
    echo [32mManual Commands:[0m
    echo   systemctl status advanced-photo-monitor
    echo   tail -f /var/log/advanced-photo-monitor.log
    echo   systemctl restart nginx
)

echo.
pause
goto MAIN_MENU

:EMERGENCY_ROLLBACK
cls
echo.
echo [35m================================================[0m
echo    🆘 EMERGENCY ROLLBACK
echo [35m================================================[0m
echo.

echo [31m⚠️ WARNING: This will remove all auto-refresh fixes![0m
echo.
echo [33m[INFO][0m Rollback will:
echo   • Stop and disable photo monitor service
echo   • Remove monitoring scripts
echo   • Restore original nginx configuration
echo   • System will return to manual reload mode
echo.

set /p confirm_rollback="Type 'ROLLBACK' to confirm emergency rollback: "

if not "%confirm_rollback%"=="ROLLBACK" (
    echo [36m[INFO][0m Rollback cancelled
    timeout /t 2 >nul
    goto MAIN_MENU
)

echo.
echo [35m[ACTION][0m Starting emergency rollback...

ssh root@************* "systemctl stop advanced-photo-monitor 2>/dev/null || true"
ssh root@************* "systemctl disable advanced-photo-monitor 2>/dev/null || true"
ssh root@************* "rm -f /etc/systemd/system/advanced-photo-monitor.service"
ssh root@************* "rm -f /usr/local/bin/advanced-photo-monitor.sh"
ssh root@************* "systemctl daemon-reload"
ssh root@************* "systemctl restart nginx"

echo [32m[SUCCESS][0m Emergency rollback completed!
echo [33m[INFO][0m System restored to original state
echo [36m[INFO][0m Manual nginx reload will be required for new uploads

echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo [36m================================================[0m
echo    Thank you for using Master Auto-Refresh Solution!
echo [36m================================================[0m
echo.
echo [32m🎉 For kegiatan.bpmpkaltim.id[0m
echo [36m📞 Support: Check troubleshooter atau logs[0m
echo [33m📋 Documentation: FINAL_AUTO_REFRESH_README.md[0m
echo.
echo [36mHave a great day! 🚀[0m
echo.
timeout /t 3 >nul
exit /b 0
