@echo off
echo ========================================
echo   UBUNTU 24.04 NGINX FIX - PHOTO UPLOAD
echo ========================================
echo.

set VPS_HOST=kegiatan.bpmpkaltim.id
set VPS_USER=root

echo Target: Ubuntu 24.04 dengan nginx config di /etc/nginx/nginx.conf
echo Issue: Photo upload 404 error
echo Solution: Auto-setup nginx config + uploads folder
echo.

echo [STEP 1] Upload Ubuntu 24.04 fix script...
scp ubuntu24-nginx-fix.sh %VPS_USER%@%VPS_HOST%:/root/
if %errorlevel% neq 0 (
    echo ERROR: Gagal upload script
    echo.
    echo MANUAL UPLOAD dengan WinSCP:
    echo File: ubuntu24-nginx-fix.sh
    echo Dest: /root/
    echo.
    pause
    exit /b 1
)

echo [STEP 2] Connecting ke VPS untuk run fix...
echo.
echo Script akan:
echo ✓ Auto-detect lokasi aplikasi Next.js
echo ✓ Buat folder /public/uploads/absensi/photos/
echo ✓ Setup nginx config untuk serve static files
echo ✓ Set permissions www-data:www-data
echo ✓ Test akses URL
echo ✓ Backup config sebelum perubahan
echo.
echo Tekan Enter untuk lanjut...
pause

ssh %VPS_USER%@%VPS_HOST% "chmod +x ubuntu24-nginx-fix.sh && sudo ./ubuntu24-nginx-fix.sh"

echo.
echo ========================================
echo   FIX COMPLETED
echo ========================================
echo.
echo VERIFICATION STEPS:
echo 1. Buka: https://kegiatan.bpmpkaltim.id/admin
echo 2. Upload foto absensi baru 
echo 3. Cek detail absensi - foto harus muncul tanpa 404
echo 4. Test direct URL: https://kegiatan.bpmpkaltim.id/uploads/
echo.
echo Jika masih ada masalah, cek log:
echo ssh %VPS_USER%@%VPS_HOST% "tail -f /var/log/nginx/uploads_error.log"
echo.
pause
