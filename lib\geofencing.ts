// Geofencing Library untuk validasi GPS dan anti-spoofing
// Implements advanced GPS validation and location verification

export interface GeofenceConfig {
  center: {
    lat: number;
    lng: number;
  };
  radius: number; // radius in meters
  name: string;
  address?: string;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  speed?: number;
  heading?: number;
}

export interface ValidatedLocation extends LocationData {
  isValid: boolean;
  distance?: number;
  validationFlags: {
    withinGeofence: boolean;
    accuracyAcceptable: boolean;
    speedReasonable: boolean;
    noSpoofingDetected: boolean;
    timestampValid: boolean;
  };
  riskScore: number; // 0-100, higher = more suspicious
  warnings: string[];
}

// Default geofences for training venues
export const DEFAULT_TRAINING_VENUES: GeofenceConfig[] = [
  {
    center: { lat: -0.471852, lng: 117.157982 },
    radius: 100,
    name: "BP<PERSON> Kaltim - Ka<PERSON>",
    address: "<PERSON><PERSON>. <PERSON><PERSON><PERSON>umo, Samarinda"
  },
  {
    center: { lat: -0.502102, lng: 117.153709 },
    radius: 150,
    name: "Gedung BPSDM Provinsi Kaltim",
    address: "Samarinda, Kalimantan Timur"
  },
  {
    center: { lat: -0.489270, lng: 117.143860 },
    radius: 200,
    name: "Balai Pelatihan Guru",
    address: "Kompleks Perkantoran Tepian, Samarinda"
  }
];

/**
 * Calculate distance between two GPS coordinates using Haversine formula
 */
export function calculateDistance(
  lat1: number, 
  lng1: number, 
  lat2: number, 
  lng2: number
): number {
  const R = 6371000; // Earth's radius in meters
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Check if location is within any of the allowed geofences
 */
export function checkGeofence(
  location: LocationData, 
  allowedVenues: GeofenceConfig[]
): { isWithin: boolean; venue?: GeofenceConfig; distance?: number } {
  
  for (const venue of allowedVenues) {
    const distance = calculateDistance(
      location.latitude,
      location.longitude,
      venue.center.lat,
      venue.center.lng
    );
    
    if (distance <= venue.radius) {
      return {
        isWithin: true,
        venue,
        distance
      };
    }
  }
  
  // Return closest venue for reference
  const distances = allowedVenues.map(venue => ({
    venue,
    distance: calculateDistance(
      location.latitude,
      location.longitude,
      venue.center.lat,
      venue.center.lng
    )
  }));
  
  const closest = distances.reduce((min, current) => 
    current.distance < min.distance ? current : min
  );
  
  return {
    isWithin: false,
    venue: closest.venue,
    distance: closest.distance
  };
}

/**
 * Detect potential GPS spoofing based on various indicators
 */
export function detectGpsSpoofing(
  currentLocation: LocationData,
  previousLocation?: LocationData
): {
  isSuspicious: boolean;
  riskScore: number;
  warnings: string[];
} {
  const warnings: string[] = [];
  let riskScore = 0;

  // 1. Check GPS accuracy
  if (currentLocation.accuracy > 50) {
    warnings.push("GPS accuracy sangat rendah (>50m)");
    riskScore += 20;
  } else if (currentLocation.accuracy > 20) {
    warnings.push("GPS accuracy rendah (>20m)");
    riskScore += 10;
  }

  // 2. Check timestamp validity
  const now = Date.now();
  const locationAge = now - currentLocation.timestamp;
  
  if (locationAge > 30000) { // 30 seconds old
    warnings.push("Data GPS terlalu lama (>30 detik)");
    riskScore += 15;
  }

  // 3. Check for impossible movement (teleportation)
  if (previousLocation) {
    const timeDiff = (currentLocation.timestamp - previousLocation.timestamp) / 1000; // seconds
    const distance = calculateDistance(
      previousLocation.latitude,
      previousLocation.longitude,
      currentLocation.latitude,
      currentLocation.longitude
    );
    
    if (timeDiff > 0 && distance > 0) {
      const speed = distance / timeDiff; // m/s
      const speedKmh = speed * 3.6; // km/h
      
      // Flag unrealistic speeds
      if (speedKmh > 200) { // Faster than high-speed train
        warnings.push(`Kecepatan tidak wajar: ${speedKmh.toFixed(1)} km/h`);
        riskScore += 40;
      } else if (speedKmh > 100) { // Faster than highway speed
        warnings.push(`Kecepatan tinggi: ${speedKmh.toFixed(1)} km/h`);
        riskScore += 20;
      }
    }
  }

  // 4. Check for round numbers (common in fake GPS)
  const latDecimals = (currentLocation.latitude.toString().split('.')[1] || '').length;
  const lngDecimals = (currentLocation.longitude.toString().split('.')[1] || '').length;
  
  if (latDecimals < 4 || lngDecimals < 4) {
    warnings.push("Koordinat GPS tidak cukup presisi");
    riskScore += 15;
  }

  // 5. Check for exact same coordinates
  if (previousLocation) {
    if (previousLocation.latitude === currentLocation.latitude && 
        previousLocation.longitude === currentLocation.longitude) {
      warnings.push("Koordinat GPS tidak berubah (statis)");
      riskScore += 10;
    }
  }

  return {
    isSuspicious: riskScore > 30,
    riskScore: Math.min(riskScore, 100),
    warnings
  };
}

/**
 * Comprehensive GPS validation with geofencing and anti-spoofing
 */
export function validateGpsLocation(
  location: LocationData,
  allowedVenues: GeofenceConfig[] = DEFAULT_TRAINING_VENUES,
  previousLocation?: LocationData,
  options: {
    maxAccuracy?: number;
    requireGeofence?: boolean;
    spoofingDetection?: boolean;
  } = {}
): ValidatedLocation {
  
  const {
    maxAccuracy = 20,
    requireGeofence = true,
    spoofingDetection = true
  } = options;

  const result: ValidatedLocation = {
    ...location,
    isValid: true,
    validationFlags: {
      withinGeofence: false,
      accuracyAcceptable: false,
      speedReasonable: true,
      noSpoofingDetected: true,
      timestampValid: true
    },
    riskScore: 0,
    warnings: []
  };

  // 1. Check geofence
  const geofenceCheck = checkGeofence(location, allowedVenues);
  result.validationFlags.withinGeofence = geofenceCheck.isWithin;
  result.distance = geofenceCheck.distance;
  
  if (!geofenceCheck.isWithin && requireGeofence) {
    result.isValid = false;
    result.warnings.push(
      `Lokasi di luar area yang diizinkan. Jarak ke ${geofenceCheck.venue?.name}: ${geofenceCheck.distance?.toFixed(0)}m`
    );
  }

  // 2. Check accuracy
  result.validationFlags.accuracyAcceptable = location.accuracy <= maxAccuracy;
  if (!result.validationFlags.accuracyAcceptable) {
    result.isValid = false;
    result.warnings.push(`Akurasi GPS tidak mencukupi: ${location.accuracy}m (maksimal ${maxAccuracy}m)`);
  }

  // 3. Check timestamp
  const now = Date.now();
  const locationAge = now - location.timestamp;
  result.validationFlags.timestampValid = locationAge <= 60000; // 1 minute
  
  if (!result.validationFlags.timestampValid) {
    result.isValid = false;
    result.warnings.push("Data GPS terlalu lama");
  }

  // 4. Spoofing detection
  if (spoofingDetection) {
    const spoofingCheck = detectGpsSpoofing(location, previousLocation);
    result.validationFlags.noSpoofingDetected = !spoofingCheck.isSuspicious;
    result.riskScore = spoofingCheck.riskScore;
    result.warnings.push(...spoofingCheck.warnings);
    
    if (spoofingCheck.isSuspicious) {
      result.isValid = false;
    }
  }

  return result;
}

/**
 * Add new training venue to allowed locations
 */
export function addTrainingVenue(venue: GeofenceConfig): GeofenceConfig[] {
  return [...DEFAULT_TRAINING_VENUES, venue];
}

/**
 * Get nearest allowed venue for display purposes
 */
export function getNearestVenue(
  location: LocationData,
  venues: GeofenceConfig[] = DEFAULT_TRAINING_VENUES
): { venue: GeofenceConfig; distance: number } {
  const distances = venues.map(venue => ({
    venue,
    distance: calculateDistance(
      location.latitude,
      location.longitude,
      venue.center.lat,
      venue.center.lng
    )
  }));

  return distances.reduce((nearest, current) => 
    current.distance < nearest.distance ? current : nearest
  );
}

/**
 * Format distance for human-readable display
 */
export function formatDistance(meters: number): string {
  if (meters < 1000) {
    return `${Math.round(meters)}m`;
  } else {
    return `${(meters / 1000).toFixed(1)}km`;
  }
}

/**
 * Get risk level description
 */
export function getRiskLevelDescription(riskScore: number): {
  level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  color: string;
} {
  if (riskScore < 20) {
    return {
      level: 'LOW',
      description: 'Lokasi GPS terverifikasi dengan baik',
      color: 'green'
    };
  } else if (riskScore < 40) {
    return {
      level: 'MEDIUM',
      description: 'Ada beberapa indikasi yang perlu diperhatikan',
      color: 'yellow'
    };
  } else if (riskScore < 70) {
    return {
      level: 'HIGH',
      description: 'Lokasi GPS mencurigakan, perlu verifikasi tambahan',
      color: 'orange'
    };
  } else {
    return {
      level: 'CRITICAL',
      description: 'Kemungkinan besar GPS palsu atau dimanipulasi',
      color: 'red'
    };
  }
}

// Helper function to get default training venues in API format
export function getDefaultTrainingVenues() {
  return DEFAULT_TRAINING_VENUES.map(venue => ({
    id: `default-${venue.name.toLowerCase().replace(/\s+/g, '-')}`,
    name: venue.name,
    latitude: venue.center.lat,
    longitude: venue.center.lng,
    radius: venue.radius,
    address: venue.address || 'Alamat tidak tersedia'
  }));
}

// Helper function to convert venue data for database storage
export function convertVenueForDatabase(venue: GeofenceConfig) {
  return {
    nama: venue.name,
    alamat: venue.address || 'Alamat tidak tersedia',
    latitude: venue.center.lat,
    longitude: venue.center.lng,
    radius: venue.radius,
    is_active: true
  };
}
