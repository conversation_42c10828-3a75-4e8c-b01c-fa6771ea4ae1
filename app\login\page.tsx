'use client';
import { useState, useEffect } from 'react';
import { loginAction, getCsrfToken } from './action';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [csrfToken, setCsrfToken] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);

  // Dapatkan CSRF token saat component mount
  useEffect(() => {
    const fetchCsrfToken = async () => {
      try {
        const token = await getCsrfToken();
        setCsrfToken(token);
      } catch (err) {
        console.error('Error mendapatkan CSRF token:', err);
      }
    };
    
    fetchCsrfToken();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
   
    console.log('Client: <PERSON><PERSON><PERSON> proses login');
    try {
      const result = await loginAction(email, password, csrfToken);
      console.log('Client: Mendapatkan hasil login', result);
     
      // Tangani redirect dari server
      if (result.success && 'redirect' in result) {
        window.location.href = result.path;
        return; // Hentikan eksekusi di sini
      }
     
      if (!result.success) {
        setError(result.message || 'Login gagal');
        
        // Jika error terkait CSRF, refresh token
        if (result.message?.includes('Sesi keamanan')) {
          const newToken = await getCsrfToken();
          setCsrfToken(newToken);
        }
      }
    } catch (err) {
      console.error('Client: Login error:', err);
      setError('Terjadi kesalahan saat login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gradient-to-br from-blue-50 to-indigo-100 sm:p-6 md:p-8">
      <div className="w-full max-w-md mx-auto">
        {/* Logo atau Header */}
        <div className="mb-6 text-center md:mb-8">
          <h1 className="text-2xl font-bold text-blue-800 sm:text-3xl">Sistem Admin Kegiatan</h1>
          <p className="mt-1 text-sm text-gray-600 sm:mt-2 sm:text-base">Masuk untuk mengelola data</p>
        </div>
        
        {/* Card Login */}
        <div className="w-full overflow-hidden transition-all duration-300 bg-white shadow-lg rounded-xl hover:shadow-xl">
          <div className="px-4 py-4 text-white sm:px-6 sm:py-5 bg-gradient-to-r from-blue-600 to-blue-700">
            <h2 className="text-lg font-bold sm:text-xl">Login Admin</h2>
            <p className="mt-1 text-xs text-blue-100 sm:text-sm">Masukkan kredensial Anda</p>
          </div>
          
          <div className="p-4 sm:p-6 md:p-8"> 
            {error && (
              <div className="flex items-center p-3 mb-4 text-xs text-red-700 border-l-4 border-red-500 rounded-md sm:mb-6 sm:p-4 bg-red-50 sm:text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2 text-red-500 sm:h-5 sm:w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              {/* Hidden CSRF Token Input */}
              <input type="hidden" name="csrfToken" value={csrfToken} />
              
              <div className="space-y-1 sm:space-y-2"> 
                <label htmlFor="email" className="block text-xs font-medium text-gray-700 sm:text-sm">Email</label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="block w-full px-3 py-2 text-sm placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none sm:px-4 sm:py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-base"
                  placeholder="Masukkan email Anda"
                />
              </div>
              
              <div className="space-y-1 sm:space-y-2"> 
                <label htmlFor="password" className="block text-xs font-medium text-gray-700 sm:text-sm">Password</label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="block w-full px-3 py-2 text-sm placeholder-gray-400 border border-gray-300 rounded-md shadow-sm appearance-none sm:px-4 sm:py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-base"
                    placeholder="Masukkan password Anda"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-sm"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-2 text-xs sm:text-sm sm:mt-4">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="w-3 h-3 text-blue-600 border-gray-300 rounded sm:h-4 sm:w-4 focus:ring-blue-500"
                  />
                  <label htmlFor="remember-me" className="block ml-1 text-gray-700 sm:ml-2">
                    Ingat saya
                  </label>
                </div>
                
                
              </div>
              
              <div className="pt-2 sm:pt-4"> 
                <button
                  type="submit"
                  disabled={isLoading || !csrfToken}
                  className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white transition duration-300 bg-blue-600 border border-transparent rounded-md shadow-sm sm:py-3 sm:text-base hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {isLoading ? (
                    <>
                      <svg className="w-4 h-4 mr-2 -ml-1 text-white animate-spin sm:h-5 sm:w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Memproses...
                    </>
                  ) : (
                    'Masuk'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
        
        {/* Footer */}
        
      </div>
    </div>
  );
}