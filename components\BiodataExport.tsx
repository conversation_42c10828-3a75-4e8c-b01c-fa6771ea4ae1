'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Eye, Trash2 } from 'lucide-react';
import toast from 'react-hot-toast';

interface BiodataData {
  id: string;
  nama: string;
  tempat_lahir: string;
  tanggal_lahir: string | Date;
  pendidikan: string;
  jenis_kelamin: string;
  nip: string | null;
  pangkat_golongan: string | null;
  jabatan: string;
  unit_kerja: string;
  alamat_unit_kerja: string;
  npwp: string | null;
  email: string;
  no_hp: string;
  tanda_tangan: string;
  pelatihan: {
    id: string;
    nama: string;
  };
}

interface Props {
  biodataList: BiodataData[];
  pelatihanList: { id: string; nama: string }[];
  enableDelete?: boolean;
}

export default function BiodataExport({ biodataList, pelatihanList, enableDelete = false }: Props) {
  const [selectedPelatihan, setSelectedPelatihan] = useState<string>('');
  const [filteredData, setFilteredData] = useState<BiodataData[]>(biodataList);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);
  
  // Filter data berdasarkan pilihan pelatihan
  useEffect(() => {
    let filtered = biodataList;
    
    if (selectedPelatihan) {
      filtered = filtered.filter(item => item.pelatihan.id === selectedPelatihan);
    }
    
    setFilteredData(filtered);
    // Reset selection when filter changes
    setSelectedIds(new Set());
    setSelectAll(false);
  }, [selectedPelatihan, biodataList]);
  
  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      // If currently all selected, unselect all
      setSelectedIds(new Set());
    } else {
      // Select all filtered items
      const newSelectedIds = new Set<string>();
      filteredData.forEach(item => newSelectedIds.add(item.id));
      setSelectedIds(newSelectedIds);
    }
    setSelectAll(!selectAll);
  };
  
  // Handle individual checkbox selection
  const handleSelectItem = (id: string) => {
    const newSelectedIds = new Set(selectedIds);
    
    if (newSelectedIds.has(id)) {
      newSelectedIds.delete(id);
    } else {
      newSelectedIds.add(id);
    }
    
    setSelectedIds(newSelectedIds);
    
    // Update selectAll state based on if all items are selected
    setSelectAll(newSelectedIds.size === filteredData.length);
  };
    // Function to export selected items to PDF
  const exportToPDF = async () => {
    try {
      if (selectedIds.size === 0) {
        alert('Silakan pilih minimal satu peserta untuk diekspor');
        return;
      }
      
      setIsLoading(true);
      
      // Get list of selected IDs as array
      const selectedIdsList = Array.from(selectedIds);
      
      // Send request to API route
      const response = await fetch('/api/biodata/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          biodataIds: selectedIdsList,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Gagal mengunduh PDF');
      }
      
      // Get PDF file from response
      const blob = await response.blob();
      
      // Create URL for file and download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = selectedIds.size > 1 ? 'Biodata_Peserta_Bulk.pdf' : 'Biodata_Peserta.pdf';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
    } catch (error) {
      console.error("Error downloading PDF:", error);
      alert("Terjadi kesalahan saat mengunduh PDF");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to show delete confirmation toast
  const confirmDeleteBiodata = (id: string) => {
    setPendingDeleteId(id);
    toast(
      <div>
        <div className="mb-1 font-medium">Konfirmasi Hapus</div>
        <div>Yakin ingin menghapus data biodata ini?</div>
        <div className="flex gap-2 mt-3">
          <button
            className="px-3 py-1 text-white bg-red-600 rounded hover:bg-red-700"
            onClick={async () => {
              await handleDeleteBiodata(id);
              setPendingDeleteId(null);
              toast.dismiss();
            }}
          >
            Ya, hapus
          </button>
          <button
            className="px-3 py-1 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            onClick={() => {
              setPendingDeleteId(null);
              toast.dismiss();
            }}
          >
            Batal
          </button>
        </div>
      </div>,
      { duration: 6000 }
    );
  };

  // Function to handle individual biodata deletion
  const handleDeleteBiodata = async (id: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/biodata/delete-multiple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: [id] })
      });
      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.message || 'Gagal menghapus data');
      toast.success('Data biodata berhasil dihapus');
      setTimeout(() => window.location.reload(), 1200);
    } catch (e: any) {
      toast.error(e?.message || 'Gagal menghapus data');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to show bulk delete confirmation
  const confirmBulkDelete = () => {
    if (selectedIds.size === 0) {
      toast.error('Pilih setidaknya satu item untuk dihapus');
      return;
    }
    
    toast(
      <div>
        <div className="mb-1 font-medium">Konfirmasi Hapus Massal</div>
        <div>Yakin ingin menghapus {selectedIds.size} data biodata yang dipilih?</div>
        <div className="flex gap-2 mt-3">
          <button
            className="px-3 py-1 text-white bg-red-600 rounded hover:bg-red-700"
            onClick={async () => {
              await handleBulkDelete();
              toast.dismiss();
            }}
          >
            Ya, hapus
          </button>
          <button
            className="px-3 py-1 text-gray-700 bg-gray-200 rounded hover:bg-gray-300"
            onClick={() => {
              toast.dismiss();
            }}
          >
            Batal
          </button>
        </div>
      </div>,
      { duration: 6000 }
    );
  };

  // Function to handle bulk deletion
  const handleBulkDelete = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/biodata/delete-multiple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: Array.from(selectedIds) })
      });
      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.message || 'Gagal menghapus data');
      toast.success(`${selectedIds.size} data biodata berhasil dihapus`);
      setSelectedIds(new Set());
      setSelectAll(false);
      setTimeout(() => window.location.reload(), 1200);
    } catch (e: any) {
      toast.error(e?.message || 'Gagal menghapus data');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="p-4 mb-6 bg-white rounded-lg shadow">
      <h2 className="mb-4 text-lg font-medium">Export Biodata Peserta</h2>
      
      <div className="grid grid-cols-1 gap-4 mb-4 md:grid-cols-2">
        <div>
          <label className="block mb-1 text-sm font-medium text-gray-700">
            Pilih Kegiatan
          </label>
          <select
            value={selectedPelatihan}
            onChange={(e) => setSelectedPelatihan(e.target.value)}
            className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          >
            <option value="">Semua Kegiatan</option>
            {pelatihanList.map((item) => (
              <option key={item.id} value={item.id}>
                {item.nama}
              </option>
            ))}
          </select>
        </div>
            <div className="flex items-end gap-2">
          <button
            onClick={exportToPDF}
            disabled={selectedIds.size === 0 || isLoading}
            className="flex items-center px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <svg className="w-4 h-4 mr-2 -ml-1 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Memproses...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export ke PDF
              </>
            )}
          </button>
          
          {enableDelete && (
            <button
              onClick={confirmBulkDelete}
              disabled={selectedIds.size === 0 || isLoading}
              className="flex items-center px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Hapus Dipilih ({selectedIds.size})
            </button>
          )}
        </div>
      </div>
      
      <div className="flex items-center justify-between mb-2 text-sm text-gray-500">
        <span>Data ditampilkan: {filteredData.length} dari {biodataList.length} biodata</span>
        <span>Dipilih: {selectedIds.size} peserta</span>
      </div>
      
      {/* Table of data to be exported */}
      {filteredData.length > 0 && (
        <div className="mt-4 overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-2 py-3 text-center">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="text-blue-600 border-gray-300 rounded shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  />
                </th>
                <th scope="col" className="w-16 px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  No
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Nama
                </th>                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  NIP
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Unit Kerja
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  Email
                </th>
                <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">
                  Aksi
                </th>              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">{filteredData.map((item, index) => (
              <tr key={item.id} className={selectedIds.has(item.id) ? "bg-blue-50" : ""}>
                <td className="px-2 py-4 text-center whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedIds.has(item.id)}
                    onChange={() => handleSelectItem(item.id)}
                    className="text-blue-600 border-gray-300 rounded shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  />
                </td>
                <td className="px-6 py-4 text-sm text-center text-gray-500 whitespace-nowrap">
                  {index + 1}
                </td>
                <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">
                  {item.nama}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                  {item.nip || '-'}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                  {item.unit_kerja}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                  {item.email}
                </td>
                <td className="px-6 py-4 text-sm text-center whitespace-nowrap">
                  <div className="flex items-center justify-center gap-2">
                    <Link
                      href={`/dashboard/biodata/${item.id}`}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 transition-colors bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 hover:text-blue-700"
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Detail
                    </Link>
                    {enableDelete && (
                      <button
                        onClick={() => confirmDeleteBiodata(item.id)}
                        disabled={isLoading || pendingDeleteId === item.id}
                        className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 transition-colors bg-red-100 border border-red-300 rounded-md hover:bg-red-200 hover:text-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Trash2 className="w-3 h-3 mr-1" />
                        {pendingDeleteId === item.id ? 'Menghapus...' : 'Hapus'}
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}</tbody>
          </table>
        </div>
      )}
    </div>
  );
}