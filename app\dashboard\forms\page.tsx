'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, FileText, Edit, Trash2, Co<PERSON>, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { formatDate } from '@/utils/helpers';

export default function FormsPage() {
  const router = useRouter();
  const [forms, setForms] = useState<any[]>([]);
  const [pelatihans, setPelatihans] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newFormTitle, setNewFormTitle] = useState('');
  const [newFormDescription, setNewFormDescription] = useState('');
  const [selectedPelatihanId, setSelectedPelatihanId] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    // Fetch forms and pelatihans
    const fetchData = async () => {
      try {
        // Fetch pelatihans first
        const pelatihanResponse = await fetch('/api/pelatihan');
        if (pelatihanResponse.ok) {
          const pelatihanData = await pelatihanResponse.json();
          setPelatihans(pelatihanData);

          // Set default selected pelatihan if available
          if (pelatihanData.length > 0) {
            setSelectedPelatihanId(pelatihanData[0].id);
          }
        }

        // Then fetch forms
        const formsResponse = await fetch('/api/forms');
        if (formsResponse.ok) {
          const formsData = await formsResponse.json();
          setForms(formsData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleCreateForm = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newFormTitle.trim() || !selectedPelatihanId) {
      return;
    }

    try {
      const response = await fetch('/api/forms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newFormTitle,
          description: newFormDescription,
          pelatihanId: selectedPelatihanId,
        }),
      });

      if (response.ok) {
        const newForm = await response.json();
        setForms([...forms, newForm]);
        setNewFormTitle('');
        setNewFormDescription('');
        setIsCreateDialogOpen(false);

        // Navigate to form editor
        router.push(`/dashboard/forms/${newForm.id}/edit`);
      }
    } catch (error) {
      console.error('Error creating form:', error);
    }
  };

  const handleDeleteForm = async (formId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus formulir ini?')) {
      return;
    }

    try {
      const response = await fetch(`/api/forms/${formId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setForms(forms.filter(form => form.id !== formId));
      }
    } catch (error) {
      console.error('Error deleting form:', error);
    }
  };

  const handleDuplicateForm = async (formId: string) => {
    try {
      const response = await fetch(`/api/forms/${formId}/duplicate`, {
        method: 'POST',
      });

      if (response.ok) {
        const duplicatedForm = await response.json();
        setForms([...forms, duplicatedForm]);
      }
    } catch (error) {
      console.error('Error duplicating form:', error);
    }
  };

  const filteredForms = forms.filter(form => {
    if (activeTab === 'all') return !form.isDeleted;
    if (activeTab === 'published') return form.isPublished && !form.isDeleted;
    if (activeTab === 'drafts') return !form.isPublished && !form.isDeleted;
    if (activeTab === 'trash') return form.isDeleted;
    return true;
  });

  return (
    <div className="container py-6 mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Formulir</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Buat Formulir
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleCreateForm}>
              <DialogHeader>
                <DialogTitle>Buat Formulir Baru</DialogTitle>
                <DialogDescription>
                  Buat formulir baru untuk mengumpulkan data dari responden.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="pelatihan">Kegiatan</Label>
                  <select
                    id="pelatihan"
                    className="flex w-full h-10 px-3 py-2 text-sm border rounded-md border-input bg-background ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={selectedPelatihanId}
                    onChange={(e) => setSelectedPelatihanId(e.target.value)}
                    required
                  >
                    <option value="">Pilih Kegiatan</option>
                    {pelatihans.map((pelatihan) => (
                      <option key={pelatihan.id} value={pelatihan.id}>
                        {pelatihan.nama}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="title">Judul Formulir</Label>
                  <Input
                    id="title"
                    value={newFormTitle}
                    onChange={(e) => setNewFormTitle(e.target.value)}
                    placeholder="Masukkan judul formulir"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="description">Deskripsi (opsional)</Label>
                  <Input
                    id="description"
                    value={newFormDescription}
                    onChange={(e) => setNewFormDescription(e.target.value)}
                    placeholder="Masukkan deskripsi formulir"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Batal
                </Button>
                <Button type="submit">Buat</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="all">Semua</TabsTrigger>
          <TabsTrigger value="published">Dipublikasikan</TabsTrigger>
          <TabsTrigger value="drafts">Draft</TabsTrigger>
          <TabsTrigger value="trash">Sampah</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <p>Memuat formulir...</p>
            </div>
          ) : filteredForms.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <FileText className="w-12 h-12 mb-4 text-gray-400" />
              <h3 className="text-lg font-medium">Tidak ada formulir</h3>
              <p className="mt-1 mb-4 text-sm text-gray-500">
                {activeTab === 'all' && 'Anda belum membuat formulir apapun.'}
                {activeTab === 'published' && 'Anda belum mempublikasikan formulir apapun.'}
                {activeTab === 'drafts' && 'Anda tidak memiliki draft formulir.'}
                {activeTab === 'trash' && 'Tidak ada formulir di sampah.'}
              </p>
              {activeTab !== 'trash' && (
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  Buat Formulir
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredForms.map((form) => (
                <Card key={form.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-xl truncate">{form.title}</CardTitle>
                    <CardDescription className="truncate">
                      {form.description || 'Tidak ada deskripsi'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="text-sm text-gray-500">
                      <p>Kegiatan: {form.pelatihan?.nama || 'Tidak ada'}</p>
                      <p>Dibuat: {formatDate(form.createdAt)}</p>
                      <p>Diperbarui: {formatDate(form.updatedAt)}</p>
                      <p>Status: {form.isPublished ? 'Dipublikasikan' : 'Draft'}</p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/dashboard/forms/${form.id}/responses`)}
                    >
                      Lihat Respons
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          Opsi
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/forms/${form.id}/edit`)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => router.push(`/dashboard/forms/${form.id}/preview`)}>
                          <Eye className="w-4 h-4 mr-2" />
                          Pratinjau
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDuplicateForm(form.id)}>
                          <Copy className="w-4 h-4 mr-2" />
                          Duplikat
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteForm(form.id)}
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
