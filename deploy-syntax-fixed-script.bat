@echo off
setlocal enabledelayedexpansion

REM ===================================================================
REM   CloudPanel Real-time Photo Fix - Syntax-Fixed Deployment
REM   Deploys the corrected script with resolved quote matching issues
REM ===================================================================

echo.
echo ========================================================
echo   DEPLOYING SYNTAX-FIXED REAL-TIME PHOTO SOLUTION
echo ========================================================
echo.

REM Configuration
set "VPS_HOST=kegiatan.bpmpkaltim.id"
set "VPS_USER=root"
set "LOCAL_SCRIPT=cloudpanel-realtime-photo-fix.sh"
set "REMOTE_SCRIPT=/tmp/cloudpanel-realtime-photo-fix.sh"
set "LOG_FILE=deploy-syntax-fixed-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log"

REM Clean log filename
set "LOG_FILE=%LOG_FILE: =0%"

echo [%date% %time%] Starting syntax-fixed deployment... > "%LOG_FILE%"
echo Target: %VPS_HOST%
echo Script: %LOCAL_SCRIPT%
echo Log: %LOG_FILE%
echo.

REM Verify local script exists and has good syntax
if not exist "%LOCAL_SCRIPT%" (
    echo ERROR: Script %LOCAL_SCRIPT% not found!
    echo [%date% %time%] ERROR: Local script not found >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo Verifying script syntax...
bash -n "%LOCAL_SCRIPT%" 2>syntax-check.tmp
if %ERRORLEVEL% neq 0 (
    echo ERROR: Script has syntax errors:
    type syntax-check.tmp
    echo [%date% %time%] ERROR: Syntax check failed >> "%LOG_FILE%"
    del syntax-check.tmp
    pause
    exit /b 1
)
del syntax-check.tmp
echo ✓ Syntax check passed
echo.

REM Upload the fixed script
echo Uploading syntax-fixed script to VPS...
echo [%date% %time%] Uploading script... >> "%LOG_FILE%"

scp "%LOCAL_SCRIPT%" %VPS_USER%@%VPS_HOST%:"%REMOTE_SCRIPT%" 2>&1 | tee -a "%LOG_FILE%"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Upload failed!
    echo [%date% %time%] ERROR: Upload failed >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo ✓ Upload successful
echo.

REM Execute the script on VPS
echo Executing syntax-fixed real-time photo solution...
echo [%date% %time%] Executing script... >> "%LOG_FILE%"
echo.

ssh %VPS_USER%@%VPS_HOST% "chmod +x %REMOTE_SCRIPT% && %REMOTE_SCRIPT%" 2>&1 | tee -a "%LOG_FILE%"

set "EXEC_RESULT=%ERRORLEVEL%"

REM Check execution result
if %EXEC_RESULT% equ 0 (
    echo.
    echo ========================================================
    echo   ✓ SYNTAX-FIXED DEPLOYMENT SUCCESSFUL!
    echo ========================================================
    echo.
    echo The real-time photo fix has been deployed with:
    echo • Resolved quote matching issues
    echo • Fixed JPEG creation command
    echo • Enhanced binary data handling
    echo.
    echo Photo uploads should now work immediately without
    echo manual nginx reloads on kegiatan.bpmpkaltim.id
    echo.
    echo [%date% %time%] Deployment successful >> "%LOG_FILE%"
) else (
    echo.
    echo ========================================================
    echo   ⚠ DEPLOYMENT COMPLETED WITH WARNINGS
    echo ========================================================
    echo.
    echo The script executed but returned exit code: %EXEC_RESULT%
    echo Check the output above for any issues.
    echo.
    echo [%date% %time%] Deployment completed with warnings (code %EXEC_RESULT%) >> "%LOG_FILE%"
)

REM Test the fix
echo.
echo Testing real-time functionality...
echo [%date% %time%] Testing functionality... >> "%LOG_FILE%"

ssh %VPS_USER%@%VPS_HOST% "systemctl is-active cloudpanel-realtime-photo 2>/dev/null || echo 'Service not running'"

echo.
echo Deployment log saved to: %LOG_FILE%
echo.
pause
