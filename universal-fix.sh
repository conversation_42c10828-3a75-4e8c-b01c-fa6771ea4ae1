#!/bin/bash

# Script Universal Fix - Bekerja dengan berbagai setup VPS
# Otomatis mendeteksi dan memperbaiki masalah photo upload

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}"
echo "============================================"
echo "   UNIVERSAL PHOTO UPLOAD FIX"
echo "============================================"
echo -e "${NC}"

# Verifikasi user permission
if [ "$EUID" -ne 0 ]; then
    log_error "Script ini harus dijalankan dengan sudo"
    echo "Gunakan: sudo $0"
    exit 1
fi

BACKUP_DIR="/tmp/backup-uploads-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
log_success "Backup directory: $BACKUP_DIR"

echo ""
log_info "=== STEP 1: AUTO-DETECT APLIKASI ===="

# Cari aplikasi Next.js
DETECTED_APPS=$(find /home /var/www /opt -name "server.js" 2>/dev/null | head -10)

if [ -z "$DETECTED_APPS" ]; then
    log_error "Aplikasi Next.js tidak ditemukan!"
    echo "Cari manual dengan: find / -name 'server.js' 2>/dev/null"
    exit 1
fi

APP_DIR=""
for app_path in $DETECTED_APPS; do
    dir_path=$(dirname "$app_path")
    if [ -d "$dir_path/public" ]; then
        APP_DIR="$dir_path"
        log_success "Aplikasi ditemukan: $APP_DIR"
        break
    fi
done

if [ -z "$APP_DIR" ]; then
    log_error "Tidak ada aplikasi dengan folder public yang ditemukan"
    exit 1
fi

echo ""
log_info "=== STEP 2: BACKUP & ANALISIS ===="

# Backup public folder
if [ -d "$APP_DIR/public" ]; then
    cp -r "$APP_DIR/public" "$BACKUP_DIR/"
    log_success "Backup public folder selesai"
fi

# Analisis struktur
HAS_DUPLICATE=false
HAS_UPLOADS=false
PHOTOS_IN_DUPLICATE=0
PHOTOS_IN_NORMAL=0

if [ -d "$APP_DIR/public/public" ]; then
    HAS_DUPLICATE=true
    log_warning "Duplikasi folder ditemukan: public/public/"
    
    if [ -d "$APP_DIR/public/public/uploads" ]; then
        PHOTOS_IN_DUPLICATE=$(find "$APP_DIR/public/public/uploads" -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l)
        log_warning "Foto di duplikasi: $PHOTOS_IN_DUPLICATE files"
    fi
fi

if [ -d "$APP_DIR/public/uploads" ]; then
    HAS_UPLOADS=true
    PHOTOS_IN_NORMAL=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l)
    log_info "Foto di lokasi normal: $PHOTOS_IN_NORMAL files"
fi

echo ""
log_info "=== STEP 3: PERBAIKAN STRUKTUR FOLDER ===="

# Perbaiki duplikasi jika ada
if [ "$HAS_DUPLICATE" = true ]; then
    log_info "Memperbaiki duplikasi folder..."
    
    if [ -d "$APP_DIR/public/public/uploads" ] && [ "$PHOTOS_IN_DUPLICATE" -gt 0 ]; then
        # Pindahkan uploads dari duplikasi
        mkdir -p "$APP_DIR/public/uploads"
        
        log_info "Memindahkan $PHOTOS_IN_DUPLICATE foto dari duplikasi..."
        cp -r "$APP_DIR/public/public/uploads/"* "$APP_DIR/public/uploads/" 2>/dev/null || true
        
        # Verifikasi perpindahan
        NEW_COUNT=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l)
        log_success "Foto berhasil dipindahkan: $NEW_COUNT files"
    fi
    
    # Hapus duplikasi
    rm -rf "$APP_DIR/public/public"
    log_success "Folder duplikasi dihapus"
fi

# Buat struktur folder yang diperlukan
REQUIRED_DIRS=(
    "$APP_DIR/public/uploads"
    "$APP_DIR/public/uploads/absensi"
    "$APP_DIR/public/uploads/absensi/photos"
    "$APP_DIR/public/uploads/biodata"
    "$APP_DIR/public/uploads/biodata/photos"
    "$APP_DIR/public/uploads/materi"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    mkdir -p "$dir"
    log_success "Struktur folder: $(echo $dir | sed "s|$APP_DIR/public/||")"
done

echo ""
log_info "=== STEP 4: SET PERMISSIONS ===="

chown -R www-data:www-data "$APP_DIR/public/uploads"
chmod -R 755 "$APP_DIR/public/uploads"
log_success "Permissions diset: www-data:www-data 755"

echo ""
log_info "=== STEP 5: AUTO-DETECT & FIX NGINX ===="

# Cari file konfigurasi nginx yang mengandung domain
NGINX_CONFIG=""
POSSIBLE_CONFIGS=$(find /etc -name "*.conf" -path "*/nginx/*" 2>/dev/null)

for config in $POSSIBLE_CONFIGS; do
    if grep -q "kegiatan.bpmpkaltim.id" "$config" 2>/dev/null; then
        NGINX_CONFIG="$config"
        log_success "Nginx config ditemukan: $config"
        break
    fi
done

# Jika tidak ditemukan, coba default
if [ -z "$NGINX_CONFIG" ]; then
    if [ -f "/etc/nginx/sites-available/default" ]; then
        NGINX_CONFIG="/etc/nginx/sites-available/default"
        log_warning "Menggunakan default nginx config: $NGINX_CONFIG"
    elif [ -f "/etc/nginx/nginx.conf" ]; then
        NGINX_CONFIG="/etc/nginx/nginx.conf"
        log_warning "Menggunakan nginx.conf utama: $NGINX_CONFIG"
    else
        log_error "Tidak dapat menemukan file konfigurasi nginx!"
        exit 1
    fi
fi

# Backup nginx config
cp "$NGINX_CONFIG" "$BACKUP_DIR/nginx-config-backup"
log_success "Nginx config di-backup"

# Cek apakah sudah ada konfigurasi uploads
if grep -q "location /uploads/" "$NGINX_CONFIG"; then
    log_info "Konfigurasi uploads sudah ada, mengupdate..."
    
    # Update existing configuration
    sed -i "/location \/uploads\//,/}/c\\
    location /uploads/ {\\
        root $APP_DIR/public;\\
        expires 1y;\\
        add_header Cache-Control \"public, immutable\";\\
        try_files \$uri \$uri/ =404;\\
    }" "$NGINX_CONFIG"
    
else
    log_info "Menambahkan konfigurasi uploads baru..."
    
    # Tambahkan konfigurasi baru
    if grep -q "server {" "$NGINX_CONFIG"; then
        # Tambahkan setelah server {
        sed -i '/server {/a\
    # Static file serving for uploads\
    location /uploads/ {\
        root '"$APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        try_files $uri $uri/ =404;\
    }\
' "$NGINX_CONFIG"
    else
        log_error "Format nginx config tidak dikenali!"
        exit 1
    fi
fi

log_success "Konfigurasi nginx diupdate"

# Test nginx config
if nginx -t 2>/dev/null; then
    log_success "Nginx config valid"
    systemctl reload nginx
    log_success "Nginx direload"
else
    log_error "Nginx config error! Restoring backup..."
    cp "$BACKUP_DIR/nginx-config-backup" "$NGINX_CONFIG"
    exit 1
fi

echo ""
log_info "=== STEP 6: RESTART APLIKASI ===="

# Restart PM2 jika ada
if command -v pm2 >/dev/null 2>&1; then
    pm2 restart all 2>/dev/null || true
    log_success "PM2 aplikasi direstart"
fi

# Restart systemd service jika ada
if systemctl is-active --quiet nextjs-app 2>/dev/null; then
    systemctl restart nextjs-app
    log_success "Systemd service direstart"
fi

echo ""
log_info "=== STEP 7: VERIFIKASI HASIL ===="

# Tampilkan ringkasan
FINAL_PHOTO_COUNT=$(find "$APP_DIR/public/uploads" -name "*.jpg" -o -name "*.png" 2>/dev/null | wc -l)
log_success "Total foto setelah perbaikan: $FINAL_PHOTO_COUNT files"

# Test sample URL jika ada foto
SAMPLE_PHOTOS=$(find "$APP_DIR/public/uploads/absensi/photos" -name "*.jpg" | head -1 2>/dev/null)
if [ -n "$SAMPLE_PHOTOS" ]; then
    REL_PATH=${SAMPLE_PHOTOS#$APP_DIR/public}
    TEST_URL="https://kegiatan.bpmpkaltim.id$REL_PATH"
    
    log_info "Testing URL: $TEST_URL"
    
    if curl -s -I "$TEST_URL" | grep -q "200 OK"; then
        log_success "✅ FOTO DAPAT DIAKSES!"
    else
        log_warning "❌ Foto belum dapat diakses, tunggu beberapa detik dan coba lagi"
    fi
fi

echo ""
echo -e "${GREEN}"
echo "============================================"
echo "   PERBAIKAN UNIVERSAL SELESAI!"
echo "============================================"
echo -e "${NC}"
echo ""
echo "📋 RINGKASAN:"
echo "✅ Aplikasi: $APP_DIR"
echo "✅ Nginx config: $NGINX_CONFIG"
echo "✅ Total foto: $FINAL_PHOTO_COUNT files"
echo "✅ Backup: $BACKUP_DIR"
echo ""
echo "🧪 TESTING:"
echo "1. Upload foto baru di form absensi"
echo "2. Cek admin panel → Detail absensi"
echo "3. Verifikasi foto muncul tanpa error"
echo ""
echo "🆘 JIKA MASIH BERMASALAH:"
echo "1. Tunggu 30 detik untuk nginx cache clear"
echo "2. Test direct URL: https://kegiatan.bpmpkaltim.id/uploads/..."
echo "3. Cek logs: tail -f /var/log/nginx/error.log"
