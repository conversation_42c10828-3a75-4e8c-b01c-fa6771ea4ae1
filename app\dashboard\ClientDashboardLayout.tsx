'use client';

import { ReactNode, useState, Fragment, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Dialog, Transition } from '@headlessui/react';
import LogoutButton from '../../components/LogoutButton';
import dynamic from 'next/dynamic';


// Buat komponen FormIcon yang konsisten dengan ikon lainnya
const FormIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
  </svg>
);

// Menu Icons (digunakan sebagai contoh saja - ganti dengan ikon yang sesuai)
const DashboardIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
  </svg>
);

const PelatihanIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
  </svg>
);

const BiodataIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
  </svg>
);

const AbsensiIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
  </svg>
);

// Tambahkan ikon Users untuk menu manajemen pengguna
const UsersIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
  </svg>
);

// Tambahkan ikon Log untuk menu log error
const LogIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
  </svg>
);

// Tambahkan ikon untuk rekap penugasan
const RekapPenugasanIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
  </svg>
);

// Tambahkan ikon untuk tempat tugas
const TempatTugasIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
  </svg>
);

// Add ProfileIcon
const ProfileIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
  </svg>
);

// Add VenueIcon for venue management  
const VenueIcon = () => (
  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
  </svg>
);

// Komponen NoSSR untuk membungkus konten yang harus dirender hanya di client
const NoSSR = ({ children }: { children: ReactNode }) => {
  return <>{children}</>;
};

// Membuat wrapper NoSSR yang aman dengan dynamic import
const ClientOnlyPortal = dynamic(() => Promise.resolve(NoSSR), {
  ssr: false
});

// Loading indicator component yang di-render hanya di sisi client
const LoadingIndicator = ({ isRouteChanging }: { isRouteChanging: boolean }) => {
  if (!isRouteChanging) return null;
  
  return (
    <div className="fixed top-0 left-0 z-50 w-full h-0.5">
      <div className="h-full bg-blue-600 animate-pulse-width"></div>
    </div>
  );
};

interface ClientDashboardLayoutProps {
  children: ReactNode;
  user: { name: string; role: string };
}

export default function ClientDashboardLayout({ children, user }: ClientDashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isRouteChanging, setIsRouteChanging] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  
  // Menandai bahwa komponen sudah di-mount pada client side
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Setup global error logging dan console interceptor hanya di client side
  useEffect(() => {
    if (!isMounted) return;
    
    // Import utils hanya di client
    const setupUtils = async () => {
      try {
        const { setupGlobalErrorLogging } = await import('../../utils/logger');
        const { setupConsoleInterceptor } = await import('../../utils/consoleInterceptor');
        
        setupGlobalErrorLogging();
        setupConsoleInterceptor();
      } catch (_error) {
        // Remove console.error
      }
    };
    
    setupUtils();
  }, [isMounted]);
  
  // Handle route change for loading indicator
  useEffect(() => {
    if (!isMounted) return;
    
    setIsRouteChanging(false);
    
    const handleClick = () => {
      setIsRouteChanging(true);
    };
    
    document.addEventListener('click', handleClick);
    return () => document.removeEventListener('click', handleClick);
  }, [pathname, isMounted]);

  const isGM = user.role === 'GM1' || user.role === 'GM2' || user.role === 'GM3' || 
               user.role === 'GM4' || user.role === 'GM5';
  const isAdmin = user.role === 'ADMIN';
  const isKepala = user.role === 'KEPALA';
  const isKasubag = user.role === 'KASUBAG';
  const isViewOnly = isKepala || isKasubag;
  const canManageData = isAdmin || isGM;

  const getUserRoleDisplay = () => {
    if (isAdmin) return 'Administrator';
    if (isGM) return 'Gugus Mutu';
    if (isKepala) return 'Kepala';
    if (isKasubag) return 'Kasubag';
    return user.role;
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      alert(`Search for: ${searchQuery}`);
      setSearchQuery('');
    }
  };

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', current: pathname === '/dashboard', icon: <DashboardIcon /> },
    { name: 'Kegiatan', href: '/dashboard/pelatihan', current: pathname.startsWith('/dashboard/pelatihan'), icon: <PelatihanIcon /> },
    { name: 'Form', href: '/dashboard/forms', current: pathname.startsWith('/dashboard/forms'), icon: <FormIcon /> },
    ...(canManageData || isViewOnly ? [
      { name: 'Biodata Peserta', href: '/dashboard/biodata', current: pathname.startsWith('/dashboard/biodata'), icon: <BiodataIcon /> },
      { name: 'Absensi', href: '/dashboard/absensi', current: pathname.startsWith('/dashboard/absensi'), icon: <AbsensiIcon /> },
      { name: 'Rekap Penugasan', href: '/dashboard/rekap-penugasan', current: pathname.startsWith('/dashboard/rekap-penugasan'), icon: <RekapPenugasanIcon /> },
    ] : []),    ...(isAdmin ? [
      { name: 'Manajemen Pengguna', href: '/dashboard/users', current: pathname.startsWith('/dashboard/users'), icon: <UsersIcon /> },
      { name: 'Venue Pelatihan', href: '/dashboard/venue-management', current: pathname.startsWith('/dashboard/venue-management'), icon: <VenueIcon /> },
      { name: 'Tempat Tugas', href: '/dashboard/tempat-tugas', current: pathname.startsWith('/dashboard/tempat-tugas'), icon: <TempatTugasIcon /> },
      { name: 'Log Error', href: '/dashboard/logs', current: pathname.startsWith('/dashboard/logs'), icon: <LogIcon /> },
    ] : []),
    ...(isGM || isKepala || isKasubag ? [
      { name: 'Profil', href: '/dashboard/profile', current: pathname.startsWith('/dashboard/profile'), icon: <ProfileIcon /> },
    ] : []),
    ...(isAdmin && isGM ? [
      { name: 'Venue', href: '/dashboard/venue', current: pathname.startsWith('/dashboard/venue'), icon: <VenueIcon /> },
    ] : []),
  ];

  const getBreadcrumb = () => {
    if (pathname === '/dashboard') return 'Dashboard';
    
    const pathSegments = pathname.split('/').filter(segment => segment);
    if (pathSegments.length < 2) return '';
    
    const section = pathSegments[1];
    const capitalized = section.charAt(0).toUpperCase() + section.slice(1);
    
    return capitalized;
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gray-100">
      {/* Gunakan ClientOnlyPortal untuk konten yang hanya di-render di client */}
      <ClientOnlyPortal>
        {isRouteChanging && <LoadingIndicator isRouteChanging={true} />}
      </ClientOnlyPortal>
      
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="fixed inset-0 z-40 flex md:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Panel className="fixed inset-0 bg-gray-600 bg-opacity-75" />
          </Transition.Child>
          
          <Transition.Child
            as={Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <div className="relative flex flex-col flex-1 w-full max-w-xs bg-white">
              <Transition.Child
                as={Fragment}
                enter="ease-in-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in-out duration-300"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="absolute top-0 right-0 pt-2 -mr-12">
                  <button
                    type="button"
                    className="flex items-center justify-center w-10 h-10 ml-1 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                    onClick={() => setSidebarOpen(false)}
                  >
                    <span className="sr-only">Close sidebar</span>
                    <svg
                      className="w-6 h-6 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </Transition.Child>
              
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
                <Dialog.Title className="flex items-center text-lg font-medium text-gray-900">
                  <Image 
                    src="/logo.png" 
                    alt="Logo Pelatihan" 
                    width={32} 
                    height={32} 
                    className="mr-2" 
                  />
                  Dashboard Kegiatan
                </Dialog.Title>
              </div>
              
              <div className="px-2 py-2 space-y-1">
                <form onSubmit={handleSearch} className="flex items-center space-x-1">
                  <input
                    type="text"
                    placeholder="Cari..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="submit"
                    className="p-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </button>
                </form>
              </div>
              
              <div className="flex-1 h-0 overflow-y-auto">
                <nav className="px-2 py-4 space-y-1">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`${
                        item.current
                          ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      } group flex items-center px-2 py-2 text-base font-medium rounded-md transition-all duration-200`}
                    >
                      {item.icon}
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
            </div>
          </Transition.Child>
          
          <div className="flex-shrink-0 w-14" aria-hidden="true">
          </div>
        </Dialog>
      </Transition.Root>

      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col flex-1 min-h-0 bg-white border-r border-gray-200">
            <div className="flex items-center justify-between flex-shrink-0 h-16 px-4 border-b border-gray-200 bg-blue-50">
              <div className="flex items-center">
                <Image 
                  src="/logo.png" 
                  alt="Logo Pelatihan" 
                  width={32} 
                  height={32} 
                  className="mr-2" 
                />
                <span className="text-lg font-medium text-gray-900">Dashboard Kegiatan</span>
              </div>
            </div>
            
            <div className="px-3 py-2">
              <form onSubmit={handleSearch} className="flex items-center space-x-1">
                <input
                  type="text"
                  placeholder="Cari..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="submit"
                  className="p-1.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </form>
            </div>
            
            <div className="flex flex-col flex-1 overflow-y-auto">
              <nav className="flex-1 px-2 py-4 space-y-1">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`${
                      item.current
                        ? 'bg-blue-50 text-blue-700 border-l-4 border-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200`}
                  >
                    {item.icon}
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="relative z-10 flex flex-shrink-0 h-16 bg-white shadow md:hidden">
          <button
            type="button"
            className="px-4 text-gray-500 border-r border-gray-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <svg
              className="w-6 h-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <div className="flex justify-between flex-1 px-4">
            <div className="flex items-center flex-1">
              {pathname !== '/dashboard' && (
                <span className="text-sm font-medium text-gray-500">
                  {getBreadcrumb()}
                </span>
              )}
            </div>
            <div className="flex items-center ml-4">
              <div className="flex flex-col items-end mr-2">
                <span className="text-sm font-medium text-gray-700 truncate">
                  {user.name}
                </span>
                <span className="text-xs text-gray-500">
                  {getUserRoleDisplay()}
                </span>
              </div>
              <LogoutButton />
            </div>
          </div>
        </div>

        <div className="hidden bg-white md:flex md:items-center md:justify-between md:px-4 md:py-2 md:border-b md:border-gray-200">
          <div>
            {pathname !== '/dashboard' && (
              <div className="flex items-center text-sm">
                <Link href="/dashboard" className="text-blue-600 hover:text-blue-800">
                  Dashboard
                </Link>
                <svg className="w-3 h-3 mx-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-gray-700">{getBreadcrumb()}</span>
              </div>
            )}
          </div>
          <div className="flex items-center">
            <div className="flex flex-col items-end mr-2">
              <span className="text-sm font-medium text-gray-700">
                {user.name}
              </span>
              <span className="text-xs text-gray-500">
                {getUserRoleDisplay()}
              </span>
            </div>
            <LogoutButton />
          </div>
        </div>

        <main className="relative flex-1 overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-full px-4 mx-auto sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
