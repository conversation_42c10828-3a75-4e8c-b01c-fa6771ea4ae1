import { NextRequest, NextResponse } from 'next/server';

// Create a timeout-enabled fetch function
const fetchWithTimeout = async (url: string, options: any = {}, timeoutMs: number = 5000) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

// Simplified geocoding service - only use reliable Nominatim
const geocodingServices = [
  {
    name: 'Nominatim',
    url: (lat: string, lng: string) => 
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=16&addressdetails=1&limit=1`,
    headers: {
      'User-Agent': 'Zoom-Rutin-App/1.0',
      'Accept-Language': 'id,en',
      'Accept': 'application/json'
    },
    timeout: 4000, // Increased timeout for better reliability
    parseResponse: (data: any) => {
      if (data && data.display_name) {
        return data.display_name;
      }
      // Try to construct address from components if display_name unavailable
      if (data && data.address) {
        const parts = [];
        if (data.address.road) parts.push(data.address.road);
        if (data.address.village || data.address.suburb) parts.push(data.address.village || data.address.suburb);
        if (data.address.city || data.address.town) parts.push(data.address.city || data.address.town);
        if (data.address.state) parts.push(data.address.state);
        if (data.address.country) parts.push(data.address.country);
        return parts.length > 0 ? parts.join(', ') : null;
      }
      return null;
    }
  }
];

// Simple in-memory cache to reduce API calls for same coordinates
const geocodeCache = new Map<string, { address: string; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const lat = searchParams.get('lat');
  const lng = searchParams.get('lng');

  if (!lat || !lng) {
    return NextResponse.json(
      { error: 'Missing latitude or longitude parameters' },
      { status: 400 }
    );
  }

  // Create cache key with reduced precision to improve cache hits
  const cacheKey = `${parseFloat(lat).toFixed(4)},${parseFloat(lng).toFixed(4)}`;
  
  // Check cache first
  const cached = geocodeCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log('Returning cached geocoding result');
    return NextResponse.json({
      address: cached.address,
      display_name: cached.address,
      details: {},
      lat: parseFloat(lat),
      lng: parseFloat(lng),
      source: 'cache'
    });
  }

  // Fallback coordinates response
  const fallbackResponse = {
    address: `Koordinat: ${lat}, ${lng}`,
    display_name: `Koordinat: ${lat}, ${lng}`,
    details: {},
    lat: parseFloat(lat),
    lng: parseFloat(lng),
    source: 'fallback'
  };

  // Try Nominatim with improved error handling
  const service = geocodingServices[0]; // Only use Nominatim now
  
  try {
    console.log(`Trying ${service.name} geocoding service...`);
    
    const response = await fetchWithTimeout(
      service.url(lat, lng),
      { headers: service.headers },
      service.timeout
    );

    if (!response.ok) {
      console.log(`${service.name} returned status: ${response.status}`);
      
      // For rate limiting (429) or server errors (5xx), return fallback immediately
      if (response.status === 429 || response.status >= 500) {
        console.log('Rate limited or server error, using coordinates fallback');
        return NextResponse.json(fallbackResponse);
      }
      
      // For other errors, still try to get the response
      if (response.status >= 400) {
        return NextResponse.json(fallbackResponse);
      }
    }

    const data = await response.json();
    const address = service.parseResponse(data);

    if (address) {
      console.log(`${service.name} geocoding successful`);
      
      // Cache the successful result
      geocodeCache.set(cacheKey, { address, timestamp: Date.now() });
      
      // Clean old cache entries occasionally
      if (geocodeCache.size > 100) {
        const now = Date.now();
        for (const [key, value] of geocodeCache.entries()) {
          if (now - value.timestamp > CACHE_DURATION) {
            geocodeCache.delete(key);
          }
        }
      }
      
      return NextResponse.json({
        address: address,
        display_name: address,
        details: data.address || {},
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        source: service.name.toLowerCase()
      });
    }
  } catch (error: any) {
    const errorType = error.name === 'AbortError' ? 'Timeout' : error.message;
    console.log(`${service.name} failed: ${errorType}`);
  }

  // If geocoding fails, return fallback with coordinates
  console.log('Geocoding service failed, returning coordinates');
  return NextResponse.json(fallbackResponse);
}
