import { ReactNode } from 'react';
import { getCurrentUser } from '../../lib/auth';
import { redirect } from 'next/navigation';
import ClientDashboardLayout from './ClientDashboardLayout';

export const dynamic = 'force-dynamic';

export default async function DashboardLayout({ children }: { children: ReactNode }) {
  const user = await getCurrentUser();
 
  if (!user) {
    redirect('/login');
  }
 
  return (
    <ClientDashboardLayout user={user}>
      {children}
    </ClientDashboardLayout>
  );
}
