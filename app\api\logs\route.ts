import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { handleApiError, handleApiValidationError } from '@/utils/apiErrorHandler';

// GET /api/logs - Mendapatkan semua logs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level');
    const resolved = searchParams.get('resolved');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const skip = (page - 1) * pageSize;

    // Buat filter
    const filter: {
      level?: any;
      resolved?: boolean;
      createdAt?: {
        gte?: Date;
        lte?: Date;
      }
    } = {};
    if (level) filter.level = level;
    if (resolved) filter.resolved = resolved === 'true';
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.gte = new Date(startDate);
      if (endDate) filter.createdAt.lte = new Date(endDate);
    }

    // Ambil logs dengan pagination
    const logs = await prisma.error_log.findMany({
      where: filter,
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: pageSize,
    });

    // Ambil total jumlah logs untuk pagination
    const totalLogs = await prisma.error_log.count({
      where: filter,
    });

    return NextResponse.json({
      logs,
      pagination: {
        currentPage: page,
        pageSize,
        totalPages: Math.ceil(totalLogs / pageSize),
        totalItems: totalLogs,
      },
    });
  } catch (error) {
    return handleApiError(error, '/api/logs', 'GET');
  }
}

// POST /api/logs - Mencatat log baru
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { level, message, stack, path, method, userId, userAgent } = body;

    // Validasi data yang diperlukan
    if (!level || !message) {
      const validationError = {
        error: 'Level dan message diperlukan',
        validationErrors: [
          ...(level ? [] : [{ path: 'level', message: 'Level diperlukan' }]),
          ...(message ? [] : [{ path: 'message', message: 'Message diperlukan' }]),
        ],
      };
      return handleApiValidationError(validationError);
    }

    // Dapatkan IP address
    const forwardedFor = request.headers.get('x-forwarded-for');
    const ip = forwardedFor ? forwardedFor.split(',')[0] : null;

    // Buat log baru
    const log = await prisma.error_log.create({
      data: {
        level,
        message,
        stack,
        path,
        method,
        userId,
        userAgent,
        ip,
      },
    });

    return NextResponse.json(log, { status: 201 });
  } catch (error) {
    return handleApiError(error, '/api/logs', 'POST');
  }
}