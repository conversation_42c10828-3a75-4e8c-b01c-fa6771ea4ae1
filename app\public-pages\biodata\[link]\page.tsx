'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import InputField from '../../../../components/InputField'
import Button from '../../../../components/Button';
import SignatureCanvas from '../../../../components/SignatureCanvas';
import { submitBiodata } from '../actions';
import { formatIndonesiaDate } from '../../../../utils/dateUtils';
import { pelatihan_jenjang } from '@prisma/client';
import PhotoUpload from '../../../../components/PhotoUpload';
import Image from 'next/image';

// Interface untuk data formulir biodata (matching the one in actions.ts)
interface BiodataFormData {
  nama: string;
  tempat_lahir: string;
  tanggal_lahir: string;
  pendidikan: string;
  jenis_kelamin: string;
  jenjang: pelatihan_jenjang;
  nip?: string;
  pangkat_golongan?: string;
  jabatan: string;
  unit_kerja: string;
  alamat_unit_kerja: string;
  npwp?: string;
  email: string;
  no_hp: string;  kota: string; // Field kota untuk eksport PDF
  tanda_tangan: string;
  foto_cloudinary_id?: string; // Cloudinary public_id
  foto_url?: string; // Cloudinary secure_url
}

// Default jenjang values as fallback (using the Prisma-generated type)
const DEFAULT_JENJANG_OPTIONS = [
  { id: '1', jenjang: pelatihan_jenjang.PAUD, target_peserta: 0 },
  { id: '2', jenjang: pelatihan_jenjang.SD, target_peserta: 0 },
  { id: '3', jenjang: pelatihan_jenjang.SMP, target_peserta: 0 },
  { id: '4', jenjang: pelatihan_jenjang.SMA, target_peserta: 0 },
  { id: '5', jenjang: pelatihan_jenjang.DINAS, target_peserta: 0 },
  { id: '6', jenjang: pelatihan_jenjang.BPMP, target_peserta: 0 },
  { id: '7', jenjang: pelatihan_jenjang.LAINNYA, target_peserta: 0 }
];

// Toast notification component
function Toast({ message, type, onClose }: { message: string; type: 'success' | 'error'; onClose: () => void }) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, 5000);
    
    return () => clearTimeout(timer);
  }, [onClose]);
  
  return (
    <div 
      className={`fixed bottom-4 right-4 p-4 rounded-md shadow-md text-white ${
        type === 'success' ? 'bg-green-600' : 'bg-red-600'
      }`}
      role="alert"
      aria-live="assertive"
    >
      <div className="flex items-center justify-between">
        <span>{message}</span>
        <button 
          onClick={onClose} 
          className="ml-4 text-white hover:text-gray-200 focus:outline-none"
          aria-label="Tutup notifikasi"
        >
          ×
        </button>
      </div>
    </div>
  );
}

// Section divider component
function FormSection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div className="p-4 mb-6 border border-gray-200 rounded-md bg-gray-50">
      <h3 className="mb-3 text-base font-medium text-gray-800">{title}</h3>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

export default function BiodataPage() {
  const params = useParams<{ link: string }>();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  
  interface JenjangTarget {
    id: string;
    jenjang: pelatihan_jenjang; // Using Prisma-generated type
    target_peserta: number;
  }
  
  interface PelatihanData {
    id: string;
    nama: string;
    tempat: string;
    tgl_mulai: string | Date;
    tgl_berakhir: string | Date;
    link_registrasi?: string;
    jenjangTargets?: JenjangTarget[];
  }
  
  interface ApiResponse {
    valid: boolean;
    message?: string;
    pelatihan?: PelatihanData;
  }
  
  const [pelatihan, setPelatihan] = useState<PelatihanData | null>(null);  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
    // Use null to represent unselected state
  const [formData, setFormData] = useState({
    nama: '',
    tempat_lahir: '',
    tanggal_lahir: '',
    pendidikan: '',
    jenis_kelamin: '',
    jenjang: null as pelatihan_jenjang | null, // Use Prisma-generated type
    nip: '',
    pangkat_golongan: '',
    jabatan: '',
    unit_kerja: '',
    alamat_unit_kerja: '',
    npwp: '',
    email: '',
    no_hp: '',    kota: 'Samarinda', // Default kota
    tanda_tangan: '',
    foto_cloudinary_id: '',
    foto_url: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Track current step in multi-step form
  const [formStep, setFormStep] = useState(1);
  const totalSteps = 3;
  const [isReviewMode, setIsReviewMode] = useState(false);

  useEffect(() => {
    async function verifyLink() {
      try {        const response = await fetch(`/api/verify-registration?link=${params.link}`);
        const data = await response.json() as ApiResponse;

        if (data.valid && data.pelatihan) {
          setPelatihan(data.pelatihan);
          setIsLoading(false);
        } else {
          setError(data.message || 'Invalid registration link');
          setIsLoading(false);
        }      } catch (_error) {
        setError('Terjadi kesalahan saat memverifikasi link');
        setIsLoading(false);
      }
    }

    if (params.link) {
      verifyLink();
    }
  }, [params.link]);

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return pattern.test(email);
  };

  const validatePhoneNumber = (phone: string): boolean => {
    const pattern = /^(\+62|62|0)8[1-9][0-9]{6,10}$/;
    return pattern.test(phone);
  };

  const validateNPWP = (npwp?: string): boolean => {
    if (!npwp || npwp.trim() === '') return true;
    const cleanNPWP = npwp.replace(/[^\d]/g, '');
    return cleanNPWP.length === 15;
  };

  const formatNPWP = (value: string): string => {
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 2) return digits;
    if (digits.length <= 5) return `${digits.slice(0, 2)}.${digits.slice(2)}`;
    if (digits.length <= 8) return `${digits.slice(0, 2)}.${digits.slice(2, 5)}.${digits.slice(5)}`;
    if (digits.length <= 9) return `${digits.slice(0, 2)}.${digits.slice(2, 5)}.${digits.slice(5, 8)}.${digits.slice(8)}`;
    return `${digits.slice(0, 2)}.${digits.slice(2, 5)}.${digits.slice(5, 8)}.${digits.slice(8, 9)}-${digits.slice(9, 12)}.${digits.slice(12, 15)}`;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Special handling for formatted fields
    if (name === 'npwp') {
      const formattedValue = formatNPWP(value);
      setFormData((prev) => ({ ...prev, [name]: formattedValue }));
      
      if (value && !validateNPWP(formattedValue)) {
        setFieldErrors(prev => ({ 
          ...prev, 
          [name]: 'Format NPWP tidak valid' 
        }));
      } else {
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
      return;
    }
    
    // Special handling for email validation
    if (name === 'email' && value) {
      if (!validateEmail(value)) {
        setFieldErrors(prev => ({ 
          ...prev, 
          [name]: 'Format email tidak valid' 
        }));
      } else {
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
    
    // Special handling for phone number validation
    if (name === 'no_hp' && value) {
      if (!validatePhoneNumber(value)) {
        setFieldErrors(prev => ({ 
          ...prev, 
          [name]: 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx' 
        }));
      } else {
        setFieldErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
    
    // Special handling for jenjang to ensure it's treated as an enum value
    if (name === 'jenjang') {
      if (value === '') {
        // Handle empty selection
        setFormData((prev) => ({ ...prev, jenjang: null }));
      } else {
        // Set as enum value when a valid option is selected
        setFormData((prev) => ({ 
          ...prev, 
          jenjang: value as pelatihan_jenjang
        }));
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSignatureChange = (dataUrl: string) => {
    setFormData((prev) => ({ ...prev, tanda_tangan: dataUrl }));
    
    if (dataUrl) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.tanda_tangan;
        return newErrors;
      });
    }
  };  // Handle photo upload to Cloudinary
  const handlePhotoUpload = ({
    public_id,
    secure_url
  }: {
    public_id: string;
    secure_url: string;
    url: string;
  }) => {
    setFormData(prev => ({
      ...prev,
      foto_cloudinary_id: public_id,
      foto_url: secure_url
    }));
    
    // Clear error if photo is provided
    if (public_id && secure_url) {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.foto;
        return newErrors;
      });
    }
  };

  // Validate current step
  const validateStep = (step: number): boolean => {
    const errors: Record<string, string> = {};
    
    if (step === 1) {
      // Personal information validation
      if (!formData.nama) errors.nama = 'Nama lengkap harus diisi';
      if (!formData.tempat_lahir) errors.tempat_lahir = 'Tempat lahir harus diisi';
      if (!formData.tanggal_lahir) errors.tanggal_lahir = 'Tanggal lahir harus diisi';
      if (!formData.jenis_kelamin) errors.jenis_kelamin = 'Jenis kelamin harus dipilih';
      if (!formData.pendidikan) errors.pendidikan = 'Pendidikan terakhir harus dipilih';
      if (!formData.jenjang) errors.jenjang = 'Jenjang kegiatan harus dipilih';
    } 
    else if (step === 2) {
      // Work information validation
      if (!formData.jabatan) errors.jabatan = 'Jabatan harus diisi';
      if (!formData.unit_kerja) errors.unit_kerja = 'Unit kerja harus diisi';
      if (!formData.alamat_unit_kerja) errors.alamat_unit_kerja = 'Alamat unit kerja harus diisi';
      
      // Optional field validation
      if (formData.npwp && !validateNPWP(formData.npwp)) {
        errors.npwp = 'Format NPWP tidak valid';
      }
    }    else if (step === 3) {
      // Contact information validation
      if (!formData.email) {
        errors.email = 'Email harus diisi';
      } else if (!validateEmail(formData.email)) {
        errors.email = 'Format email tidak valid';
      }
      
      if (!formData.no_hp) {
        errors.no_hp = 'Nomor HP harus diisi';
      } else if (!validatePhoneNumber(formData.no_hp)) {
        errors.no_hp = 'Format nomor HP tidak valid';
      }
      
      if (!formData.kota) {
        errors.kota = 'Kota harus diisi';
      }
      
      if (!formData.tanda_tangan) {
        errors.tanda_tangan = 'Tanda tangan harus diisi';
      }
    }
    
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Navigate to next step
  const goToNextStep = () => {
    if (validateStep(formStep) && formStep < totalSteps) {
      setFormStep(formStep + 1);
      window.scrollTo(0, 0);
    } else {
      setToast({
        message: 'Mohon lengkapi data yang diperlukan',
        type: 'error'
      });
    }
  };

  // Navigate to previous step
  const goToPrevStep = () => {
    if (formStep > 1) {
      setFormStep(formStep - 1);
      window.scrollTo(0, 0);
    }
  };  const handleShowReview = (e: React.FormEvent) => {
    e.preventDefault();
    setIsReviewMode(true);
  };

  // Fungsi untuk kembali ke mode edit
  const handleBackToEdit = () => {
    setIsReviewMode(false);
  };

  // Fungsi submit ke backend (hanya dari mode review)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setFieldErrors({});
    setToast(null);
    try {
      // Import timezone conversion function
      const { convertFormDateToIndonesia } = await import('@/utils/dateUtils');
        // Create a new object with all required fields and proper types
      // Convert tanggal_lahir to Indonesia timezone before submission
      const submissionData: BiodataFormData = {
        nama: formData.nama,        tempat_lahir: formData.tempat_lahir,
        tanggal_lahir: convertFormDateToIndonesia(formData.tanggal_lahir).toISOString(),
        pendidikan: formData.pendidikan,
        jenis_kelamin: formData.jenis_kelamin,
        jenjang: formData.jenjang as pelatihan_jenjang, // Asserted as non-null since we checked above
        nip: formData.nip,
        pangkat_golongan: formData.pangkat_golongan,
        jabatan: formData.jabatan,
        unit_kerja: formData.unit_kerja,
        alamat_unit_kerja: formData.alamat_unit_kerja,
        npwp: formData.npwp,
        email: formData.email,
        no_hp: formData.no_hp,
        kota: formData.kota,        tanda_tangan: formData.tanda_tangan,
        foto_cloudinary_id: formData.foto_cloudinary_id,
        foto_url: formData.foto_url
      };      
      const result = await submitBiodata(params.link as string, submissionData);
      
      if (result.success) {
        setToast({
          message: 'Biodata berhasil disimpan',
          type: 'success'
        });
        setTimeout(() => {
          router.push(`/public-pages/biodata/${params.link}/success`);
        }, 1500);
      } else {
        setToast({
          message: `Gagal menyimpan biodata: ${result.message}`,
          type: 'error'
        });
        setIsSubmitting(false);
      }    } catch (_err) {
      setToast({
        message: 'Terjadi kesalahan saat mengirim data',
        type: 'error'
      });
      setIsSubmitting(false);
    }
  };
  // Helper function to get jenjang options from jenjangTargets
  const getJenjangOptions = (): JenjangTarget[] => {
    // Check if pelatihan has jenjangTargets
    if (pelatihan?.jenjangTargets && Array.isArray(pelatihan.jenjangTargets) && pelatihan.jenjangTargets.length > 0) {
      return pelatihan.jenjangTargets;
    }
    
    // Fall back to default options if no options found
    return DEFAULT_JENJANG_OPTIONS;
  };

  // Get jenjang options from jenjangTargets table
  const jenjangOptions = getJenjangOptions();

  // Progress indicator component
  const ProgressIndicator = () => (
    <div className="flex mb-6 space-x-1 md:space-x-2">
      {Array.from({ length: totalSteps }).map((_, index) => (
        <div 
          key={index} 
          className={`flex-1 h-2 rounded ${
            index + 1 === formStep ? 'bg-blue-600' : 
            index + 1 < formStep ? 'bg-green-500' : 'bg-gray-300'
          }`}
          aria-hidden="true"
        />
      ))}
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen px-4 bg-gray-100">
        <div className="w-full max-w-md p-4 text-center bg-white rounded-lg shadow-md sm:p-8">
          <div className="w-10 h-10 mx-auto border-b-2 border-blue-600 rounded-full animate-spin sm:h-12 sm:w-12"></div>
          <p className="mt-3 text-sm sm:mt-4 sm:text-base">Memuat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen px-4 bg-gray-100">
        <div className="w-full max-w-md p-4 text-center bg-white rounded-lg shadow-md sm:p-8">
          <h1 className="mb-3 text-xl font-bold text-red-600 sm:text-2xl sm:mb-4">Error</h1>
          <p className="mb-2 text-sm sm:mb-4 sm:text-base">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen px-4 py-4 bg-gray-100 sm:py-6 md:py-8 sm:px-6">
      <div className="w-full max-w-2xl mx-auto overflow-hidden bg-white rounded-lg shadow-md">
        <div className="px-4 py-3 text-white bg-blue-600 sm:px-6 sm:py-4">
          <h1 className="text-lg font-bold sm:text-xl">Form Pendaftaran Kegiatan</h1>
          {pelatihan && <p className="mt-1 text-sm truncate sm:text-base">{pelatihan.nama}</p>}
        </div>

        <div className="p-4 sm:p-6">
          {pelatihan && (
            <div className="p-3 mb-4 border border-gray-200 rounded-md sm:mb-6 sm:p-4 bg-gray-50">
              <h2 className="mb-2 text-base font-medium text-gray-800 sm:text-lg">Informasi Kegiatan</h2>
              <div className="space-y-1 text-sm sm:space-y-2 sm:text-base">                <p><span className="font-medium">Nama Kegiatan:</span> {pelatihan.nama}</p>
                <p><span className="font-medium">Tempat:</span> {pelatihan.tempat}</p>
                <p><span className="font-medium">Tanggal:</span> {formatIndonesiaDate(pelatihan.tgl_mulai)} - {formatIndonesiaDate(pelatihan.tgl_berakhir)}</p>
              </div>
            </div>
          )}

          <ProgressIndicator />
          
          <div className="mb-4 text-center">
            <span className="text-sm font-medium text-gray-500">
              Langkah {formStep} dari {totalSteps}: 
              {formStep === 1 && " Informasi Pribadi"}
              {formStep === 2 && " Informasi Pekerjaan"}
              {formStep === 3 && " Kontak & Tanda Tangan"}
            </span>
          </div>

          {isReviewMode ? (
            <div className="max-w-2xl mx-auto">
              <h2 className="mb-4 text-lg font-semibold text-center">Review Data Anda</h2>
              <div className="space-y-4">
                <div className="p-4 border rounded bg-gray-50">
                  <div className="mb-2"><b>Nama:</b> {formData.nama}</div>
                  <div className="mb-2"><b>Tempat, Tanggal Lahir:</b> {formData.tempat_lahir}, {formData.tanggal_lahir}</div>
                  <div className="mb-2"><b>Pendidikan:</b> {formData.pendidikan}</div>
                  <div className="mb-2"><b>Jenis Kelamin:</b> {formData.jenis_kelamin}</div>
                  <div className="mb-2"><b>Jenjang:</b> {formData.jenjang}</div>
                  <div className="mb-2"><b>NIP:</b> {formData.nip || '-'}</div>
                  <div className="mb-2"><b>Pangkat/Golongan:</b> {formData.pangkat_golongan || '-'}</div>
                  <div className="mb-2"><b>Jabatan:</b> {formData.jabatan}</div>
                  <div className="mb-2"><b>Unit Kerja:</b> {formData.unit_kerja}</div>                  <div className="mb-2"><b>Alamat Unit Kerja:</b> {formData.alamat_unit_kerja}</div>
                  <div className="mb-2"><b>NPWP:</b> {formData.npwp || '-'}</div>
                  <div className="mb-2"><b>Email:</b> {formData.email}</div>
                  <div className="mb-2"><b>Nomor HP:</b> {formData.no_hp}</div>
                  <div className="mb-2"><b>Kota:</b> {formData.kota}</div>                  <div className="mb-2"><b>Foto:</b><br/>{formData.foto_url && (
                    <Image src={formData.foto_url} alt="Foto" width={128} height={128} className="object-cover w-32 h-32 mt-2 rounded" />
                  )}</div>
                  <div className="mb-2"><b>Tanda Tangan:</b><br/>{formData.tanda_tangan && (
                    <Image src={formData.tanda_tangan} alt="Tanda Tangan" width={200} height={80} className="object-contain w-40 h-20 mt-2 bg-white border" />
                  )}</div>
                </div>
              </div>
              <div className="flex justify-between mt-6">
                <Button
                  type="button"
                  onClick={handleBackToEdit}
                  className="px-4 py-2 text-sm text-white bg-green-600 hover:bg-green-700 sm:text-base"
                >
                  Edit Data
                </Button>
                <Button
                  type="button"
                  onClick={handleSubmit}
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm sm:text-base"
                >
                  Simpan Biodata
                </Button>
              </div>
            </div>
          ) : (
            <form onSubmit={formStep < totalSteps ? handleShowReview : handleShowReview}>
              {/* Step 1: Personal Information */}
              {formStep === 1 && (
                <FormSection title="Informasi Pribadi">
                  <div>
                    <InputField
                      label="Nama Lengkap"
                      name="nama"
                      value={formData.nama}
                      onChange={handleChange}
                      required
                      error={fieldErrors.nama}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <InputField
                        label="Tempat Lahir"
                        name="tempat_lahir"
                        value={formData.tempat_lahir}
                        onChange={handleChange}
                        required
                        error={fieldErrors.tempat_lahir}
                      />
                    </div>

                    <div>
                      <InputField
                        label="Tanggal Lahir"
                        name="tanggal_lahir"
                        type="date"
                        value={formData.tanggal_lahir}
                        onChange={handleChange}
                        required
                        error={fieldErrors.tanggal_lahir}
                      />
                    </div>
                  </div>

                  <div className="mb-1 sm:mb-2">
                    <label className="block mb-1 text-xs font-medium text-gray-700 sm:text-sm">
                      Jenis Kelamin <span className="text-red-500">*</span>
                    </label>
                    <div className="flex flex-wrap space-x-4 sm:flex-nowrap">
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="jenis_kelamin"
                          value="Laki-laki"
                          checked={formData.jenis_kelamin === 'Laki-laki'}
                          onChange={handleChange}
                          className="w-4 h-4 text-blue-600 border-gray-300"
                          required
                        />
                        <span className="ml-2 text-sm">Laki-laki</span>
                      </label>
                      <label className="inline-flex items-center">
                        <input
                          type="radio"
                          name="jenis_kelamin"
                          value="Perempuan"
                          checked={formData.jenis_kelamin === 'Perempuan'}
                          onChange={handleChange}
                          className="w-4 h-4 text-blue-600 border-gray-300"
                        />
                        <span className="ml-2 text-sm">Perempuan</span>
                      </label>
                    </div>
                    {fieldErrors.jenis_kelamin && (
                      <p className="mt-1 text-sm text-red-600">{fieldErrors.jenis_kelamin}</p>
                    )}
                  </div>

                  <div className="mb-1 sm:mb-2">
                    <label className="block mb-1 text-xs font-medium text-gray-700 sm:text-sm">
                      Pendidikan Terakhir <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="pendidikan"
                      value={formData.pendidikan}
                      onChange={handleChange}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                      aria-describedby={fieldErrors.pendidikan ? "pendidikan-error" : undefined}
                    >
                      <option value="">Pilih Pendidikan</option>
                      <option value="SD">SD</option>
                      <option value="SMP">SMP</option>
                      <option value="SMA/SMK">SMA/SMK</option>
                      <option value="D3">D3</option>
                      <option value="S1">S1</option>
                      <option value="S2">S2</option>
                      <option value="S3">S3</option>
                    </select>
                    {fieldErrors.pendidikan && (
                      <p id="pendidikan-error" className="mt-1 text-sm text-red-600">{fieldErrors.pendidikan}</p>
                    )}
                  </div>

                  <div className="mb-1 sm:mb-2">
                    <label className="block mb-1 text-xs font-medium text-gray-700 sm:text-sm">
                      Jenjang Kegiatan Yang Diikuti <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="jenjang"
                      value={formData.jenjang || ''}
                      onChange={handleChange}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                      aria-describedby={fieldErrors.jenjang ? "jenjang-error" : undefined}
                    >
                      <option value="">Pilih Jenjang</option>
                      {jenjangOptions.map((option) => (
                        <option key={option.id} value={option.jenjang}>
                          {option.jenjang}
                        </option>
                      ))}
                    </select>
                    {fieldErrors.jenjang && (
                      <p id="jenjang-error" className="mt-1 text-sm text-red-600">{fieldErrors.jenjang}</p>
                    )}
                  </div>

                  <div className="mb-4">
                    <label className="block mb-1 text-xs font-medium text-gray-700 sm:text-sm">
                      Foto <span className="text-gray-500">(Opsional)</span>
                    </label>                    <PhotoUpload 
                      onPhotoUploaded={handlePhotoUpload}
                      currentPhotoUrl={formData.foto_url}
                      folder="biodata"
                      maxSizeMB={5}
                      label="Upload Foto"
                    />
                    {fieldErrors.foto && (
                      <p className="mt-1 text-sm text-red-600">{fieldErrors.foto}</p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Upload foto dengan ukuran maksimal 5 MB. Format yang diterima: JPG, PNG, GIF.
                    </p>
                  </div>
                </FormSection>
              )}

              {/* Step 2: Work Information */}
              {formStep === 2 && (
                <FormSection title="Informasi Pekerjaan">
                  <InputField
                    label="NIP"
                    name="nip"
                    value={formData.nip}
                    onChange={handleChange}
                    error={fieldErrors.nip}
                  />

                  <InputField
                    label="Pangkat/Golongan"
                    name="pangkat_golongan"
                    value={formData.pangkat_golongan}
                    onChange={handleChange}
                    error={fieldErrors.pangkat_golongan}
                  />

                  <InputField
                    label="Jabatan"
                    name="jabatan"
                    value={formData.jabatan}
                    onChange={handleChange}
                    required
                    error={fieldErrors.jabatan}
                  />

                  <InputField
                    label="Unit Kerja"
                    name="unit_kerja"
                    value={formData.unit_kerja}
                    onChange={handleChange}
                    required
                    error={fieldErrors.unit_kerja}
                  />

                  <InputField
                    label="Alamat Unit Kerja"
                    name="alamat_unit_kerja"
                    value={formData.alamat_unit_kerja}
                    onChange={handleChange}
                    required
                    error={fieldErrors.alamat_unit_kerja}
                  />

                  <InputField
                    label="NPWP"
                    name="npwp"
                    value={formData.npwp}
                    onChange={handleChange}
                    placeholder="xx.xxx.xxx.x-xxx.xxx"
                    error={fieldErrors.npwp}
                  />
                </FormSection>
              )}

              {/* Step 3: Contact & Signature */}
              {formStep === 3 && (
                <FormSection title="Informasi Kontak & Tanda Tangan">
                  <InputField
                    label="Email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    error={fieldErrors.email}
                  />                  <InputField
                    label="Nomor HP"
                    name="no_hp"
                    type="tel"
                    value={formData.no_hp}
                    onChange={handleChange}
                    required
                    placeholder="08xxxxxxxxxx"
                    error={fieldErrors.no_hp}
                  />

                  <InputField
                    label="Kota"
                    name="kota"
                    value={formData.kota}
                    onChange={handleChange}
                    required
                    placeholder="Masukkan nama kota"
                    error={fieldErrors.kota}
                  />

                  <div className="pt-1 sm:pt-2">
                    
                    <div className="w-full">
                      <SignatureCanvas
                        onSignatureChange={handleSignatureChange}
                      />
                    </div>
                    {fieldErrors.tanda_tangan && (
                      <p className="mt-1 text-sm text-red-600">{fieldErrors.tanda_tangan}</p>
                    )}
                  </div>
                </FormSection>
              )}

              <div className="flex justify-between mt-6">
                {formStep > 1 ? (
                  <Button
                    type="button"
                    onClick={goToPrevStep}
                    className="px-4 py-2 text-sm text-white bg-green-600 hover:bg-green-700 sm:text-base"
                  >
                    Kembali
                  </Button>
                ) : (
                  <div></div> // Empty div to maintain spacing with flex-between
                )}
                
                {formStep < totalSteps ? (
                  <Button
                    type="button"
                    onClick={goToNextStep}
                    className="px-4 py-2 text-sm sm:text-base"
                  >
                    Lanjut
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="px-4 py-2 text-sm sm:text-base"
                  >
                    Review Data
                  </Button>
                )}
              </div>
            </form>
          )}
        </div>
      </div>

      {toast && (
        <Toast 
          message={toast.message} 
          type={toast.type} 
          onClose={() => setToast(null)} 
        />
      )}
    </div>
  );
}