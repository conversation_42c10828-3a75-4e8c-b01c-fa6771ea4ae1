# ⚡ MANUAL COMMANDS untuk VPS
# Copy dan paste commands ini di SSH terminal

# ========================================
#   EMERGENCY FIX PHOTO 404 - MANUAL
# ========================================

# STEP 1: Auto-find aplikasi Next.js
echo "🔍 Mencari aplikasi Next.js..."
find /home /var/www /opt /usr/local -name "server.js" -path "*/standalone/*" 2>/dev/null
find /home /var/www /opt -name "package.json" -exec grep -l "next" {} \; 2>/dev/null

# STEP 2: Set APP_DIR (ganti dengan path yang ditemukan di STEP 1)
# Contoh: APP_DIR="/var/www/html" atau path yang ditemukan
APP_DIR="/var/www/html"  # UPDATE INI!

echo "📁 Menggunakan app directory: $APP_DIR"

# STEP 3: Buat struktur folder uploads
echo "📁 Membuat folder uploads..."
sudo mkdir -p $APP_DIR/public/uploads/absensi/photos
sudo chown -R www-data:www-data $APP_DIR/public/uploads
sudo chmod -R 755 $APP_DIR/public/uploads
echo "✅ Folder uploads dibuat"

# STEP 4: Cek dan perbaiki duplikasi folder public/public
if [ -d "$APP_DIR/public/public" ]; then
    echo "⚠️ Duplikasi folder ditemukan, memperbaiki..."
    sudo cp -r $APP_DIR/public/public/uploads/* $APP_DIR/public/uploads/ 2>/dev/null || true
    sudo rm -rf $APP_DIR/public/public
    echo "✅ Duplikasi folder diperbaiki"
fi

# STEP 5: Update nginx config
echo "🔧 Mengupdate nginx config..."

# Backup config dulu
sudo cp /etc/nginx/sites-available/kegiatan.bpmpkaltim.id /etc/nginx/sites-available/kegiatan.bpmpkaltim.id.backup.$(date +%Y%m%d-%H%M%S) 2>/dev/null || \
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d-%H%M%S)

# Tambahkan konfigurasi uploads
NGINX_CONFIG="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
if [ ! -f "$NGINX_CONFIG" ]; then
    NGINX_CONFIG="/etc/nginx/sites-available/default"
fi

# Cek apakah sudah ada konfigurasi uploads
if ! sudo grep -q "location /uploads/" "$NGINX_CONFIG"; then
    echo "📝 Menambahkan konfigurasi uploads..."
    
    # Metode 1: Tambahkan sebelum location /
    sudo sed -i '/location \/ {/i\
    # Static files untuk uploads\
    location /uploads/ {\
        root '"$APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        try_files $uri $uri/ =404;\
    }\
' "$NGINX_CONFIG"
    
    echo "✅ Nginx config diupdate"
else
    echo "ℹ️ Nginx config sudah ada"
fi

# STEP 6: Test dan reload nginx
echo "🔄 Testing dan reload nginx..."
sudo nginx -t
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "✅ Nginx direload"
else
    echo "❌ Nginx config error - cek syntax"
fi

# STEP 7: Test akses
echo "🧪 Testing akses..."
curl -I https://kegiatan.bpmpkaltim.id/uploads/
echo ""

# STEP 8: Show status
echo ""
echo "📊 Status folder:"
ls -la $APP_DIR/public/uploads/absensi/photos/ 2>/dev/null || echo "Folder belum ada"

echo ""
echo "🎉 EMERGENCY FIX SELESAI!"
echo ""
echo "🔍 LANGKAH SELANJUTNYA:"
echo "1. Test upload foto baru di admin panel"
echo "2. Cek apakah foto muncul tanpa 404"
echo "3. Monitor: tail -f /var/log/nginx/error.log"
echo ""

# ========================================
#   QUICK VERIFICATION
# ========================================

echo "✅ VERIFICATION COMMANDS:"
echo "ls -la $APP_DIR/public/uploads/"
echo "curl -I https://kegiatan.bpmpkaltim.id/uploads/"
echo "sudo nginx -t"
echo "systemctl status nginx"
