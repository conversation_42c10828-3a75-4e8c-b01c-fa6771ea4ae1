@echo off
setlocal enabledelayedexpansion

:: Master Auto-Refresh Fix Controller
:: Interface utama untuk semua operasi auto-refresh fix

title Master Auto-Refresh Fix Controller - CloudPanel Ubuntu 24.04

:main_menu
cls
echo.
echo ================================================
echo    MASTER AUTO-REFRESH FIX CONTROLLER
echo    CloudPanel Ubuntu 24.04 Solution
echo ================================================
echo.
echo [36mVPS Target: 185.243.56.27[0m
echo [36mDomain: kegiatan.bpmpkaltim.id[0m
echo.
echo [33mSelect Operation:[0m
echo.
echo [32m1.[0m Deploy Advanced Auto-Refresh Fix
echo [32m2.[0m Monitor Real-time Status
echo [32m3.[0m Run Troubleshooter
echo [32m4.[0m Monitor Continuous (Auto-refresh)
echo [32m5.[0m Quick SSH Connect
echo [32m6.[0m View System Logs
echo [32m7.[0m Emergency Rollback
echo [32m8.[0m Help & Documentation
echo [32m9.[0m Exit
echo.
set /p choice="Enter choice (1-9): "

if "%choice%"=="1" goto :deploy_fix
if "%choice%"=="2" goto :monitor_status
if "%choice%"=="3" goto :run_troubleshooter
if "%choice%"=="4" goto :monitor_continuous
if "%choice%"=="5" goto :ssh_connect
if "%choice%"=="6" goto :view_logs
if "%choice%"=="7" goto :emergency_rollback
if "%choice%"=="8" goto :show_help
if "%choice%"=="9" goto :exit_program

echo [31m[ERROR][0m Invalid choice. Please try again.
pause
goto :main_menu

:deploy_fix
cls
echo.
echo [35m[ACTION][0m Deploying Advanced Auto-Refresh Fix...
echo.
call "%~dp0deploy-advanced-autorefresh.bat"
echo.
echo [36m[INFO][0m Press any key to return to main menu...
pause >nul
goto :main_menu

:monitor_status
cls
echo.
echo [35m[ACTION][0m Running Status Monitor...
echo.
call "%~dp0monitor-autorefresh.bat" single
echo.
echo [36m[INFO][0m Press any key to return to main menu...
pause >nul
goto :main_menu

:run_troubleshooter
cls
echo.
echo [35m[ACTION][0m Running Troubleshooter...
echo.
call "%~dp0troubleshoot-autorefresh.bat"
echo.
echo [36m[INFO][0m Press any key to return to main menu...
pause >nul
goto :main_menu

:monitor_continuous
cls
echo.
echo [35m[ACTION][0m Starting Continuous Monitoring...
echo [33m[WARNING][0m Press Ctrl+C to stop and return to menu
echo.
call "%~dp0monitor-autorefresh.bat" continuous
echo.
echo [36m[INFO][0m Press any key to return to main menu...
pause >nul
goto :main_menu

:ssh_connect
cls
echo.
echo [35m[ACTION][0m Connecting to SSH...
echo.
ssh root@185.243.56.27
echo.
echo [36m[INFO][0m SSH session ended. Press any key to continue...
pause >nul
goto :main_menu

:view_logs
cls
echo.
echo ================================================
echo    SYSTEM LOGS VIEWER
echo ================================================
echo.
echo [33mSelect Log Type:[0m
echo.
echo [32m1.[0m Advanced Photo Monitor Logs
echo [32m2.[0m Nginx Photo Access Logs
echo [32m3.[0m Nginx Error Logs
echo [32m4.[0m System Service Logs
echo [32m5.[0m All Logs (Combined)
echo [32m6.[0m Back to Main Menu
echo.
set /p log_choice="Enter choice (1-6): "

if "%log_choice%"=="1" (
    echo.
    echo [35m[ACTION][0m Viewing Photo Monitor Logs...
    ssh root@185.243.56.27 "tail -f /var/log/advanced-photo-monitor.log"
) else if "%log_choice%"=="2" (
    echo.
    echo [35m[ACTION][0m Viewing Nginx Photo Access Logs...
    ssh root@185.243.56.27 "tail -f /var/log/nginx/photo_access.log"
) else if "%log_choice%"=="3" (
    echo.
    echo [35m[ACTION][0m Viewing Nginx Error Logs...
    ssh root@185.243.56.27 "tail -f /var/log/nginx/kegiatan_error.log"
) else if "%log_choice%"=="4" (
    echo.
    echo [35m[ACTION][0m Viewing Service Logs...
    ssh root@185.243.56.27 "journalctl -u advanced-photo-monitor -f"
) else if "%log_choice%"=="5" (
    echo.
    echo [35m[ACTION][0m Viewing Combined Logs...
    ssh root@185.243.56.27 "multitail /var/log/advanced-photo-monitor.log /var/log/nginx/photo_access.log"
) else if "%log_choice%"=="6" (
    goto :main_menu
) else (
    echo [31m[ERROR][0m Invalid choice
    pause
)
goto :view_logs

:emergency_rollback
cls
echo.
echo [31m================================================[0m
echo [31m    EMERGENCY ROLLBACK[0m
echo [31m================================================[0m
echo.
echo [33m[WARNING][0m This will rollback all auto-refresh changes!
echo [33m[WARNING][0m Only use if auto-refresh fix is causing issues.
echo.
echo [31mRollback Options:[0m
echo.
echo [32m1.[0m Stop Auto-Refresh Services Only
echo [32m2.[0m Restore Nginx Configuration
echo [32m3.[0m Complete Rollback (All Changes)
echo [32m4.[0m Cancel - Back to Main Menu
echo.
set /p rollback_choice="Enter choice (1-4): "

if "%rollback_choice%"=="1" (
    echo.
    echo [35m[ACTION][0m Stopping auto-refresh services...
    ssh root@185.243.56.27 "systemctl stop advanced-photo-monitor.service && systemctl disable advanced-photo-monitor.service"
    echo [32m[SUCCESS][0m Services stopped
) else if "%rollback_choice%"=="2" (
    echo.
    echo [35m[ACTION][0m Restoring nginx configuration...
    ssh root@185.243.56.27 "cp /tmp/advanced-autorefresh-backup-*/nginx.conf /etc/nginx/nginx.conf && systemctl restart nginx"
    echo [32m[SUCCESS][0m Nginx configuration restored
) else if "%rollback_choice%"=="3" (
    echo.
    echo [31m[WARNING][0m Are you sure? This will undo ALL changes! (y/N)
    set /p confirm="Confirm: "
    if /i "!confirm!"=="y" (
        echo.
        echo [35m[ACTION][0m Performing complete rollback...
        ssh root@185.243.56.27 "systemctl stop advanced-photo-monitor.service; systemctl disable advanced-photo-monitor.service; cp /tmp/advanced-autorefresh-backup-*/nginx.conf /etc/nginx/nginx.conf; systemctl restart nginx; rm -f /usr/local/bin/advanced-photo-monitor.sh /etc/systemd/system/advanced-photo-monitor.service; systemctl daemon-reload"
        echo [32m[SUCCESS][0m Complete rollback performed
    ) else (
        echo [36m[INFO][0m Rollback cancelled
    )
) else if "%rollback_choice%"=="4" (
    goto :main_menu
) else (
    echo [31m[ERROR][0m Invalid choice
    pause
    goto :emergency_rollback
)

echo.
pause
goto :main_menu

:show_help
cls
echo.
echo ================================================
echo    HELP & DOCUMENTATION
echo ================================================
echo.
echo [33m🎯 TENTANG AUTO-REFRESH FIX:[0m
echo   Solution untuk masalah foto upload yang memerlukan
echo   nginx reload manual setiap kali ada foto baru.
echo.
echo [33m🔧 KOMPONEN UTAMA:[0m
echo   • Advanced Nginx Configuration (no-cache untuk uploads)
echo   • Real-time File Monitoring Service
echo   • Automatic Permission Fixing
echo   • System Optimization untuk File Watching
echo.
echo [33m📋 CARA KERJA:[0m
echo   1. Monitor file uploads dengan inotify
echo   2. Auto-fix permissions secara real-time
echo   3. Nginx dikonfigurasi tanpa cache untuk uploads
echo   4. Background service handle semua otomatis
echo.
echo [33m🧪 TESTING:[0m
echo   1. Upload foto di aplikasi absensi
echo   2. Langsung buka detail absensi
echo   3. Foto harus muncul TANPA reload nginx
echo.
echo [33m🔍 MONITORING:[0m
echo   • Option 2: Real-time status check
echo   • Option 4: Continuous monitoring
echo   • Option 6: Log viewing
echo.
echo [33m🚨 TROUBLESHOOTING:[0m
echo   • Option 3: Automated troubleshooter
echo   • Option 7: Emergency rollback
echo   • Option 5: Manual SSH access
echo.
echo [33m📞 SUPPORT:[0m
echo   Jika masih ada masalah setelah troubleshooting:
echo   1. Jalankan troubleshooter lagi (Option 3)
echo   2. Check logs via Option 6
echo   3. Manual SSH access via Option 5
echo.
echo [33m⚠️ KNOWN ISSUES & SOLUTIONS:[0m
echo   • Monitoring service tidak start: Restart via troubleshooter
echo   • 404 errors: Check nginx config dan permissions
echo   • High resource usage: Check inotify limits
echo   • File permissions: Auto-fixed by monitoring service
echo.
echo.
echo [36m[INFO][0m Press any key to return to main menu...
pause >nul
goto :main_menu

:exit_program
cls
echo.
echo [32m================================================[0m
echo [32m   AUTO-REFRESH FIX CONTROLLER EXITING[0m
echo [32m================================================[0m
echo.
echo [36m[INFO][0m Thank you for using Auto-Refresh Fix Controller!
echo.
echo [33mRemember:[0m
echo • Test photo uploads after any changes
echo • Monitor logs if issues persist
echo • Use troubleshooter for automatic diagnosis
echo.
echo [32m🎉 Happy coding![0m
echo.
pause
exit /b 0
