'use client';

import React, { useRef, useState, useEffect } from 'react';

interface SignatureCanvasProps {
  onSignatureChange: (dataUrl: string) => void;
  defaultValue?: string;
  className?: string;
}

export default function SignatureCanvas({
  onSignatureChange,
  defaultValue,
  className = '',
}: SignatureCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const lastPosition = useRef<{ x: number; y: number } | null>(null);
  const [color, setColor] = useState<string>('#000000'); // Tambah state warna

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Menyesuaikan ukuran canvas dengan resolusi yang lebih tinggi untuk kualitas lebih baik
    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();

      // Meningkatkan resolusi canvas dengan faktor 2 untuk gambar lebih tajam
      const dpr = window.devicePixelRatio || 2;
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;

      // Sesuaikan ukuran tampilan canvas
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;

      const context = canvas.getContext('2d');
      if (!context) return;

      // Skalakan konteks sesuai DPR untuk resolusi yang lebih tinggi
      context.scale(dpr, dpr);

      // Set canvas properties untuk kualitas garis yang lebih baik
      context.lineWidth = 2.5; // Sedikit lebih kecil di perangkat mobile
      context.lineCap = 'round';
      context.lineJoin = 'round'; // Tambahkan lineJoin untuk sudut yang lebih halus
      context.strokeStyle = color; // Gunakan warna dari state
      context.shadowColor = color;
      context.shadowBlur = 1; // Efek blur minimal untuk garis yang lebih tajam
      setCtx(context);

      // Load default signature if provided
      if (defaultValue) {
        const img = new Image();
        img.onload = () => {
          // Bersihkan canvas terlebih dahulu
          context.clearRect(0, 0, rect.width, rect.height);
          context.drawImage(img, 0, 0, rect.width, rect.height);
        };
        img.src = defaultValue;
      }
    };

    // Resize canvas saat komponen dimuat
    resizeCanvas();

    // Resize canvas saat ukuran window berubah
    window.addEventListener('resize', resizeCanvas);

    // Add non-passive touch event listeners to prevent scrolling
    const handleTouchStart = (e: TouchEvent) => {
      e.preventDefault();
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();
    };

    // Add non-passive event listeners
    canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
    canvas.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      canvas.removeEventListener('touchstart', handleTouchStart);
      canvas.removeEventListener('touchmove', handleTouchMove);
    };
  }, [defaultValue, color]); // Tambah color sebagai dependency

  const getCoordinates = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ): { x: number; y: number } | null => {
    const canvas = canvasRef.current;
    if (!canvas) return null;

    const rect = canvas.getBoundingClientRect();
    
    // Handle touch events
    if ('touches' in e) {
      const touch = e.touches[0];
      return {
        x: touch.clientX - rect.left,
        y: touch.clientY - rect.top
      };
    } 
    // Handle mouse events
    else {
      return {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
    }
  };

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!ctx) return;

    setIsDrawing(true);

    const coords = getCoordinates(e);
    if (!coords) return;

    lastPosition.current = coords;

    ctx.beginPath();
    ctx.moveTo(coords.x, coords.y);
    // Tambahkan dot untuk titik awal
    ctx.arc(coords.x, coords.y, ctx.lineWidth / 2, 0, Math.PI * 2);
    ctx.fill();
    ctx.beginPath();
    ctx.moveTo(coords.x, coords.y);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !ctx || !lastPosition.current) return;

    const coords = getCoordinates(e);
    if (!coords) return;

    // Menggunakan quadraticCurveTo untuk menghasilkan garis yang lebih halus
    ctx.quadraticCurveTo(
      lastPosition.current.x,
      lastPosition.current.y,
      (coords.x + lastPosition.current.x) / 2,
      (coords.y + lastPosition.current.y) / 2
    );

    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo((coords.x + lastPosition.current.x) / 2, (coords.y + lastPosition.current.y) / 2);

    lastPosition.current = coords;
  };

  const endDrawing = () => {
    if (!isDrawing || !ctx) return;
    
    setIsDrawing(false);
    ctx.closePath();
    lastPosition.current = null;
    
    // Get signature as base64 data URL dengan kualitas maksimal
    if (canvasRef.current) {
      // Gunakan maksimum kualitas (1.0) untuk PNG
      const dataUrl = canvasRef.current.toDataURL('image/png', 1.0);
      if (dataUrl) {
        onSignatureChange(dataUrl);
      }
    }
  };

  const clearSignature = () => {
    if (!ctx || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    // Sesuaikan dengan devicePixelRatio saat menghapus
    const dpr = window.devicePixelRatio || 2;
    ctx.clearRect(0, 0, rect.width * dpr, rect.height * dpr);
    onSignatureChange('');
  };

  return (
    <div className={`mb-3 sm:mb-4 ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <span className="text-xs text-gray-500">Warna:</span>
        <button
          type="button"
          className={`w-4 h-4 rounded-full border-2 ${color === '#000000' ? 'border-blue-500' : 'border-gray-300'}`}
          style={{ background: '#000000' }}
          aria-label="Hitam"
          onClick={() => setColor('#000000')}
        />
        <button
          type="button"
          className={`w-4 h-4 rounded-full border-2 ${color === '#1976d2' ? 'border-blue-500' : 'border-gray-300'}`}
          style={{ background: '#1976d2' }}
          aria-label="Biru"
          onClick={() => setColor('#1976d2')}
        />
        <button
          type="button"
          className={`w-4 h-4 rounded-full border-2 ${color === '#388e3c' ? 'border-blue-500' : 'border-gray-300'}`}
          style={{ background: '#388e3c' }}
          aria-label="Hijau"
          onClick={() => setColor('#388e3c')}
        />
      </div>
      <div ref={containerRef} className="p-2 bg-white border border-gray-300 rounded-md">
        <canvas
          ref={canvasRef}
          className="w-full h-32 border border-gray-200 rounded sm:h-40 md:h-48 touch-none"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={endDrawing}
          onMouseLeave={endDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={endDrawing}
          // Add this to prevent touch events from scrolling
          style={{ touchAction: 'none' }}
        />
        <div className="flex items-center justify-between mt-2">
          
          <button
            type="button"
            onClick={clearSignature}
            className="px-2 py-1 ml-auto text-xs text-gray-700 transition-colors duration-200 bg-gray-100 border border-gray-300 rounded-md sm:px-3 sm:text-sm hover:bg-gray-200"
          >
            Hapus
          </button>
        </div>
      </div>
      <p className="mt-1 text-xs text-gray-500 sm:text-sm">
        Gunakan mouse atau jari untuk menandatangani di area di atas
      </p>
    </div>
  );
}