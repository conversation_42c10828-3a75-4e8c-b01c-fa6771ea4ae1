'use server';
import { prisma } from '../../../lib/prisma';
import { pelatihan as <PERSON><PERSON><PERSON><PERSON> } from '@prisma/client';
import { randomUUID } from 'crypto';

// Fungsi untuk membuat waktu Indonesia yang tepat untuk database
function createIndonesiaTime(): Date {
  // Server sudah Asia/Makassar, cukup gunakan waktu lokal server
  return new Date();
}

// Interface untuk data formulir absensi
interface AbsensiFormData {
  nama: string;
  nip_nik?: string;
  jabatan?: string;
  unit_kerja: string;
  no_hp: string;
  tanda_tangan: string;
  latitude?: string;
  longitude?: string;
  alamat?: string;
  pelatihanId?: string;
  is_internal?: boolean;
  foto_path?: string; // Path foto untuk absensi internal (legacy support)
  foto_url?: string;  // URL foto untuk absensi internal
  foto_cloudinary_id?: string; // Cloudinary public_id for new uploads
}

// Interface untuk hasil validasi
interface ValidationResult {
  valid: boolean;
  message?: string;
  pelatihan?: Pick<Pelatihan, 'id' | 'nama' | 'tempat' | 'tgl_mulai' | 'tgl_berakhir'>;
}

// Fungsi untuk validasi nomor telepon (format Indonesia)
function validatePhoneNumber(phone: string): boolean {
  const pattern = /^(\+62|62|0)8[1-9][0-9]{6,10}$/;
  return pattern.test(phone);
}

// Fungsi untuk sanitasi input
function sanitizeInput(input: string): string {
  // Hapus karakter berbahaya dan HTML tags
  return input
    .replace(/<[^>]*>/g, '')
    .trim();
}

export async function submitAbsensi(formData: AbsensiFormData) {
  try {
    // Jika ada pelatihanId langsung, gunakan itu (untuk form internal/eksternal)
    let validation: ValidationResult;
    
    if (formData.pelatihanId) {
      // Untuk form internal/eksternal, cari pelatihan berdasarkan ID
      const pelatihan = await prisma.pelatihan.findUnique({
        where: { id: formData.pelatihanId },
        select: { id: true, nama: true, tempat: true, tgl_mulai: true, tgl_berakhir: true }
      });
      
      if (!pelatihan) {
        return {
          success: false,
          message: 'Pelatihan tidak ditemukan'
        };
      }
      
      validation = { valid: true, pelatihan };
    } else {
      return {
        success: false,
        message: 'Data pelatihan tidak valid'
      };
    }

    // Validasi nomor telepon
    if (!validatePhoneNumber(formData.no_hp)) {
      return {
        success: false,
        message: 'Format nomor HP tidak valid. Gunakan format: 08xxxxxxxxxx'
      };
    }

    // Validasi tanda tangan
    if (!formData.tanda_tangan || formData.tanda_tangan.length < 100) {
      return {
        success: false,
        message: 'Tanda tangan tidak valid'
      };
    }    // Sanitasi input
    const sanitizedData = {
      nama: sanitizeInput(formData.nama),
      nip_nik: formData.nip_nik ? sanitizeInput(formData.nip_nik) : null,
      jabatan: formData.jabatan ? sanitizeInput(formData.jabatan) : null,
      unit_kerja: sanitizeInput(formData.unit_kerja),
      no_hp: sanitizeInput(formData.no_hp),
      tanda_tangan: formData.tanda_tangan,  // Binary data, no sanitization needed
      foto_path: formData.foto_cloudinary_id ? sanitizeInput(formData.foto_cloudinary_id) : 
                 (formData.foto_path ? sanitizeInput(formData.foto_path) : null),
      foto_url: formData.foto_url ? sanitizeInput(formData.foto_url) : null,
      latitude: formData.latitude ? parseFloat(sanitizeInput(formData.latitude)) : null,
      longitude: formData.longitude ? parseFloat(sanitizeInput(formData.longitude)) : null,
      alamat: formData.alamat ? sanitizeInput(formData.alamat) : null
    };

    // Validasi khusus untuk absensi internal - wajib ada foto dan lokasi
    if (formData.is_internal) {
      if (!sanitizedData.foto_path || !sanitizedData.foto_url) {
        return {
          success: false,
          message: 'Foto wajib diambil untuk absensi internal'
        };
      }
      
      if (!sanitizedData.latitude || !sanitizedData.longitude) {
        return {
          success: false,
          message: 'Lokasi GPS wajib diaktifkan untuk absensi internal'
        };
      }
    }

    // Simpan data absensi
    if (!validation.pelatihan) {
      return {
        success: false,
        message: 'Data pelatihan tidak valid'
      };
    }
    
    const absensi = await prisma.absensi.create({
      data: {
        id: randomUUID(),
        pelatihanId: validation.pelatihan.id,
        nama: sanitizedData.nama,
        nip_nik: sanitizedData.nip_nik,
        jabatan: sanitizedData.jabatan,
        unit_kerja: sanitizedData.unit_kerja,
        no_hp: sanitizedData.no_hp,
        tanda_tangan: sanitizedData.tanda_tangan,
        foto_path: sanitizedData.foto_path,
        foto_url: sanitizedData.foto_url,
        latitude: sanitizedData.latitude,
        longitude: sanitizedData.longitude,
        alamat: sanitizedData.alamat,
        waktu: createIndonesiaTime()
      }
    });
    
    return {
      success: true,
      message: `Absensi berhasil disimpan untuk ${formData.is_internal ? 'peserta internal' : 'peserta eksternal'}`,      data: absensi
    };
  } catch (_error) {
    return {
      success: false,
      message: 'Terjadi kesalahan saat menyimpan data absensi'
    };
  }
}