// lib/imagekit.ts
import ImageKit from 'imagekit';

// Initialize ImageKit instance for server-side operations
const imagekit = new ImageKit({
  publicKey: process.env.IMAGEKIT_PUBLIC_KEY!,
  privateKey: process.env.IMAGEKIT_PRIVATE_KEY!,
  urlEndpoint: process.env.IMAGEKIT_URL_ENDPOINT!
});

// Interface untuk hasil upload ImageKit
export interface ImageKitUploadResult {
  fileId: string;
  name: string;
  url: string;
  thumbnail: string;
  height: number;
  width: number;
  size: number;
  filePath: string;
}

// Upload image ke ImageKit
export async function uploadToImageKit(
  file: Buffer | string,
  fileName: string,
  folder: string = 'biodata'
): Promise<ImageKitUploadResult> {
  try {
    const uploadOptions: any = {
      file: file, // Buffer or base64 string
      fileName: fileName,
      folder: folder,
      useUniqueFileName: true,
      responseFields: ['tags', 'customCoordinates', 'isPrivateFile', 'metadata'],
      transformation: {
        pre: 'w-800,h-800,c-at_max,q-85,f-webp', // Auto-optimize
      }
    };

    const result = await imagekit.upload(uploadOptions);    return {
      fileId: result.fileId,
      name: result.name,
      url: result.url,
      thumbnail: result.thumbnailUrl || result.url,
      height: result.height || 800,
      width: result.width || 800,
      size: result.size,
      filePath: result.filePath
    };
  } catch (error) {
    console.error('Error uploading to ImageKit:', error);
    throw new Error('Gagal mengupload foto ke ImageKit');
  }
}

// Delete image dari ImageKit
export async function deleteFromImageKit(fileId: string): Promise<boolean> {
  try {
    await imagekit.deleteFile(fileId);
    return true;
  } catch (error) {
    console.error('Error deleting from ImageKit:', error);
    return false;
  }
}

// Generate authentication parameters untuk client-side upload
export function getImageKitAuthParams() {
  const token = imagekit.getAuthenticationParameters();
  return token;
}

// Generate optimized URL
export function getOptimizedImageKitUrl(
  url: string,
  transformations: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
  } = {}
): string {
  const { width = 400, height = 400, quality = 85, format = 'webp' } = transformations;
  
  if (!url.includes('ik.imagekit.io')) {
    return url; // Return original if not ImageKit URL
  }

  // Add transformation parameters
  const transformationString = `tr:w-${width},h-${height},q-${quality},f-${format}`;
  
  if (url.includes('/tr:')) {
    // Replace existing transformations
    return url.replace(/\/tr:[^\/]+/, `/${transformationString}`);
  } else {
    // Add transformations
    const urlParts = url.split('/');
    const fileIndex = urlParts.findIndex(part => part.includes('.'));
    if (fileIndex > 0) {
      urlParts.splice(fileIndex, 0, transformationString);
    }
    return urlParts.join('/');
  }
}
