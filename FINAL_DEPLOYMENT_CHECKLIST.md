# 🚀 FINAL DEPLOYMENT CHECKLIST - Photo Upload Fix

## ✅ PRE-DEPLOYMENT VERIFICATION (COMPLETED)

### Local Environment Status
- [x] **ESLint Issues Resolved**: Converted `fix-deployment-paths.js` to ES modules (`.mjs`)
- [x] **Build Completed**: Next.js standalone build generated successfully
- [x] **Path Fixes Applied**: Public folder structure corrected in deployment
- [x] **Deployment Archive Created**: `deployment.tar.gz` (34.9 MB) ready
- [x] **Scripts Updated**: Added `npm run fix-deployment` command

### Files Ready for VPS
- [x] `.next/deployment.tar.gz` - Main deployment package (34.9 MB)
- [x] `fix-photo-upload-vps.sh` - Automated VPS configuration script
- [x] `nginx-config.txt` - Nginx configuration template
- [x] `deploy-to-vps.bat` - Windows deployment helper
- [x] Multiple documentation guides

## 🎯 VPS DEPLOYMENT STEPS

### Step 1: Upload Files (5 minutes)
```bash
# Upload main deployment archive
scp .next/deployment.tar.gz <EMAIL>:/home/<USER>/

# Upload fix script
scp fix-photo-upload-vps.sh <EMAIL>:/home/<USER>/
```

### Step 2: Extract & Setup (5 minutes)
```bash
# SSH into VPS
ssh <EMAIL>

# Create deployment directory
mkdir -p /home/<USER>/deployment
cd /home/<USER>/deployment

# Extract files
tar -xzf ../deployment.tar.gz

# Verify extraction
ls -la  # Should see: server.js, public/, package.json, etc.
```

### Step 3: Configure & Run Fix Script (10 minutes)
```bash
# Make script executable
chmod +x /home/<USER>/fix-photo-upload-vps.sh

# Edit script to update deployment path
nano /home/<USER>/fix-photo-upload-vps.sh
# Change: DEPLOYMENT_PATH="/home/<USER>/deployment"

# Run the automated fix
cd /home/<USER>/deployment
sudo /home/<USER>/fix-photo-upload-vps.sh
```

### Step 4: Verification Tests (10 minutes)
1. **File Structure Check**:
   ```bash
   ls -la public/uploads/absensi/photos/
   ls -la public/uploads/biodata/photos/
   ```

2. **Permissions Check**:
   ```bash
   ls -la public/uploads/  # Should show www-data:www-data
   ```

3. **Nginx Configuration**:
   ```bash
   sudo nginx -t  # Should show "test is successful"
   sudo systemctl status nginx
   ```

4. **Application Status**:
   ```bash
   pm2 status
   pm2 logs
   ```

## 🧪 FUNCTIONAL TESTING

### Test 1: Photo Upload
1. Navigate to: `https://kegiatan.bpmpkaltim.id/public-pages/absensi/internal/[link]`
2. Fill out attendance form
3. Take/upload photo
4. Submit form
5. **Expected**: Success message, no upload errors

### Test 2: Photo Display
1. Access admin panel → Absensi list
2. Click "Detail" on recent attendance
3. **Expected**: Photo displays correctly, no 404 errors

### Test 3: Direct URL Access
```bash
# Test direct file access (replace with actual file)
curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/[pelatihan-id]/[photo-file]
# Expected: HTTP/1.1 200 OK
```

## 🔧 TECHNICAL SOLUTION SUMMARY

### Problem Solved
- **Before**: Files saved to `/public/public/uploads/` (duplicate public)
- **After**: Files saved to `/public/uploads/` (correct structure)
- **URL Access**: `https://kegiatan.bpmpkaltim.id/uploads/` (working)

### Key Changes Applied
1. **Public Folder Fix**: Corrected standalone build structure
2. **Nginx Configuration**: Root set to `{deployment_path}/public`
3. **Upload Permissions**: `www-data:www-data` ownership
4. **ES Module Conversion**: Fixed ESLint `require()` warnings

### Upload API Flow (No Changes Needed)
- **API Route**: `/api/absensi/upload-photo/route.ts`
- **Save Path**: `public/uploads/absensi/photos/{pelatihanId}/`
- **Return URL**: `/uploads/absensi/photos/{pelatihanId}/[filename]`
- **Nginx Serving**: Direct static file serving from `/uploads/`

## 🚨 TROUBLESHOOTING GUIDE

### If Photos Still Don't Display

1. **Check Nginx Logs**:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

2. **Verify File Creation**:
   ```bash
   find public/uploads -name "*.jpg" -ls | tail -5
   ```

3. **Test Direct Access**:
   ```bash
   curl -v https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/test.jpg
   ```

4. **Check Permissions**:
   ```bash
   ls -la public/uploads/absensi/photos/
   ```

### Common Issues & Solutions

| Issue | Diagnostic | Solution |
|-------|------------|----------|
| 404 on images | `curl -I [image-url]` returns 404 | Check nginx root path |
| Upload fails | Check application logs | Verify directory permissions |
| Permission denied | `ls -la uploads/` | Run: `sudo chown -R www-data:www-data public/uploads/` |
| Nginx config error | `sudo nginx -t` fails | Restore backup config |

## 📊 SUCCESS CRITERIA

The deployment is successful when:
- [ ] Photo upload completes without errors
- [ ] Uploaded photos display in detail views
- [ ] No 404 errors in browser console
- [ ] Direct image URLs return HTTP 200
- [ ] New uploads save to correct directory structure

## 🎯 POST-DEPLOYMENT MONITORING

### Week 1: Monitor for Issues
- Check photo upload success rates
- Monitor disk space usage in `/uploads/`
- Verify no new 404 errors in logs

### Ongoing Maintenance
- Regular backup of `/uploads/` directory
- Monitor file permissions remain correct
- Clean up old uploaded files if needed

---

**STATUS**: ✅ Ready for Production Deployment  
**RISK LEVEL**: Low (includes rollback procedures)  
**ESTIMATED TIME**: 30 minutes total  
**LAST UPDATED**: June 7, 2025
