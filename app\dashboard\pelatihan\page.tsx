import Link from 'next/link';
import { redirect } from 'next/navigation';
import { getCurrentUser } from '../../../lib/auth';
import { prisma } from '../../../lib/prisma';
import PelatihanCard from './components/PelatihanCard';
import PelatihanTable from './components/PelatihanTable';
import { getUserAccessScope } from '../../../utils/helpers';
import Pagination from '../../../components/Pagination';

// Define type for searchParams
interface SearchParams {
  page?: string;
  limit?: string;
  [key: string]: string | undefined;
}

export default async function PelatihanPage(
  props: {
    searchParams: Promise<SearchParams>
  }
) {
  const searchParams = await props.searchParams;
  const user = await getCurrentUser();
  const params = await searchParams;

  if (!user) {
    redirect('/login');
  }

  // Pagination parameters
  const page = Number(params.page) || 1;
  const limit = Number(params.limit) || 10;
  const skip = (page - 1) * limit;

  // Mendapatkan access scope berdasarkan role user
  const { whereClause } = getUserAccessScope(user);

  // Get total count for pagination
  const totalCount = await prisma.pelatihan.count({
    where: whereClause
  });

  const totalPages = Math.ceil(totalCount / limit);
  // Optimalisasi query dengan select kolom yang diperlukan saja
  // dan dengan mengambil data jenjangTargets terpisah untuk mengurangi kompleksitas query
  const pelatihan = await prisma.pelatihan.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
    select: {
      id: true,
      nama: true,
      tempat: true, 
      tgl_mulai: true,
      tgl_berakhir: true,
      link_registrasi: true,
      link_absensi: true,
      peserta_kegiatan: true,
      link_absensi_internal: true,
      link_absensi_eksternal: true,
      _count: {
        select: {
          biodata: true,
          absensi: true,
        },
      },
      // Ambil jenjangTargets dengan data minimal
      jenjangTargets: {
        select: {
          id: true,
          jenjang: true,
          target_peserta: true,
        },
      },
      // Ambil data biodata yang minimal untuk keperluan agregasi per jenjang
      biodata: {
        select: {
          id: true,
          jenjang: true,
        },
      },
      // Ambil data absensi yang minimal untuk keperluan agregasi
      absensi: {
        select: {
          id: true,
          nama: true,
          jenjang: true,
        },
        distinct: ['nama'],
      },
    },
  });

  return (
    <div className="max-w-full px-4 py-4 mx-auto sm:px-6">
      <div className='flex flex-col items-start justify-between gap-4 mb-6 sm:flex-row sm:items-center'>
        <h1 className='text-xl font-semibold sm:text-2xl'>Daftar Kegiatan</h1>
        <Link
          href='/dashboard/pelatihan/add'
          className='w-full px-4 py-2 text-center text-white transition-colors duration-200 bg-blue-600 rounded-md sm:w-auto hover:bg-blue-700'
        >
          Tambah Kegiatan
        </Link>
      </div>

      <div className='overflow-hidden bg-white rounded-lg shadow'>
        {/* Mobile view: Cards */}
        <div className='block sm:hidden'>
          {pelatihan.length === 0 ? (
            <div className='p-6 text-sm text-center text-gray-500'>
              Belum ada data kegiatan
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {pelatihan.map((item) => (
                <PelatihanCard key={item.id} pelatihan={item} />
              ))}
            </div>
          )}
        </div>
        
        {/* Desktop view: Table */}
        <div className='hidden sm:block'>
          <PelatihanTable pelatihan={pelatihan} />
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-4 py-4 border-t border-gray-200">
            <Pagination
              currentPage={page}
              totalPages={totalPages}
              limit={limit}
              searchParams={params}
              baseUrl="/dashboard/pelatihan"
            />
          </div>
        )}
      </div>
    </div>
  );
}
