/**
 * Console Interceptor - Redirects console.error to the logging system
 * DINONAKTIFKAN - tidak lagi melakukan logging ke database
 */
// import { logger } from './logger';

/**
 * Setup console interceptor to redirect console.error calls to the logging system
 * DINONAKTIFKAN - hanya melewati console logs biasa
 */
export function setupConsoleInterceptor(): void {
  // Fungsi dinonaktifkan - tidak melakukan override console methods
  return;
}

/**
 * Helper to safely catch and log errors
 * DINONAKTIFKAN - hanya menjalankan fungsi tanpa logging
 */
export function withErrorLogging<T extends (...args: any[]) => any>(
  fn: T,
  errorMessage: string = 'Error in function execution'
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  return (...args: Parameters<T>): ReturnType<T> | undefined => {
    try {
      return fn(...args);
    } catch (error) {
      // Error logging dinonaktifkan
      console.error(errorMessage, error);
      return undefined;
    }
  };
}

/**
 * Async version of withErrorLogging
 * DINONAKTIFKAN - hanya menjalankan fungsi tanpa logging
 */
export async function withAsyncErrorLogging<T>(
  fn: () => Promise<T>,
  errorMessage: string = 'Error in async function execution'
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    // Error logging dinonaktifkan
    console.error(errorMessage, error);
    return undefined;
  }
}