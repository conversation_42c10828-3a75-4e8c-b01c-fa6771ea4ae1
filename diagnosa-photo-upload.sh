#!/bin/bash

# Script untuk cek dan perbaiki masalah photo upload di VPS
# Jalankan script ini di VPS untuk diagnosa dan perbaikan

set -e

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}"
echo "============================================"
echo "   DIAGNOSA MASALAH PHOTO UPLOAD"
echo "============================================"
echo -e "${NC}"

# Auto-detect aplikasi Next.js
log_info "Mencari aplikasi Next.js di VPS..."

POSSIBLE_APP_DIRS=()

# Cari berdasarkan server.js (Next.js standalone)
while IFS= read -r -d '' app_path; do
    app_dir=$(dirname "$app_path")
    POSSIBLE_APP_DIRS+=("$app_dir")
    log_success "Ditemukan Next.js app: $app_dir"
done < <(find /home /var/www /opt /usr/local -name "server.js" -path "*/standalone/*" -print0 2>/dev/null || true)

# Cari berdasarkan package.json dengan next
while IFS= read -r -d '' package_path; do
    if grep -q '"next"' "$package_path" 2>/dev/null; then
        app_dir=$(dirname "$package_path")
        if [[ ! " ${POSSIBLE_APP_DIRS[@]} " =~ " ${app_dir} " ]]; then
            POSSIBLE_APP_DIRS+=("$app_dir")
            log_success "Ditemukan Next.js project: $app_dir"
        fi
    fi
done < <(find /home /var/www /opt /usr/local -name "package.json" -print0 2>/dev/null || true)

# Jika tidak ada yang ditemukan, gunakan default
if [ ${#POSSIBLE_APP_DIRS[@]} -eq 0 ]; then
    log_warning "Tidak ditemukan aplikasi Next.js otomatis, menggunakan default paths..."
    POSSIBLE_APP_DIRS=("/var/www/html" "/home/<USER>/app" "/opt/app")
fi

# Gunakan app directory pertama yang ditemukan
APP_DIR="${POSSIBLE_APP_DIRS[0]}"
log_info "Menggunakan app directory: $APP_DIR"

SAMPLE_PHOTO="absensi-2025-06-07T08-48-47-545Z-db7abc29-1921-46dd-8fa8-d1a6f83ec1a0.jpg"
SAMPLE_PELATIHAN_ID="915b8bfd-14ad-48d0-805f-6509f9540dd3"

log_info "Melakukan diagnosa struktur folder..."

# 1. Cek struktur folder yang ada
echo ""
log_info "=== CEK STRUKTUR FOLDER ===="

if [ -d "$APP_DIR/public" ]; then
    log_success "Folder $APP_DIR/public ditemukan"
    
    # Cek apakah ada folder public/public (duplikasi)
    if [ -d "$APP_DIR/public/public" ]; then
        log_warning "MASALAH DITEMUKAN: Folder duplikasi public/public/ ada"
        echo "   Path: $APP_DIR/public/public/"
        
        # Cek apakah foto ada di folder duplikasi
        if [ -d "$APP_DIR/public/public/uploads/absensi/photos" ]; then
            log_warning "Foto kemungkinan tersimpan di: public/public/uploads/"
            echo "   Struktur: $APP_DIR/public/public/uploads/absensi/photos/"
        fi
    fi
    
    # Cek struktur normal
    if [ -d "$APP_DIR/public/uploads/absensi/photos" ]; then
        log_info "Folder uploads normal ditemukan: public/uploads/absensi/photos/"
    else
        log_error "Folder public/uploads/absensi/photos/ TIDAK ditemukan"
    fi
    
else
    log_error "Folder $APP_DIR/public TIDAK ditemukan"
    echo "   Pastikan APP_DIR sudah benar di script ini"
fi

echo ""
log_info "=== MENCARI FILE FOTO SAMPLE ===="

# Cari file foto di berbagai lokasi
SEARCH_RESULTS=$(find "$APP_DIR" -name "$SAMPLE_PHOTO" 2>/dev/null || true)

if [ -n "$SEARCH_RESULTS" ]; then
    log_success "File foto ditemukan di:"
    echo "$SEARCH_RESULTS" | while read -r line; do
        echo "   - $line"
        
        # Cek apakah di lokasi duplikasi
        if [[ "$line" == *"public/public/uploads"* ]]; then
            log_warning "File ada di lokasi duplikasi!"
        fi
    done
else
    log_error "File foto $SAMPLE_PHOTO TIDAK ditemukan"
    echo "   Kemungkinan:"
    echo "   1. File belum diupload"
    echo "   2. Nama file berbeda"
    echo "   3. Lokasi penyimpanan salah"
fi

echo ""
log_info "=== CEK FOLDER UPLOAD LENGKAP ===="

# Tampilkan semua folder uploads yang ada
echo "Semua folder uploads yang ditemukan:"
find "$APP_DIR" -type d -name "uploads" 2>/dev/null | while read -r upload_dir; do
    echo "   - $upload_dir"
    
    # Cek isi folder uploads
    if [ -d "$upload_dir/absensi/photos" ]; then
        file_count=$(find "$upload_dir/absensi/photos" -name "*.jpg" 2>/dev/null | wc -l || echo "0")
        echo "     └── absensi/photos/ ($file_count foto)"
    fi
done

echo ""
log_info "=== CEK KONFIGURASI NGINX ===="

# Cek konfigurasi nginx
if [ -f "/etc/nginx/sites-available/default" ] || [ -f "/etc/nginx/sites-available/kegiatan.bpmpkaltim.id" ]; then
    log_info "Mencari konfigurasi nginx untuk static files..."
    
    # Cari konfigurasi location untuk uploads
    NGINX_CONFIG=$(grep -r "location.*uploads" /etc/nginx/sites-* 2>/dev/null || true)
    
    if [ -n "$NGINX_CONFIG" ]; then
        log_success "Konfigurasi nginx untuk uploads ditemukan:"
        echo "$NGINX_CONFIG"
    else
        log_warning "Konfigurasi nginx untuk uploads TIDAK ditemukan"
        echo "   Nginx mungkin tidak dikonfigurasi untuk serve static files"
    fi
    
    # Cek root directory nginx
    ROOT_CONFIG=$(grep -r "root.*" /etc/nginx/sites-* 2>/dev/null | grep -v "#" | head -5 || true)
    if [ -n "$ROOT_CONFIG" ]; then
        log_info "Root directory nginx:"
        echo "$ROOT_CONFIG"
    fi
else
    log_error "File konfigurasi nginx tidak ditemukan"
fi

echo ""
log_info "=== SOLUSI YANG DISARANKAN ===="

echo "Berdasarkan hasil diagnosa:"
echo ""

# Tentukan solusi berdasarkan temuan
if [ -d "$APP_DIR/public/public/uploads" ]; then
    log_warning "SOLUSI 1: Pindahkan file dari duplikasi folder"
    echo "   mv $APP_DIR/public/public/uploads/* $APP_DIR/public/uploads/"
    echo "   rmdir $APP_DIR/public/public/uploads"
    echo "   rmdir $APP_DIR/public/public"
    echo ""
fi

log_info "SOLUSI 2: Update konfigurasi nginx"
echo '   location /uploads/ {'
echo "       root $APP_DIR/public;"
echo '       expires 1y;'
echo '       add_header Cache-Control "public, immutable";'
echo '   }'
echo ""

log_info "SOLUSI 3: Test akses file"
echo "   curl -I https://kegiatan.bpmpkaltim.id/uploads/absensi/photos/$SAMPLE_PELATIHAN_ID/$SAMPLE_PHOTO"
echo ""

# AUTO-FIX OPTION
echo ""
log_warning "=== PERBAIKAN OTOMATIS ==="
echo ""
read -p "Apakah Anda ingin menjalankan perbaikan otomatis? (y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    log_info "Memulai perbaikan otomatis..."
    
    # 1. Buat struktur folder jika belum ada
    if [ ! -d "$APP_DIR/public/uploads/absensi/photos" ]; then
        log_info "Membuat folder uploads..."
        mkdir -p "$APP_DIR/public/uploads/absensi/photos"
        log_success "Folder uploads dibuat"
    fi
    
    # 2. Perbaiki duplikasi jika ada
    if [ -d "$APP_DIR/public/public" ]; then
        log_info "Memperbaiki duplikasi folder..."
        if [ -d "$APP_DIR/public/public/uploads" ]; then
            cp -r "$APP_DIR/public/public/uploads/"* "$APP_DIR/public/uploads/" 2>/dev/null || true
        fi
        rm -rf "$APP_DIR/public/public"
        log_success "Duplikasi folder diperbaiki"
    fi
    
    # 3. Set permissions
    log_info "Mengatur permissions..."
    chown -R www-data:www-data "$APP_DIR/public/uploads" 2>/dev/null || true
    chmod -R 755 "$APP_DIR/public/uploads" 2>/dev/null || true
    log_success "Permissions diatur"
    
    # 4. Update nginx config
    log_info "Mengupdate nginx config..."
    NGINX_CONFIG_FILE="/etc/nginx/sites-available/kegiatan.bpmpkaltim.id"
    
    if [ ! -f "$NGINX_CONFIG_FILE" ]; then
        NGINX_CONFIG_FILE="/etc/nginx/sites-available/default"
    fi
    
    if [ -f "$NGINX_CONFIG_FILE" ]; then
        # Backup config
        cp "$NGINX_CONFIG_FILE" "$NGINX_CONFIG_FILE.backup.$(date +%Y%m%d-%H%M%S)"
        
        # Add uploads location if not exists
        if ! grep -q "location /uploads/" "$NGINX_CONFIG_FILE"; then
            sed -i '/location \/ {/i\
    # Static files untuk uploads\
    location /uploads/ {\
        root '"$APP_DIR"'/public;\
        expires 1y;\
        add_header Cache-Control "public, immutable";\
        try_files $uri $uri/ =404;\
    }\
' "$NGINX_CONFIG_FILE"
            log_success "Nginx config diupdate"
        else
            log_info "Nginx config sudah ada"
        fi
        
        # Test dan reload nginx
        if nginx -t 2>/dev/null; then
            systemctl reload nginx 2>/dev/null || service nginx reload 2>/dev/null
            log_success "Nginx direload"
        else
            log_error "Nginx config error"
        fi
    else
        log_error "Nginx config file tidak ditemukan"
    fi
    
    echo ""
    log_success "PERBAIKAN OTOMATIS SELESAI!"
    echo ""
    
    # Test akses
    log_info "Testing akses..."
    TEST_URL="https://kegiatan.bpmpkaltim.id/uploads/"
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" --max-time 10 2>/dev/null || echo "000")
    
    if [ "$HTTP_STATUS" = "404" ]; then
        log_success "Test OK - Server responding (404 normal untuk empty folder)"
    elif [ "$HTTP_STATUS" = "403" ]; then
        log_success "Test OK - Server responding (403 = folder accessible)"
    elif [ "$HTTP_STATUS" = "000" ]; then
        log_error "Connection failed"
    else
        log_info "Status: $HTTP_STATUS"
    fi
    
    echo ""
    log_info "LANGKAH SELANJUTNYA:"
    echo "1. Test upload foto baru di aplikasi"
    echo "2. Cek apakah foto muncul tanpa 404"
    echo "3. Monitor: tail -f /var/log/nginx/error.log"
    
else
    echo ""
    log_info "Perbaikan manual dipilih"
fi

echo -e "${GREEN}"
echo "============================================"
echo "   DIAGNOSA SELESAI"
echo "============================================"
echo -e "${NC}"
echo ""
echo "Untuk menjalankan perbaikan otomatis, gunakan script:"
echo "   fix-photo-upload-vps.sh"
