# 🚀 DEPLOY NOW - Photo Upload Fix

## Status: ✅ ALL FILES READY

Your deployment package is ready:
- **deployment.tar.gz**: 34.9 MB (main application)
- **fix-photo-upload-vps.sh**: VPS configuration script
- **nginx-config.txt**: Nginx configuration

## 📋 DEPLOYMENT COMMANDS

### 1. Upload Files to VPS (run from Windows)
```cmd
cd f:\online\zoom_rutin

# Upload deployment archive
scp .next\deployment.tar.gz <EMAIL>:/home/<USER>/

# Upload fix script
scp fix-photo-upload-vps.sh <EMAIL>:/home/<USER>/

# Upload nginx config
scp nginx-config.txt <EMAIL>:/home/<USER>/
```

### 2. SSH to VPS and Deploy
```bash
# Connect to VPS
ssh <EMAIL>

# Create deployment directory
mkdir -p /home/<USER>/deployment
cd /home/<USER>/deployment

# Extract deployment
tar -xzf ../deployment.tar.gz

# Make script executable
chmod +x /home/<USER>/fix-photo-upload-vps.sh

# Run the fix (this will take 5-10 minutes)
sudo /home/<USER>/fix-photo-upload-vps.sh
```

### 3. Restart Services
```bash
# Restart PM2 app
pm2 restart all

# Restart Nginx
sudo systemctl restart nginx
```

## 🧪 Test the Fix

1. Go to: https://kegiatan.bpmpkaltim.id/public-pages/absensi/internal/[your-link]
2. Fill attendance form and upload photo
3. Check admin panel → Absensi → Detail
4. **Result**: Photos should display correctly!

## 🆘 If You Need Help

If you encounter any issues:
1. Check the logs: `pm2 logs`
2. Check nginx: `sudo nginx -t`
3. Verify permissions: `ls -la public/uploads/`

---
**Time to complete**: 15-20 minutes  
**Risk level**: Low (script includes backups)
