import { formatIndonesiaDate, formatIndonesiaDateTime } from './dateUtils';

// Define question types explicitly based on your schema.prisma
type QuestionType = 
  | 'SHORT_TEXT'
  | 'LONG_TEXT'
  | 'SINGLE_CHOICE'
  | 'MULTIPLE_CHOICE'
  | 'DROPDOWN'
  | 'DATE'
  | 'TIME'
  | 'FILE_UPLOAD'
  | 'SCALE'
  | 'EMAIL'
  | 'PHONE'
  | 'NUMBER';

// Define our own types based on the schema
type FormResponse = {
  id: string;
  formId: string;
  respondentId?: string | null;
  respondentName?: string | null;
  respondentEmail?: string | null;
  respondentInfo?: any;
  submittedAt: Date;
  answers: Answer[];
};

type Answer = {
  id: string;
  submissionId: string;
  questionId: string;
  answerValue?: string | null;
  answerJson?: any;
  fileUrl?: string | null;
  question?: Question;
};

type Question = {
  id: string;
  formId: string;
  title?: string;
  questionText: string;
  questionType: QuestionType;
  isRequired: boolean;
  order: number;
  options?: any;
};

type Form = {
  id: string;
  title: string;
  description?: string | null;
  userId: string;
  questions: Question[];
};

/**
 * Mengubah respons formulir menjadi format yang mudah dibaca
 */
export function formatFormResponseForDisplay(
  response: FormResponse & { 
    answers: Array<Answer & { 
      question: Question 
    }> 
  }
): Record<string, any> {
  const formattedResponse: Record<string, any> = {
    id: response.id,
    respondentName: response.respondentName || 'Anonymous',
    respondentEmail: response.respondentEmail || '-',
    submittedAt: formatDateTime(response.submittedAt),
    answers: {}
  };

  // Format setiap jawaban
  response.answers.forEach(answer => {
    formattedResponse.answers[answer.questionId] = {
      question: answer.question?.questionText || 'Unknown Question',
      value: formatAnswerByType(answer.answerValue ?? null, answer.question?.questionType || 'SHORT_TEXT'),
      rawValue: answer.answerValue,
      type: answer.question?.questionType || 'SHORT_TEXT'
    };
  });

  return formattedResponse;
}

/**
 * Format nilai jawaban berdasarkan tipe pertanyaan
 */
function formatAnswerByType(value: string | null, type: QuestionType): string | string[] | null {
  if (!value) return null;

  switch (type) {
    case 'MULTIPLE_CHOICE':
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    case 'DATE':
      try {
        return formatIndonesiaDate(new Date(value));
      } catch {
        return value;
      }
    case 'FILE_UPLOAD':
      try {
        const fileData = JSON.parse(value);
        return fileData.fileName || fileData.url || value;
      } catch {
        return value;
      }
    default:
      return value;
  }
}

/**
 * Menghasilkan statistik dasar dari respons formulir
 */
export function generateFormResponseStats(
  form: Form & { questions: Question[] },
  responses: Array<FormResponse & { answers: Answer[] }>
): Record<string, any> {
  const stats: Record<string, any> = {
    totalResponses: responses.length,
    completionRate: 0,
    questionStats: {}
  };

  if (responses.length === 0) {
    return stats;
  }

  // Hitung statistik untuk setiap pertanyaan
  form.questions.forEach(question => {
    const answers = responses.flatMap(r => 
      r.answers.filter(a => a.questionId === question.id)
    );
    
    const answerCount = answers.length;
    const completionRate = (answerCount / responses.length) * 100;
    
    const questionStat: Record<string, any> = {
      title: question.questionText,
      type: question.questionType,
      answerCount,
      completionRate: Math.round(completionRate)
    };
    
    // Tambahkan statistik khusus berdasarkan tipe pertanyaan
    switch (question.questionType) {
      case 'SINGLE_CHOICE':
      case 'MULTIPLE_CHOICE':
      case 'DROPDOWN':
        const optionCounts: Record<string, number> = {};
        
        answers.forEach(answer => {
          if (!answer.answerValue) return;
          
          const values = question.questionType === 'MULTIPLE_CHOICE' 
            ? JSON.parse(answer.answerValue || '[]') 
            : [answer.answerValue];
            
          values.forEach((val: string) => {
            optionCounts[val] = (optionCounts[val] || 0) + 1;
          });
        });
        
        questionStat.optionCounts = optionCounts;
        break;
        
      case 'SCALE':
        if (answers.length > 0) {
          const values = answers
            .filter(a => a.answerValue)
            .map(a => parseInt(a.answerValue || '0'));
            
          questionStat.average = values.reduce((sum, val) => sum + val, 0) / values.length;
          questionStat.min = Math.min(...values);
          questionStat.max = Math.max(...values);
        }
        break;
    }
    
    stats.questionStats[question.id] = questionStat;
  });
  
  // Hitung tingkat penyelesaian keseluruhan
  const totalPossibleAnswers = form.questions.length * responses.length;
  const totalActualAnswers = responses.reduce(
    (sum, response) => sum + response.answers.length, 
    0
  );
  
  stats.completionRate = Math.round((totalActualAnswers / totalPossibleAnswers) * 100);
  
  return stats;
}

/**
 * Ekspor respons formulir ke format CSV
 */
export function exportResponsesToCsv(
  form: Form & { questions: Question[] },
  responses: Array<FormResponse & { answers: Answer[] }>
): string {
  // Buat header CSV
  const headers = [
    'Respondent Name',
    'Email',
    'Submitted At',
    ...form.questions.map(q => q.questionText)
  ];
  
  let csv = headers.map(h => `"${h.replace(/"/g, '""')}"`).join(',') + '\n';
  
  // Tambahkan baris data
  responses.forEach(response => {
    const row = [
      response.respondentName || 'Anonymous',
      response.respondentEmail || '',
      formatDateTime(response.submittedAt)
    ];
    
    // Tambahkan jawaban untuk setiap pertanyaan
    form.questions.forEach(question => {
      const answer = response.answers.find(a => a.questionId === question.id);
      
      if (!answer || !answer.answerValue) {
        row.push('');
        return;
      }
      
      let formattedValue = answer.answerValue;
      
      // Format nilai berdasarkan tipe pertanyaan
      switch (question.questionType) {
        case 'MULTIPLE_CHOICE':
          try {
            const values = JSON.parse(answer.answerValue);
            formattedValue = Array.isArray(values) ? values.join('; ') : answer.answerValue;
          } catch {
            // Gunakan nilai asli jika parsing gagal
          }
          break;
          
        case 'DATE':
          try {
            formattedValue = formatIndonesiaDate(new Date(answer.answerValue));
          } catch {
            // Gunakan nilai asli jika parsing gagal
          }
          break;
      }
      
      // Escape quotes untuk CSV
      row.push(`"${formattedValue.toString().replace(/"/g, '""')}"`);
    });
    
    csv += row.join(',') + '\n';
  });
  
  return csv;
}

/**
 * Mengekspor respons formulir ke format JSON
 */
export function exportResponsesToJson(
  form: Form & { questions: Question[] },
  responses: Array<FormResponse & { 
    answers: Array<Answer & { 
      question: Question 
    }> 
  }>
): string {
  const formattedResponses = responses.map(response => {
    const formattedResponse: Record<string, any> = {
      id: response.id,
      respondentName: response.respondentName || 'Anonymous',
      respondentEmail: response.respondentEmail || null,
      submittedAt: response.submittedAt.toISOString(),
      answers: {}
    };
    
    // Format jawaban
    response.answers.forEach(answer => {
      // Make sure question exists before accessing its properties
      if (!answer.question) return;
      
      let value = answer.answerValue;
      
      // Parse nilai berdasarkan tipe
      if (value) {
        switch (answer.question.questionType) {
          case 'MULTIPLE_CHOICE':
            try {
              value = JSON.parse(value);
            } catch {
              // Gunakan nilai asli jika parsing gagal
            }
            break;
            
          case 'NUMBER':
            try {
              const parsedValue = parseFloat(value);
              // Store the parsed number as a string to match expected type
              value = isNaN(parsedValue) ? value : String(parsedValue);
            } catch {
              // Gunakan nilai asli jika parsing gagal
            }
            break;
        }
      }
      
      formattedResponse.answers[answer.question.questionText] = value;
    });
    
    return formattedResponse;
  });
  
  return JSON.stringify(formattedResponses, null, 2);
}
/**
 * Format a Date object to a readable datetime string
 */
function formatDateTime(date: Date): string {
  try {
    return formatIndonesiaDateTime(date);
  } catch (_error) {
    // Fallback if locale formatting fails
    return date.toISOString().replace('T', ' ').substring(0, 16);
  }
}

