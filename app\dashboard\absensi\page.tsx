import { redirect } from 'next/navigation';
import { getCurrentUser } from '../../../lib/auth';
import { prisma } from '../../../lib/prisma';
import { Metadata } from 'next';
import AbsensiExport from '../../../components/AbsensiExport';
import { Suspense } from 'react';
import Pagination from '@/components/Pagination';
import { getFilteredAbsensi } from '@/utils/absensiHelpers';
import Card from '@/components/Card';
import Button from '@/components/Button';
import FilterBar from '@/components/FilterBar';

export const metadata: Metadata = {
  title: 'Manajemen Absensi',
  description: 'Manajemen data absensi peserta kegiatan'
};

export default async function AbsensiPage(
  props: {
    searchParams: Promise<{ page?: string; limit?: string; pelatihan?: string; date?: string; search?: string }>;
  }
) {
  try {
    const searchParams = await props.searchParams;
    const user = await getCurrentUser();

    if (!user) {
      redirect('/login');
    }

    // Pagination parameters
    const page = Number(searchParams.page) || 1;
    const limit = Number(searchParams.limit) || 20;
    
    // Filter parameters
    const pelatihanId = searchParams.pelatihan;
    const date = searchParams.date;
    // Pastikan date dikirim ke getFilteredAbsensi
    const { absensi, totalPages } = await getFilteredAbsensi(
      prisma,
      user.id,
      page,
      limit,
      pelatihanId,
      date
    );

    // Prepare filter options
    interface PelatihanOption {
      id: string;
      nama: string;
    }
    
    let pelatihanOptions: PelatihanOption[] = [];
    if (["ADMIN", "KEPALA", "KASUBAG"].includes(user.role)) {
      pelatihanOptions = await prisma.pelatihan.findMany({
        select: { id: true, nama: true },
        orderBy: { nama: 'asc' }
      });
    } else if (["GM1", "GM2", "GM3", "GM4", "GM5"].includes(user.role)) {
      pelatihanOptions = await prisma.pelatihan.findMany({
        where: { userId: user.id },
        select: { id: true, nama: true },
        orderBy: { nama: 'asc' }
      });
    } else {
      pelatihanOptions = [];
    }

    // Ambil nilai filter dari searchParams
    const filterParams = {
      pelatihan: pelatihanId || '',
      date: date || '',
    };

    return (
      <div className="container px-4 mx-auto sm:px-6 lg:px-8">
        <div className="flex flex-col items-start justify-between gap-4 my-4 sm:my-6 sm:flex-row sm:items-center">
          <h1 className="text-xl font-semibold sm:text-2xl">Daftar Absensi</h1>
        </div>
        <FilterBar
          filters={[
            {
              name: 'pelatihan',
              label: 'Kegiatan',
              type: 'select',
              options: pelatihanOptions.map(p => ({ id: p.id, label: p.nama, value: p.id }))
            },
            {
              name: 'date',
              label: 'Tanggal',
              type: 'date'
            }
          ]}
          baseUrl="/dashboard/absensi"
          currentParams={filterParams}
        />
        <Suspense fallback={
          <div className="p-8 text-center">
            <div className="inline-block w-8 h-8 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
            <p className="mt-2 text-gray-600">Memuat data...</p>
          </div>
        }>
          <Card>
            <div className="w-full overflow-x-auto">
              <AbsensiExport 
                absensiList={absensi}
                filterPelatihan={pelatihanId || ''}
                filterDate={date || ''}
                enableDelete
              />
            </div>
            
            {/* Pagination akan ditampilkan hanya jika ada lebih dari 1 halaman */}
            {totalPages > 0 ? (
              <div className="mt-4">
                <Pagination
                  currentPage={page}
                  totalPages={totalPages}
                  limit={limit}
                  searchParams={searchParams}
                  baseUrl="/dashboard/absensi"
                />
              </div>
            ) : (
              <div className="py-4 text-center text-gray-500">
                {absensi.length === 0 ? 'Tidak ada data absensi yang ditemukan.' : ''}
              </div>
            )}
          </Card>
        </Suspense>
      </div>
    );
  } catch (_error) {
    // Remove console.error
    return (
      <div className="container px-4 mx-auto sm:px-6 lg:px-8">
        <Card>
          <div className="p-4 my-4 text-red-700 bg-red-100 rounded-md">
            <p className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Terjadi kesalahan saat mengambil data. Silakan coba lagi nanti.
            </p>
          </div>
          <div className="mt-4 text-center">
            <Button 
              onClick={() => window.location.reload()}
              variant="primary"
              icon={
                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              }
            >
              Muat Ulang
            </Button>
          </div>
        </Card>
      </div>
    );
  }
}
